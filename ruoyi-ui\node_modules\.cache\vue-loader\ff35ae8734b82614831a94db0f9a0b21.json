{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorWeb.vue?vue&type=style&index=1&id=526ab65b&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorWeb.vue", "mtime": 1755768894571}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5wb2xpY3lDb2wgewogIG1pbi13aWR0aDogMzMwcHg7CiAgbWFyZ2luLXRvcDogMTBweDsKfQoKLnBvbGljeURlc2MgewogIGRpc3BsYXk6IGZsZXg7CiAgaGVpZ2h0OiA4MHB4Owp9CgoucG9saWN5VHh0IHsKICBtYXJnaW4tbGVmdDogMTBweDsKICBsaW5lLWhlaWdodDogMjBweDsKfQoKLnBvbGljeVRpdGxlIHsKICBoZWlnaHQ6IDQwcHg7CiAgbGluZS1oZWlnaHQ6IDQwcHg7Cn0KCi5vbmVMaW5lIHsKICBvdmVyZmxvdzogaGlkZGVuOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7Cn0KCjo6di1kZWVwIC5lbC10YWJsZSB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwp9Cgo6OnYtZGVlcCAuZWwtdGFibGVfX2JvZHktd3JhcHBlciB7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBmbGV4OiAxOwp9Cg=="}, {"version": 3, "sources": ["monitorWeb.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4oBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "monitorWeb.vue", "sourceRoot": "src/views/frailty/monitor", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务名称\" prop=\"jobName\">\n                <el-input\n                  v-model=\"queryParams.jobName\"\n                  placeholder=\"请输入任务名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"扫描目标\" prop=\"invokeTarget\">\n                <el-input\n                  v-model=\"queryParams.invokeTarget\"\n                  placeholder=\"扫描目标\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务状态\" prop=\"status\">\n                <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.sys_job_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <!--<el-col :span=\"6\">-->\n              <!--<el-form-item label=\"执行策略\" prop=\"jobType\">-->\n                <!--<el-select v-model=\"queryParams.jobType\" placeholder=\"请选择执行策略\" clearable>-->\n                  <!--<el-option label=\"资产扫描监控\" :value=\"0\"/>-->\n                  <!--<el-option label=\"基础服务漏洞扫描\" :value=\"1\"/>-->\n                  <!--<el-option label=\"基础Web漏洞扫描\" :value=\"2\"/>-->\n                  <!--<el-option label=\"主机资产探测\" :value=\"3\"/>-->\n                <!--</el-select>-->\n              <!--</el-form-item>-->\n            <!--</el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">Web漏扫任务列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleScan\"\n                  v-hasPermi=\"['monitor:webschedule:add']\"\n                >新增\n                </el-button>\n              </el-col>\n<!--              <el-col :span=\"1.5\">-->\n<!--                <el-button-->\n<!--                  class=\"btn1\"-->\n<!--                  size=\"small\"-->\n<!--                  :disabled=\"single\"-->\n<!--                  @click=\"handleUpdate\"-->\n<!--                  v-hasPermi=\"['monitor:schedule:edit']\"-->\n<!--                >修改-->\n<!--                </el-button>-->\n<!--              </el-col>-->\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['monitor:webschedule:remove']\"\n                >批量删除\n                </el-button>\n              </el-col>\n              <!--            <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          :data=\"jobList\"\n          ref=\"multipleTable\"\n          :row-key=\"getRowKey\"\n          @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <!--<el-table-column label=\"任务编号\" width=\"100\"  prop=\"jobId\"/>-->\n          <el-table-column label=\"任务名称\"  prop=\"jobName\" :show-overflow-tooltip=\"true\"/>\n          <!--<el-table-column label=\"执行策略\"  prop=\"jobGroup\">-->\n            <!--<template slot-scope=\"scope\">-->\n              <!--<span v-if=\"scope.row.jobType === 0\">资产扫描监控</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 1\">基础服务漏洞扫描</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 2\">基础Web漏洞扫描</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 3\">主机资产探测</span>-->\n            <!--</template>-->\n          <!--</el-table-column>-->\n          <el-table-column label=\"扫描目标\"  prop=\"ipShow\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <el-tooltip v-if=\"scope.row.jobType !== 3\" class=\"item\" placement=\"top\">\n                <div v-html=\"scope.row.ipOver\" slot=\"content\"></div>\n                <div class=\"oneLine\">\n                  {{ scope.row.ipShow }}\n                </div>\n              </el-tooltip>\n              <span v-else>-</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"执行周期\"  prop=\"cronTransfer\"/>\n          <el-table-column label=\"最近一次执行时间\"  prop=\"lastRunTime\"/>\n          <el-table-column label=\"最近扫描状态\"  prop=\"jobGroup\">\n            <template slot-scope=\"scope\">\n              <el-tag type=\"danger\" v-if=\"scope.row.currentStatus === 0\">未扫描</el-tag>\n              <el-tag v-else-if=\"scope.row.currentStatus === 1\">扫描中</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.currentStatus === 2\">已扫描</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"任务状态\" >\n            <template slot-scope=\"scope\">\n              <el-switch\n                v-model=\"scope.row.status\"\n                active-value=\"0\"\n                inactive-value=\"1\"\n                @change=\"handleStatusChange(scope.row)\"\n                v-hasPermi=\"['monitor:webschedule:query']\"\n              ></el-switch>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"220\" fixed=\"right\" :show-overflow-tooltip=\"false\" class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleView(scope.row)\"\n              >详情\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.currentStatus === 1\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['monitor:webschedule:edit']\"\n              >编辑\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :class=\"{'table-delBtn':scope.row.currentStatus !== 1}\"\n                :disabled=\"scope.row.currentStatus === 1\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['monitor:webschedule:remove']\"\n              >删除\n              </el-button>\n              <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\"\n                           v-hasPermi=\"['monitor:webschedule:query']\">\n            <span class=\"el-dropdown-link\">\n              <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\n            </span>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item :disabled=\"scope.row.status === '1' || scope.row.currentStatus === 1\"\n                                    command=\"handleRun\" icon=\"el-icon-caret-right\">执行一次\n                  </el-dropdown-item>\n                  <!--<el-dropdown-item command=\"handleView\" icon=\"el-icon-view\">任务详细</el-dropdown-item>-->\n                  <el-dropdown-item command=\"handleJobLog\" icon=\"el-icon-s-operation\">调度日志</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <!-- 添加或修改定时任务对话框 -->\n    <LeakScanDialog\n      :title=\"title\"\n      :edit-form=\"editForm\"\n      :edit-title=\"editTitle\"\n      :is-disable.sync=\"invokeDisable\"\n      @getList=\"getList\"\n      :scan-strategy-visible.sync=\"scanStrategyVisible\"/>\n\n\n    <!-- 任务日志详细 -->\n    <el-dialog title=\"任务详细\" v-if=\"openView\" :visible.sync=\"openView\" v-dialog-drag width=\"1200px\" append-to-body>\n      <ff-job-tasks v-if=\"openView\" :jobId=\"jobId\" :job-type=\"jobType\" :job-row=\"editForm\" />\n    </el-dialog>\n\n    <!-- 调度日志 -->\n    <el-dialog title=\"调度日志\" v-if=\"openLogView\" :visible.sync=\"openLogView\" v-dialog-drag width=\"1200px\" append-to-body>\n      <job-log v-if=\"openLogView\" :jobId=\"logJobId\"></job-log>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"openLogView = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus} from \"@/api/safe/monitor\";\nimport QuestResultDetails from '../../safe/server/questResultDetails'\nimport LeakScanDialog from '../../safe/server/components/LeakScanDialog'\nimport FfJobTasks from './ffJobTasks'\nimport JobLog from '../../monitor/job/log'\n\nexport default {\n  name: \"Job\",\n  components: { JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },\n  dicts: ['sys_job_group', 'sys_job_status'],\n  props: {\n    toParams: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      jobType: undefined,\n      // 是否显示Cron表达式弹出层\n      openCron: false,\n      // 展示最近一次运行结果\n      openSelect: false,\n      // 遮罩层\n      loading: true,\n      // 任务ID\n      jobId: '',\n      logJobId: null,\n      totalScan: 0,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 定时任务表格数据\n      jobList: [],\n      // 弹出层标题\n      title: \"\",\n      editTitle: '',\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详细弹出层\n      openView: false,\n      openLogView: false,\n      scanStrategyVisible:  false,\n      editForm: {},\n      rows: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        jobType: 2,\n        ipShow: '',\n        jobName: undefined,\n        jobGroup: 'ASSET_SCAN',\n        status: undefined\n      },\n      isDisabled: false,\n      invokeDisable: false,\n      // 周期转换文字\n      cronText: '',\n      form: {},\n      // 表单校验\n      rules: {\n        jobName: [\n          {required: true, message: \"任务名称不能为空\", trigger: \"blur\"}\n        ],\n        invokeIp: [\n          {required: true, message: \"扫描IP不能为空\", trigger: \"blur\"}\n        ],\n        cronExpression: [\n          {required: true, message: \"cron执行表达式不能为空\", trigger: \"blur\"}\n        ]\n      },\n      getListInterval: null,\n      selectedIds: []\n    };\n  },\n  created() {\n    this.getList()\n    this.getListInterval = setInterval(() => {\n      this.loopGetList()\n    }, 10000)\n  },\n  destroyed() {\n    if(this.getListInterval){\n      clearInterval(this.getListInterval);\n    }\n  },\n  watch: {\n    toParams: {\n      handler(newVal) {\n        if(newVal && newVal.id){\n          let curRow = {\n            jobId: newVal.id,\n            jobName: newVal.jobName,\n            cronTransfer: newVal.cronTransfer\n          };\n          if (newVal.invokeTarget) {\n            const target = newVal.invokeTarget\n            const start = target.indexOf('\\',\\'') + 3\n            const end = target.length - 2\n            const ips = target.substring(start, end)\n            const ipss = ips.split('|')\n            if (ipss.length > 1) {\n              curRow.ipShow = ipss[1].replaceAll(';', ' ')\n              curRow.ipOver = ipss[1].replaceAll(';', '<br>')\n            }\n          }\n          this.handleView(curRow);\n        }\n      },\n      immediate: true\n    }\n  },\n  // watch: {\n  //   'dict.type.sys_job_group': {\n  //     handler(newVal) {\n  //       if (newVal.length > 0) {\n  //         let tmp = newVal.filter(s => s.label === '资产扫描')\n  //         this.form.jobGroup = tmp.length > 0 ? tmp[0].value : undefined\n  //         this.queryParams.jobGroup = this.form.jobGroup\n  //       }\n  //     },\n  //     deep: true\n  //   }\n  // },\n  methods: {\n    /** 查询定时任务列表 */\n    getList() {\n      this.loading = true;\n      listJob(this.queryParams).then(response => {\n        response.rows.forEach(s => {\n          if (s.invokeTarget) {\n            const target = s.invokeTarget\n            const start = target.indexOf('\\',\\'') + 3\n            const end = target.length - 2\n            const ips = target.substring(start, end)\n            const ipss = ips.split('|')\n            if (ipss.length > 1) {\n              s.ipShow = ipss[1].replaceAll(';', ' ')\n              s.ipOver = ipss[1].replaceAll(';', '<br>')\n            }\n          }\n          if(s.period === 0){\n            s.status = s.currentStatus === 1 ? '0' : '1'\n          }\n        })\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    loopGetList() {\n      listJob(this.queryParams).then(response => {\n        response.rows.forEach(s => {\n          if (s.invokeTarget) {\n            const target = s.invokeTarget\n            const start = target.indexOf('\\',\\'') + 3\n            const end = target.length - 2\n            const ips = target.substring(start, end)\n            const ipss = ips.split('|')\n            if (ipss.length > 1) {\n              s.ipShow = ipss[1].replaceAll(';', ' ')\n              s.ipOver = ipss[1].replaceAll(';', '<br>')\n            }\n            if(s.period === 0){\n              s.status = s.currentStatus === 1 ? '0' : '1'\n            }\n          }\n        })\n        const newJobList = response.rows\n        const selectedIds = [...this.selectedIds]\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.$nextTick(() => {\n          const rowsToSelect = newJobList.filter(row =>\n            selectedIds.includes(row.jobId)\n          );\n          this.$refs.multipleTable.clearSelection();\n          rowsToSelect.forEach(row => {\n            this.$refs.multipleTable.toggleRowSelection(row, true);\n          })\n        })\n      });\n    },\n    handleScan() {\n      this.invokeDisable = false\n      this.title = '添加任务';\n      this.editTitle = 'Web漏洞扫描'\n      this.editForm = {}\n      this.editForm.jobType = 2;\n      this.editForm.speed = '2';\n      this.editForm.status = '0';\n      this.editForm.cronExpression= '* * * * * ?';\n      this.editForm.period= 0;\n      this.editForm.cronTransfer= '立即执行';\n      this.scanStrategyVisible = true;\n    },\n    // 任务组名字典翻译\n    jobGroupFormat(row, column) {\n      return this.selectDictLabel(this.dict.type.sys_job_group, row.jobGroup);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.invokeDisable = false\n      this.reset();\n    },\n    /** 确定后回传值 */\n    cronTabFill(val) {\n      this.form.cronExpression = val.cronText\n      this.form.period = val.period\n      this.form.cronTransfer = val.cronTransfer\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        jobId: undefined,\n        jobName: undefined,\n        invokeTarget: undefined,\n        cronExpression: undefined,\n        misfirePolicy: 0,\n        concurrent: 1,\n        period: 0,\n        jobType: 2,\n        status: \"0\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n\n    getRowKey(row) {\n      return row.jobId;\n    },\n\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.jobId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n      this.rows = selection;\n      this.selectedIds = [...this.ids]\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"lastResult\":\n          this.handleDetail(row);\n          break;\n        case \"handleRun\":\n          this.handleRun(row);\n          break;\n        case \"handleView\":\n          this.handleView(row);\n          break;\n        case \"handleJobLog\":\n          this.handleJobLog(row);\n          break;\n        default:\n          break;\n      }\n    },\n    // 任务状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.jobName + '\"任务吗？').then(function () {\n        return changeJobStatus(row.jobId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function () {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      }).finally(() => {\n        var _this = this\n        setTimeout(() => {\n          _this.getList();\n        }, 1000);\n      });\n    },\n    /* 最近执行结果 */\n    handleDetail(row) {\n      this.jobId = row.jobId\n    },\n    /* 立即执行一次 */\n    handleRun(row) {\n      this.$modal.confirm('确认要立即执行一次\"' + row.jobName + '\"任务吗？').then(function () {\n        return runJob(row.jobId, row.jobGroup);\n      }).then(() => {\n        this.$modal.msgSuccess(\"执行成功\");\n      }).catch(() => {\n      });\n    },\n    /** 任务详细信息 */\n    handleView(row) {\n      this.openView = true;\n      this.jobType = 1;\n      this.jobId = row.jobId;\n      this.editForm = row\n    },\n    editNow(jobId) {\n      let filter = this.jobList.filter(item => item.jobId == jobId)\n      if (filter.length === 0) {\n        this.$message.error('未找到任务数据！')\n        return\n      } else {\n        if (filter[0].currentStatus === 1) {\n          this.$message.error('当前任务状态为正在扫描中，请勿更改！')\n          return\n        }\n        this.openView = false\n        this.handleUpdate(filter[0])\n      }\n\n    },\n    executeNow() {\n      this.openView = false\n    },\n    /** 任务日志列表查询 */\n    handleJobLog(row) {\n      this.logJobId = row.jobId || 0;\n      this.openLogView = true\n      //this.$router.push({path: '/monitor/job-log/index', query: {jobId: jobId}})\n    },\n    handleSelect() {\n      this.openSelect = true\n    },\n    /** 新增按钮操作 */\n    handleAdd(val) {\n      this.reset();\n      switch (val) {\n        case 0:\n          this.editTitle = '资产扫描监控'\n          break\n        case 1:\n          this.editTitle = '基础服务漏洞扫描'\n          break\n        case 2:\n          this.editTitle = 'Web漏洞扫描'\n          break\n        case 3:\n          this.editTitle = '主机资产探测'\n          break\n        default:\n          break\n      }\n      this.openSelect = false\n      this.form.jobType = val\n      this.open = true;\n      this.title = \"添加任务\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      switch (row.jobType) {\n        case 0:\n          this.editTitle = '资产扫描监控'\n          break\n        case 1:\n          this.editTitle = '基础服务漏洞扫描'\n          break\n        default:\n          break\n      }\n      this.reset();\n      const jobId = row.jobId || this.ids;\n      this.invokeDisable = true\n      getJob(jobId).then(response => {\n        const target = response.data.invokeTarget\n        const start = target.indexOf('\\',\\'') + 3\n        const end = target.length - 2\n        const ips = target.substring(start, end)\n        const ipss = ips.split('|')\n        if (ipss.length > 1) {\n          response.data.invokeIp = ipss[1].replaceAll(';', '\\n')\n          response.data.speed = ipss[2]\n        }\n\n        this.editForm= response.data;\n        this.scanStrategyVisible = true;\n        this.title = \"修改任务\";\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.currentStatus === 1);\n      if (rows.length > 0) {\n        this.$message.error('选择中有扫描中任务，无法批量删除');\n        return false;\n      }\n      const jobIds = row.jobId || this.ids;\n      this.$modal.confirm('是否确认删除定时任务编号为【' + jobIds + '】的数据项？').then(function () {\n        return delJob(jobIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('monitor/schedule/export', {\n        ...this.queryParams\n      }, `job_${new Date().getTime()}.xlsx`)\n    },\n  }\n};\n</script>\n<style src=\"../../../assets/styles/assetIndex.scss\" scoped lang=\"scss\"/>\n<style scoped lang=\"scss\">\n.policyCol {\n  min-width: 330px;\n  margin-top: 10px;\n}\n\n.policyDesc {\n  display: flex;\n  height: 80px;\n}\n\n.policyTxt {\n  margin-left: 10px;\n  line-height: 20px;\n}\n\n.policyTitle {\n  height: 40px;\n  line-height: 40px;\n}\n\n.oneLine {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n</style>\n"]}]}