{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorIp.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorIp.vue", "mtime": 1755768894570}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_monitor", "require", "_questResultDetails", "_interopRequireDefault", "_LeakScanDialog", "_ffJobTasks", "_log", "name", "components", "JobLog", "FfJobTasks", "LeakScanDialog", "QuestResultDetails", "dicts", "props", "toParams", "type", "Object", "default", "data", "jobType", "undefined", "openCron", "openSelect", "loading", "jobId", "logJobId", "totalScan", "ids", "single", "multiple", "showSearch", "total", "jobList", "title", "editTitle", "open", "openView", "openLogView", "scanStrategyVisible", "editForm", "queryParams", "pageNum", "pageSize", "job<PERSON>ame", "jobGroup", "status", "invokeDisable", "isDisabled", "cronText", "rows", "form", "rules", "required", "message", "trigger", "invokeIp", "cronExpression", "getListInterval", "selectedIds", "watch", "handler", "newVal", "id", "handleJobLog", "immediate", "created", "_this2", "getList", "setInterval", "loopGetList", "destroyed", "clearInterval", "methods", "_this3", "listJob", "then", "response", "for<PERSON>ach", "s", "invoke<PERSON><PERSON><PERSON>", "target", "start", "indexOf", "end", "length", "ips", "substring", "ipss", "split", "ipShow", "replaceAll", "ipOver", "period", "currentStatus", "_this4", "newJobList", "_toConsumableArray2", "$nextTick", "rowsToSelect", "filter", "row", "includes", "$refs", "multipleTable", "clearSelection", "toggleRowSelection", "handleScan", "weakPw", "cronTransfer", "jobGroupFormat", "column", "selectDictLabel", "dict", "sys_job_group", "cancel", "reset", "cronTabFill", "val", "misfirePolicy", "concurrent", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "getRowKey", "handleSelectionChange", "selection", "map", "item", "handleCommand", "command", "handleDetail", "handleRun", "handleView", "handleStatusChange", "_this5", "text", "$modal", "confirm", "changeJobStatus", "msgSuccess", "catch", "finally", "_this", "setTimeout", "_this6", "runJob", "_objectSpread2", "editNow", "$message", "error", "handleUpdate", "executeNow", "handleSelect", "handleAdd", "_this7", "get<PERSON>ob", "submitForm", "_this8", "validate", "valid", "ipReg", "ipSegmentReg", "join", "updateJob", "addJob", "handleDelete", "_this9", "jobIds", "<PERSON><PERSON><PERSON>", "handleExport", "download", "concat", "Date", "getTime"], "sources": ["src/views/frailty/monitor/monitorIp.vue"], "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务名称\" prop=\"jobName\">\n                <el-input\n                  v-model=\"queryParams.jobName\"\n                  placeholder=\"请输入任务名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"扫描目标\" prop=\"invokeTarget\">\n                <el-input\n                  v-model=\"queryParams.invokeTarget\"\n                  placeholder=\"扫描目标\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务状态\" prop=\"status\">\n                <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.sys_job_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <!--<el-col :span=\"6\">-->\n              <!--<el-form-item label=\"执行策略\" prop=\"jobType\">-->\n                <!--<el-select v-model=\"queryParams.jobType\" placeholder=\"请选择执行策略\" clearable>-->\n                  <!--<el-option label=\"资产扫描监控\" :value=\"0\"/>-->\n                  <!--<el-option label=\"基础服务漏洞扫描\" :value=\"1\"/>-->\n                  <!--<el-option label=\"基础Web漏洞扫描\" :value=\"2\"/>-->\n                  <!--<el-option label=\"主机资产探测\" :value=\"3\"/>-->\n                <!--</el-select>-->\n              <!--</el-form-item>-->\n            <!--</el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">主机漏扫任务列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleScan\"\n                  v-hasPermi=\"['monitor:ipschedule:add']\"\n                >新增\n                </el-button>\n              </el-col>\n<!--              <el-col :span=\"1.5\">-->\n<!--                <el-button-->\n<!--                  class=\"btn1\"-->\n<!--                  size=\"small\"-->\n<!--                  :disabled=\"single\"-->\n<!--                  @click=\"handleUpdate\"-->\n<!--                  v-hasPermi=\"['monitor:schedule:edit']\"-->\n<!--                >修改-->\n<!--                </el-button>-->\n<!--              </el-col>-->\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['monitor:ipschedule:remove']\"\n                >批量删除\n                </el-button>\n              </el-col>\n              <!--            <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          :data=\"jobList\"\n          ref=\"multipleTable\"\n          :row-key=\"getRowKey\"\n          @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <!--<el-table-column label=\"任务编号\" width=\"100\"  prop=\"jobId\"/>-->\n          <el-table-column label=\"任务名称\"  prop=\"jobName\" :show-overflow-tooltip=\"true\"/>\n          <!--<el-table-column label=\"执行策略\"  prop=\"jobGroup\">-->\n            <!--<template slot-scope=\"scope\">-->\n              <!--<span v-if=\"scope.row.jobType === 0\">资产扫描监控</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 1\">基础服务漏洞扫描</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 2\">基础Web漏洞扫描</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 3\">主机资产探测</span>-->\n            <!--</template>-->\n          <!--</el-table-column>-->\n          <el-table-column label=\"扫描目标\"  prop=\"ipShow\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <el-tooltip v-if=\"scope.row.jobType !== 3\" class=\"item\" placement=\"top\">\n                <div v-html=\"scope.row.ipOver\" slot=\"content\"></div>\n                <div class=\"oneLine\">\n                  {{ scope.row.ipShow }}\n                </div>\n              </el-tooltip>\n              <span v-else>-</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"执行周期\"  prop=\"cronTransfer\"/>\n          <el-table-column label=\"最近一次执行时间\"  prop=\"lastRunTime\"/>\n          <el-table-column label=\"最近扫描状态\"  prop=\"jobGroup\">\n            <template slot-scope=\"scope\">\n              <el-tag type=\"danger\" v-if=\"scope.row.currentStatus === 0\">未扫描</el-tag>\n              <el-tag v-else-if=\"scope.row.currentStatus === 1\">扫描中</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.currentStatus === 2\">已扫描</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"任务状态\" >\n            <template slot-scope=\"scope\">\n              <el-switch\n                v-model=\"scope.row.status\"\n                active-value=\"0\"\n                inactive-value=\"1\"\n                @change=\"handleStatusChange(scope.row)\"\n              ></el-switch>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"220\" fixed=\"right\" :show-overflow-tooltip=\"false\" class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleView(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:query']\"\n              >详情\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.currentStatus === 1\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:edit']\"\n              >编辑\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :class=\"{'table-delBtn':scope.row.currentStatus !== 1}\"\n                :disabled=\"scope.row.currentStatus === 1\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:remove']\"\n              >删除\n              </el-button>\n              <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\"\n                           v-hasPermi=\"['monitor:ipschedule:query']\">\n            <span class=\"el-dropdown-link\">\n              <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\n            </span>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item :disabled=\"scope.row.status === '1' || scope.row.currentStatus === 1\"\n                                    command=\"handleRun\" icon=\"el-icon-caret-right\">执行一次\n                  </el-dropdown-item>\n                  <!--<el-dropdown-item command=\"handleView\" icon=\"el-icon-view\">任务详细</el-dropdown-item>-->\n                  <el-dropdown-item command=\"handleJobLog\" icon=\"el-icon-s-operation\">调度日志</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <LeakScanDialog\n      :title=\"title\"\n      :edit-form=\"editForm\"\n      :edit-title=\"editTitle\"\n      :is-disable.sync=\"invokeDisable\"\n      @getList=\"getList\"\n      :scan-strategy-visible.sync=\"scanStrategyVisible\"/>\n\n\n\n    <!-- 任务日志详细 -->\n    <el-dialog title=\"任务详细\" v-if=\"openView\" :visible.sync=\"openView\" v-dialog-drag width=\"1200px\" append-to-body>\n      <ff-job-tasks  v-if=\"openView\" :jobId=\"jobId\" :job-type=\"jobType\" :job-row=\"editForm\" />\n    </el-dialog>\n    <!-- 调度日志 -->\n    <el-dialog title=\"调度日志\" v-if=\"openLogView\" :visible.sync=\"openLogView\" v-dialog-drag width=\"1200px\" append-to-body>\n      <job-log v-if=\"openLogView\" :jobId=\"logJobId\"></job-log>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"openLogView = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus} from \"@/api/safe/monitor\";\nimport QuestResultDetails from '../../safe/server/questResultDetails'\nimport LeakScanDialog from '../../safe/server/components/LeakScanDialog'\nimport FfJobTasks from './ffJobTasks'\nimport JobLog from '../../monitor/job/log'\n\nexport default {\n  name: \"Job\",\n  components: { JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },\n  dicts: ['sys_job_group', 'sys_job_status'],\n  props: {\n    toParams: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      jobType: undefined,\n      // 是否显示Cron表达式弹出层\n      openCron: false,\n      // 展示最近一次运行结果\n      openSelect: false,\n      // 遮罩层\n      loading: true,\n      // 任务ID\n      jobId: '',\n      logJobId: null,\n      totalScan: 0,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 定时任务表格数据\n      jobList: [],\n      // 弹出层标题\n      title: \"\",\n      editTitle: '',\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详细弹出层\n      openView: false,\n      openLogView: false,\n      scanStrategyVisible:  false,\n      editForm: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        jobType: 1,\n        jobName: undefined,\n        jobGroup: 'ASSET_SCAN',\n        status: undefined\n      },\n      invokeDisable: false,\n      isDisabled: false,\n      // 周期转换文字\n      cronText: '',\n      rows: [],\n      form: {},\n      // 表单校验\n      rules: {\n        jobName: [\n          {required: true, message: \"任务名称不能为空\", trigger: \"blur\"}\n        ],\n        invokeIp: [\n          {required: true, message: \"扫描IP不能为空\", trigger: \"blur\"}\n        ],\n        cronExpression: [\n          {required: true, message: \"cron执行表达式不能为空\", trigger: \"blur\"}\n        ]\n      },\n      getListInterval: null,\n      selectedIds: []\n    };\n  },\n  watch: {\n    toParams: {\n      handler(newVal) {\n        if(newVal && newVal.id){\n          this.handleJobLog({\n            jobId: newVal.id\n          });\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getList()\n    this.getListInterval = setInterval(() => {\n      this.loopGetList()\n    }, 10000)\n  },\n  destroyed() {\n    if(this.getListInterval){\n      clearInterval(this.getListInterval);\n    }\n  },\n  // watch: {\n  //   'dict.type.sys_job_group': {\n  //     handler(newVal) {\n  //       if (newVal.length > 0) {\n  //         let tmp = newVal.filter(s => s.label === '资产扫描')\n  //         this.form.jobGroup = tmp.length > 0 ? tmp[0].value : undefined\n  //         this.queryParams.jobGroup = this.form.jobGroup\n  //       }\n  //     },\n  //     deep: true\n  //   }\n  // },\n  methods: {\n    /** 查询定时任务列表 */\n    getList() {\n      this.loading = true;\n      listJob(this.queryParams).then(response => {\n        response.rows.forEach(s => {\n          if (s.invokeTarget) {\n            const target = s.invokeTarget\n            const start = target.indexOf('\\',\\'') + 3\n            const end = target.length - 2\n            const ips = target.substring(start, end)\n            const ipss = ips.split('|')\n            if (ipss.length > 1) {\n              s.ipShow = ipss[1].replaceAll(';', '  ')\n              s.ipOver = ipss[1].replaceAll(';', '<br>')\n            }\n            if(s.period === 0){\n              s.status = s.currentStatus === 1 ? '0' : '1'\n            }\n          }\n        })\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    loopGetList() {\n      listJob(this.queryParams).then(response => {\n        response.rows.forEach(s => {\n          if (s.invokeTarget) {\n            const target = s.invokeTarget\n            const start = target.indexOf('\\',\\'') + 3\n            const end = target.length - 2\n            const ips = target.substring(start, end)\n            const ipss = ips.split('|')\n            if (ipss.length > 1) {\n              s.ipShow = ipss[1].replaceAll(';', ' ')\n              s.ipOver = ipss[1].replaceAll(';', '<br>')\n            }\n            if(s.period === 0){\n              s.status = s.currentStatus === 1 ? '0' : '1'\n            }\n          }\n        })\n        const newJobList = response.rows\n        const selectedIds = [...this.selectedIds]\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.$nextTick(() => {\n          const rowsToSelect = newJobList.filter(row =>\n            selectedIds.includes(row.jobId)\n          );\n          this.$refs.multipleTable.clearSelection();\n          rowsToSelect.forEach(row => {\n            this.$refs.multipleTable.toggleRowSelection(row, true);\n          })\n        })\n      });\n    },\n    handleScan() {\n      this.invokeDisable = false\n      this.title = '添加任务';\n      this.editTitle = 'IP漏洞扫描'\n      this.editForm = {}\n      this.editForm.jobType = 1;\n      this.editForm.weakPw = '1';\n      this.editForm.status = '0';\n      this.editForm.cronExpression= '* * * * * ?';\n      this.editForm.period= 0;\n      this.editForm.cronTransfer= '立即执行';\n      this.scanStrategyVisible = true;\n    },\n    // 任务组名字典翻译\n    jobGroupFormat(row, column) {\n      return this.selectDictLabel(this.dict.type.sys_job_group, row.jobGroup);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.invokeDisable = false\n      this.reset();\n    },\n    /** 确定后回传值 */\n    cronTabFill(val) {\n      this.form.cronExpression = val.cronText\n      this.form.period = val.period\n      this.form.cronTransfer = val.cronTransfer\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        jobId: undefined,\n        jobName: undefined,\n        invokeTarget: undefined,\n        cronExpression: undefined,\n        misfirePolicy: 0,\n        concurrent: 1,\n        period: 0,\n        jobType: 1,\n        status: \"0\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n\n    getRowKey(row) {\n      return row.jobId;\n    },\n\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.jobId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n      this.rows = selection;\n      this.selectedIds = [...this.ids]\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"lastResult\":\n          this.handleDetail(row);\n          break;\n        case \"handleRun\":\n          this.handleRun(row);\n          break;\n        case \"handleView\":\n          this.handleView(row);\n          break;\n        case \"handleJobLog\":\n          this.handleJobLog(row);\n          break;\n        default:\n          break;\n      }\n    },\n    // 任务状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.jobName + '\"任务吗？').then(function () {\n        return changeJobStatus(row.jobId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function () {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      }).finally(() => {\n        var _this = this\n        setTimeout(() => {\n          _this.getList();\n        }, 1000);\n      });\n    },\n    /* 最近执行结果 */\n    handleDetail(row) {\n      this.jobId = row.jobId\n    },\n    /* 立即执行一次 */\n    handleRun(row) {\n      this.$modal.confirm('确认要立即执行一次\"' + row.jobName + '\"任务吗？').then(function () {\n        return runJob(row.jobId, row.jobGroup);\n      }).then(() => {\n        this.$modal.msgSuccess(\"执行成功\");\n      }).catch(() => {\n      }).finally(() => {\n        this.getList();\n      });\n    },\n    /** 任务详细信息 */\n    handleView(row) {\n      this.openView = true;\n      this.jobType = 2;\n      this.jobId = row.jobId;\n      this.editForm = {...row}\n    },\n    editNow(jobId) {\n      let filter = this.jobList.filter(item => item.jobId == jobId)\n      if (filter.length === 0) {\n        this.$message.error('未找到任务数据！')\n        return\n      } else {\n        if (filter[0].currentStatus === 1) {\n          this.$message.error('当前任务状态为正在扫描中，请勿更改！')\n          return\n        }\n        this.openView = false\n        this.handleUpdate(filter[0])\n      }\n    },\n    executeNow(jobId) {\n      let filter = this.jobList.filter(item => item.jobId == jobId)\n      if (filter.length === 0) {\n        this.$message.error('未找到任务数据！')\n        return\n      } else {\n        if (filter[0].status === '1' || filter[0].currentStatus === 1) {\n          this.$message.error('当前任务状态为暂停或正在扫描中，请勿执行！')\n          return\n        }\n        this.openView = false\n        this.handleRun(filter[0])\n      }\n\n    },\n    /** 任务日志列表查询 */\n    handleJobLog(row) {\n      this.logJobId = row.jobId || 0;\n      this.openLogView = true\n      // this.$router.push({path: '/monitor/job-log/index', query: {jobId: jobId}})\n    },\n    handleSelect() {\n      this.openSelect = true\n    },\n    /** 新增按钮操作 */\n    handleAdd(val) {\n      this.reset();\n      switch (val) {\n        case 0:\n          this.editTitle = '资产扫描监控'\n          break\n        case 1:\n          this.editTitle = '基础服务漏洞扫描'\n          break\n        case 2:\n          this.editTitle = 'Web漏洞扫描'\n          break\n        case 3:\n          this.editTitle = '主机资产探测'\n          break\n        default:\n          break\n      }\n      this.openSelect = false\n      this.form.jobType = val\n      this.open = true;\n      this.title = \"添加任务\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      switch (row.jobType) {\n        case 0:\n          this.editTitle = '资产扫描监控'\n          break\n        case 1:\n          this.editTitle = '基础服务漏洞扫描'\n          break\n        default:\n          break\n      }\n      this.reset();\n      const jobId = row.jobId || this.ids;\n      getJob(jobId).then(response => {\n        const target = response.data.invokeTarget\n        const start = target.indexOf('\\',\\'') + 3\n        const end = target.length - 2\n        const ips = target.substring(start, end)\n        const ipss = ips.split('|')\n        if (ipss.length > 1) {\n          response.data.invokeIp = ipss[1].replaceAll(';', '\\n')\n          response.data.weakPw = ipss[4]\n        }\n        this.editForm= response.data;\n        this.scanStrategyVisible = true;\n        this.invokeDisable = true\n        this.title = \"修改任务\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // ip正则\n          const ipReg = /^(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|[1-9])\\.((1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\.){2}(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$/\n          // ip段正则\n          const ipSegmentReg = /^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.([0-9]|\\d{1,2}|1\\d{1,2}|2[0-4]\\d|25[0-5])-([1-9]|\\d{1,2}|1\\d{1,2}|2[0-4]\\d|25[0-5])$/\n          // 带子网掩码正则\n          // const ipMaskReg = /^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\/(\\d{1,2})$/\n\n          if (this.form.invokeIp) {\n            let ips = this.form.invokeIp.split(/\\n/g);\n            // 调用目标字符串\n            this.form.invokeIp = ips.join(' ')\n          }\n          /**\n           for (let i = 0; i < ips.length; i++) {\n           if (!(ipReg.test(ips[i]) || ipSegmentReg.test(ips[i]))) {\n           this.$message.error('请输入正确的ip地址或地址段，检查是否有空格和特殊字符！')\n           return\n           }\n           } */\n\n\n          if (this.form.jobType === 0) {\n            this.form.invokeTarget = 'jkServer.scan(\\'${jobId}\\',\\'' + this.form.invokeIp + '\\')'\n          } else if (this.form.jobType === 1) {\n            this.form.invokeTarget = 'BaseServerVulnScan.scan(\\'${jobId}\\',\\'' + this.form.invokeIp + '\\')'\n          } else if (this.form.jobType === 2) {\n            this.form.invokeTarget = 'WebServerVulnScan.scan(\\'${jobId}\\',\\'' + this.form.invokeIp + '\\')'\n          } else if (this.form.jobType === 3) {\n            this.form.invokeTarget = 'cloudWalkerScan.scan(\\'${jobId}\\')'\n          }\n\n          // 任务分组\n          this.form.jobGroup = 'ASSET_SCAN'\n\n          if (this.form.jobId != undefined) {\n            updateJob(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.invokeDisable = false\n              this.getList();\n            })\n          } else {\n            addJob(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.invokeDisable = false\n              this.getList();\n            })\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.currentStatus === 1);\n      if (rows.length > 0) {\n        this.$message.error('选择中有扫描中任务，无法批量删除');\n        return false;\n      }\n      const jobIds = row.jobId || this.ids;\n      this.$modal.confirm('是否确认删除定时任务编号为【' + jobIds + '】的数据项？').then(function () {\n        return delJob(jobIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('monitor/schedule/export', {\n        ...this.queryParams\n      }, `job_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n<style src=\"../../../assets/styles/assetIndex.scss\" scoped lang=\"scss\"/>\n<style scoped lang=\"scss\">\n.policyCol {\n  min-width: 330px;\n  margin-top: 10px;\n}\n\n.policyDesc {\n  display: flex;\n  height: 80px;\n}\n\n.policyTxt {\n  margin-left: 10px;\n  line-height: 20px;\n}\n\n.policyTitle {\n  height: 40px;\n  line-height: 40px;\n}\n\n.oneLine {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAoOA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,IAAA,GAAAH,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAM,IAAA;EACAC,UAAA;IAAAC,MAAA,EAAAA,YAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,kBAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAA,SAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA,EAAAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACA;MACAC,IAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,mBAAA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAvB,OAAA;QACAwB,OAAA,EAAAvB,SAAA;QACAwB,QAAA;QACAC,MAAA,EAAAzB;MACA;MACA0B,aAAA;MACAC,UAAA;MACA;MACAC,QAAA;MACAC,IAAA;MACAC,IAAA;MACA;MACAC,KAAA;QACAR,OAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,cAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAG,eAAA;MACAC,WAAA;IACA;EACA;EACAC,KAAA;IACA7C,QAAA;MACA8C,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAC,EAAA;UACA,KAAAC,YAAA;YACAvC,KAAA,EAAAqC,MAAA,CAAAC;UACA;QACA;MACA;MACAE,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAC,OAAA;IACA,KAAAV,eAAA,GAAAW,WAAA;MACAF,MAAA,CAAAG,WAAA;IACA;EACA;EACAC,SAAA,WAAAA,UAAA;IACA,SAAAb,eAAA;MACAc,aAAA,MAAAd,eAAA;IACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAe,OAAA;IACA,eACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,MAAA;MACA,KAAAlD,OAAA;MACA,IAAAmD,gBAAA,OAAAlC,WAAA,EAAAmC,IAAA,WAAAC,QAAA;QACAA,QAAA,CAAA3B,IAAA,CAAA4B,OAAA,WAAAC,CAAA;UACA,IAAAA,CAAA,CAAAC,YAAA;YACA,IAAAC,MAAA,GAAAF,CAAA,CAAAC,YAAA;YACA,IAAAE,KAAA,GAAAD,MAAA,CAAAE,OAAA;YACA,IAAAC,GAAA,GAAAH,MAAA,CAAAI,MAAA;YACA,IAAAC,GAAA,GAAAL,MAAA,CAAAM,SAAA,CAAAL,KAAA,EAAAE,GAAA;YACA,IAAAI,IAAA,GAAAF,GAAA,CAAAG,KAAA;YACA,IAAAD,IAAA,CAAAH,MAAA;cACAN,CAAA,CAAAW,MAAA,GAAAF,IAAA,IAAAG,UAAA;cACAZ,CAAA,CAAAa,MAAA,GAAAJ,IAAA,IAAAG,UAAA;YACA;YACA,IAAAZ,CAAA,CAAAc,MAAA;cACAd,CAAA,CAAAjC,MAAA,GAAAiC,CAAA,CAAAe,aAAA;YACA;UACA;QACA;QACApB,MAAA,CAAAzC,OAAA,GAAA4C,QAAA,CAAA3B,IAAA;QACAwB,MAAA,CAAA1C,KAAA,GAAA6C,QAAA,CAAA7C,KAAA;QACA0C,MAAA,CAAAlD,OAAA;MACA;IACA;IACA8C,WAAA,WAAAA,YAAA;MAAA,IAAAyB,MAAA;MACA,IAAApB,gBAAA,OAAAlC,WAAA,EAAAmC,IAAA,WAAAC,QAAA;QACAA,QAAA,CAAA3B,IAAA,CAAA4B,OAAA,WAAAC,CAAA;UACA,IAAAA,CAAA,CAAAC,YAAA;YACA,IAAAC,MAAA,GAAAF,CAAA,CAAAC,YAAA;YACA,IAAAE,KAAA,GAAAD,MAAA,CAAAE,OAAA;YACA,IAAAC,GAAA,GAAAH,MAAA,CAAAI,MAAA;YACA,IAAAC,GAAA,GAAAL,MAAA,CAAAM,SAAA,CAAAL,KAAA,EAAAE,GAAA;YACA,IAAAI,IAAA,GAAAF,GAAA,CAAAG,KAAA;YACA,IAAAD,IAAA,CAAAH,MAAA;cACAN,CAAA,CAAAW,MAAA,GAAAF,IAAA,IAAAG,UAAA;cACAZ,CAAA,CAAAa,MAAA,GAAAJ,IAAA,IAAAG,UAAA;YACA;YACA,IAAAZ,CAAA,CAAAc,MAAA;cACAd,CAAA,CAAAjC,MAAA,GAAAiC,CAAA,CAAAe,aAAA;YACA;UACA;QACA;QACA,IAAAE,UAAA,GAAAnB,QAAA,CAAA3B,IAAA;QACA,IAAAS,WAAA,OAAAsC,mBAAA,CAAA/E,OAAA,EAAA6E,MAAA,CAAApC,WAAA;QACAoC,MAAA,CAAA9D,OAAA,GAAA4C,QAAA,CAAA3B,IAAA;QACA6C,MAAA,CAAA/D,KAAA,GAAA6C,QAAA,CAAA7C,KAAA;QACA+D,MAAA,CAAAG,SAAA;UACA,IAAAC,YAAA,GAAAH,UAAA,CAAAI,MAAA,WAAAC,GAAA;YAAA,OACA1C,WAAA,CAAA2C,QAAA,CAAAD,GAAA,CAAA5E,KAAA;UAAA,CACA;UACAsE,MAAA,CAAAQ,KAAA,CAAAC,aAAA,CAAAC,cAAA;UACAN,YAAA,CAAArB,OAAA,WAAAuB,GAAA;YACAN,MAAA,CAAAQ,KAAA,CAAAC,aAAA,CAAAE,kBAAA,CAAAL,GAAA;UACA;QACA;MACA;IACA;IACAM,UAAA,WAAAA,WAAA;MACA,KAAA5D,aAAA;MACA,KAAAb,KAAA;MACA,KAAAC,SAAA;MACA,KAAAK,QAAA;MACA,KAAAA,QAAA,CAAApB,OAAA;MACA,KAAAoB,QAAA,CAAAoE,MAAA;MACA,KAAApE,QAAA,CAAAM,MAAA;MACA,KAAAN,QAAA,CAAAiB,cAAA;MACA,KAAAjB,QAAA,CAAAqD,MAAA;MACA,KAAArD,QAAA,CAAAqE,YAAA;MACA,KAAAtE,mBAAA;IACA;IACA;IACAuE,cAAA,WAAAA,eAAAT,GAAA,EAAAU,MAAA;MACA,YAAAC,eAAA,MAAAC,IAAA,CAAAjG,IAAA,CAAAkG,aAAA,EAAAb,GAAA,CAAAxD,QAAA;IACA;IACA;IACAsE,MAAA,WAAAA,OAAA;MACA,KAAA/E,IAAA;MACA,KAAAW,aAAA;MACA,KAAAqE,KAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAAnE,IAAA,CAAAM,cAAA,GAAA6D,GAAA,CAAArE,QAAA;MACA,KAAAE,IAAA,CAAA0C,MAAA,GAAAyB,GAAA,CAAAzB,MAAA;MACA,KAAA1C,IAAA,CAAA0D,YAAA,GAAAS,GAAA,CAAAT,YAAA;IACA;IACA;IACAO,KAAA,WAAAA,MAAA;MACA,KAAAjE,IAAA;QACA1B,KAAA,EAAAJ,SAAA;QACAuB,OAAA,EAAAvB,SAAA;QACA2D,YAAA,EAAA3D,SAAA;QACAoC,cAAA,EAAApC,SAAA;QACAkG,aAAA;QACAC,UAAA;QACA3B,MAAA;QACAzE,OAAA;QACA0B,MAAA;MACA;MACA,KAAA2E,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAjF,WAAA,CAAAC,OAAA;MACA,KAAA0B,OAAA;IACA;IACA,aACAuD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IAEAE,SAAA,WAAAA,UAAAvB,GAAA;MACA,OAAAA,GAAA,CAAA5E,KAAA;IACA;IAEA;IACAoG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAlG,GAAA,GAAAkG,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAvG,KAAA;MAAA;MACA,KAAAI,MAAA,GAAAiG,SAAA,CAAAzC,MAAA;MACA,KAAAvD,QAAA,IAAAgG,SAAA,CAAAzC,MAAA;MACA,KAAAnC,IAAA,GAAA4E,SAAA;MACA,KAAAnE,WAAA,OAAAsC,mBAAA,CAAA/E,OAAA,OAAAU,GAAA;IACA;IACA;IACAqG,aAAA,WAAAA,cAAAC,OAAA,EAAA7B,GAAA;MACA,QAAA6B,OAAA;QACA;UACA,KAAAC,YAAA,CAAA9B,GAAA;UACA;QACA;UACA,KAAA+B,SAAA,CAAA/B,GAAA;UACA;QACA;UACA,KAAAgC,UAAA,CAAAhC,GAAA;UACA;QACA;UACA,KAAArC,YAAA,CAAAqC,GAAA;UACA;QACA;UACA;MACA;IACA;IACA;IACAiC,kBAAA,WAAAA,mBAAAjC,GAAA;MAAA,IAAAkC,MAAA;MACA,IAAAC,IAAA,GAAAnC,GAAA,CAAAvD,MAAA;MACA,KAAA2F,MAAA,CAAAC,OAAA,UAAAF,IAAA,UAAAnC,GAAA,CAAAzD,OAAA,YAAAgC,IAAA;QACA,WAAA+D,wBAAA,EAAAtC,GAAA,CAAA5E,KAAA,EAAA4E,GAAA,CAAAvD,MAAA;MACA,GAAA8B,IAAA;QACA2D,MAAA,CAAAE,MAAA,CAAAG,UAAA,CAAAJ,IAAA;MACA,GAAAK,KAAA;QACAxC,GAAA,CAAAvD,MAAA,GAAAuD,GAAA,CAAAvD,MAAA;MACA,GAAAgG,OAAA;QACA,IAAAC,KAAA,GAAAR,MAAA;QACAS,UAAA;UACAD,KAAA,CAAA3E,OAAA;QACA;MACA;IACA;IACA,YACA+D,YAAA,WAAAA,aAAA9B,GAAA;MACA,KAAA5E,KAAA,GAAA4E,GAAA,CAAA5E,KAAA;IACA;IACA,YACA2G,SAAA,WAAAA,UAAA/B,GAAA;MAAA,IAAA4C,MAAA;MACA,KAAAR,MAAA,CAAAC,OAAA,gBAAArC,GAAA,CAAAzD,OAAA,YAAAgC,IAAA;QACA,WAAAsE,eAAA,EAAA7C,GAAA,CAAA5E,KAAA,EAAA4E,GAAA,CAAAxD,QAAA;MACA,GAAA+B,IAAA;QACAqE,MAAA,CAAAR,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA,cACA,GAAAC,OAAA;QACAG,MAAA,CAAA7E,OAAA;MACA;IACA;IACA,aACAiE,UAAA,WAAAA,WAAAhC,GAAA;MACA,KAAAhE,QAAA;MACA,KAAAjB,OAAA;MACA,KAAAK,KAAA,GAAA4E,GAAA,CAAA5E,KAAA;MACA,KAAAe,QAAA,OAAA2G,cAAA,CAAAjI,OAAA,MAAAmF,GAAA;IACA;IACA+C,OAAA,WAAAA,QAAA3H,KAAA;MACA,IAAA2E,MAAA,QAAAnE,OAAA,CAAAmE,MAAA,WAAA4B,IAAA;QAAA,OAAAA,IAAA,CAAAvG,KAAA,IAAAA,KAAA;MAAA;MACA,IAAA2E,MAAA,CAAAf,MAAA;QACA,KAAAgE,QAAA,CAAAC,KAAA;QACA;MACA;QACA,IAAAlD,MAAA,IAAAN,aAAA;UACA,KAAAuD,QAAA,CAAAC,KAAA;UACA;QACA;QACA,KAAAjH,QAAA;QACA,KAAAkH,YAAA,CAAAnD,MAAA;MACA;IACA;IACAoD,UAAA,WAAAA,WAAA/H,KAAA;MACA,IAAA2E,MAAA,QAAAnE,OAAA,CAAAmE,MAAA,WAAA4B,IAAA;QAAA,OAAAA,IAAA,CAAAvG,KAAA,IAAAA,KAAA;MAAA;MACA,IAAA2E,MAAA,CAAAf,MAAA;QACA,KAAAgE,QAAA,CAAAC,KAAA;QACA;MACA;QACA,IAAAlD,MAAA,IAAAtD,MAAA,YAAAsD,MAAA,IAAAN,aAAA;UACA,KAAAuD,QAAA,CAAAC,KAAA;UACA;QACA;QACA,KAAAjH,QAAA;QACA,KAAA+F,SAAA,CAAAhC,MAAA;MACA;IAEA;IACA,eACApC,YAAA,WAAAA,aAAAqC,GAAA;MACA,KAAA3E,QAAA,GAAA2E,GAAA,CAAA5E,KAAA;MACA,KAAAa,WAAA;MACA;IACA;IACAmH,YAAA,WAAAA,aAAA;MACA,KAAAlI,UAAA;IACA;IACA,aACAmI,SAAA,WAAAA,UAAApC,GAAA;MACA,KAAAF,KAAA;MACA,QAAAE,GAAA;QACA;UACA,KAAAnF,SAAA;UACA;QACA;UACA,KAAAA,SAAA;UACA;QACA;UACA,KAAAA,SAAA;UACA;QACA;UACA,KAAAA,SAAA;UACA;QACA;UACA;MACA;MACA,KAAAZ,UAAA;MACA,KAAA4B,IAAA,CAAA/B,OAAA,GAAAkG,GAAA;MACA,KAAAlF,IAAA;MACA,KAAAF,KAAA;IACA;IACA,aACAqH,YAAA,WAAAA,aAAAlD,GAAA;MAAA,IAAAsD,MAAA;MACA,QAAAtD,GAAA,CAAAjF,OAAA;QACA;UACA,KAAAe,SAAA;UACA;QACA;UACA,KAAAA,SAAA;UACA;QACA;UACA;MACA;MACA,KAAAiF,KAAA;MACA,IAAA3F,KAAA,GAAA4E,GAAA,CAAA5E,KAAA,SAAAG,GAAA;MACA,IAAAgI,eAAA,EAAAnI,KAAA,EAAAmD,IAAA,WAAAC,QAAA;QACA,IAAAI,MAAA,GAAAJ,QAAA,CAAA1D,IAAA,CAAA6D,YAAA;QACA,IAAAE,KAAA,GAAAD,MAAA,CAAAE,OAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAAI,MAAA;QACA,IAAAC,GAAA,GAAAL,MAAA,CAAAM,SAAA,CAAAL,KAAA,EAAAE,GAAA;QACA,IAAAI,IAAA,GAAAF,GAAA,CAAAG,KAAA;QACA,IAAAD,IAAA,CAAAH,MAAA;UACAR,QAAA,CAAA1D,IAAA,CAAAqC,QAAA,GAAAgC,IAAA,IAAAG,UAAA;UACAd,QAAA,CAAA1D,IAAA,CAAAyF,MAAA,GAAApB,IAAA;QACA;QACAmE,MAAA,CAAAnH,QAAA,GAAAqC,QAAA,CAAA1D,IAAA;QACAwI,MAAA,CAAApH,mBAAA;QACAoH,MAAA,CAAA5G,aAAA;QACA4G,MAAA,CAAAzH,KAAA;MACA;IACA;IACA;IACA2H,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAvD,KAAA,SAAAwD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAC,KAAA;UACA;UACA,IAAAC,YAAA;UACA;UACA;;UAEA,IAAAJ,MAAA,CAAA3G,IAAA,CAAAK,QAAA;YACA,IAAA8B,GAAA,GAAAwE,MAAA,CAAA3G,IAAA,CAAAK,QAAA,CAAAiC,KAAA;YACA;YACAqE,MAAA,CAAA3G,IAAA,CAAAK,QAAA,GAAA8B,GAAA,CAAA6E,IAAA;UACA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;;UAGA,IAAAL,MAAA,CAAA3G,IAAA,CAAA/B,OAAA;YACA0I,MAAA,CAAA3G,IAAA,CAAA6B,YAAA,qCAAA8E,MAAA,CAAA3G,IAAA,CAAAK,QAAA;UACA,WAAAsG,MAAA,CAAA3G,IAAA,CAAA/B,OAAA;YACA0I,MAAA,CAAA3G,IAAA,CAAA6B,YAAA,+CAAA8E,MAAA,CAAA3G,IAAA,CAAAK,QAAA;UACA,WAAAsG,MAAA,CAAA3G,IAAA,CAAA/B,OAAA;YACA0I,MAAA,CAAA3G,IAAA,CAAA6B,YAAA,8CAAA8E,MAAA,CAAA3G,IAAA,CAAAK,QAAA;UACA,WAAAsG,MAAA,CAAA3G,IAAA,CAAA/B,OAAA;YACA0I,MAAA,CAAA3G,IAAA,CAAA6B,YAAA;UACA;;UAEA;UACA8E,MAAA,CAAA3G,IAAA,CAAAN,QAAA;UAEA,IAAAiH,MAAA,CAAA3G,IAAA,CAAA1B,KAAA,IAAAJ,SAAA;YACA,IAAA+I,kBAAA,EAAAN,MAAA,CAAA3G,IAAA,EAAAyB,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAArB,MAAA,CAAAG,UAAA;cACAkB,MAAA,CAAA1H,IAAA;cACA0H,MAAA,CAAA/G,aAAA;cACA+G,MAAA,CAAA1F,OAAA;YACA;UACA;YACA,IAAAiG,eAAA,EAAAP,MAAA,CAAA3G,IAAA,EAAAyB,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAArB,MAAA,CAAAG,UAAA;cACAkB,MAAA,CAAA1H,IAAA;cACA0H,MAAA,CAAA/G,aAAA;cACA+G,MAAA,CAAA1F,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAkG,YAAA,WAAAA,aAAAjE,GAAA;MAAA,IAAAkE,MAAA;MACA,IAAArH,IAAA,OAAA+C,mBAAA,CAAA/E,OAAA,OAAAgC,IAAA;MACAA,IAAA,GAAAA,IAAA,CAAAkD,MAAA,WAAA4B,IAAA;QAAA,OAAAA,IAAA,CAAAlC,aAAA;MAAA;MACA,IAAA5C,IAAA,CAAAmC,MAAA;QACA,KAAAgE,QAAA,CAAAC,KAAA;QACA;MACA;MACA,IAAAkB,MAAA,GAAAnE,GAAA,CAAA5E,KAAA,SAAAG,GAAA;MACA,KAAA6G,MAAA,CAAAC,OAAA,oBAAA8B,MAAA,aAAA5F,IAAA;QACA,WAAA6F,eAAA,EAAAD,MAAA;MACA,GAAA5F,IAAA;QACA2F,MAAA,CAAAnG,OAAA;QACAmG,MAAA,CAAA9B,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,aACA6B,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,gCAAAxB,cAAA,CAAAjI,OAAA,MACA,KAAAuB,WAAA,UAAAmI,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}