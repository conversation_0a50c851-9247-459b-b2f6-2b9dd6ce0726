package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class FfsafeHostscanTaskResult extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    private Long id;

    /** 主机扫描任务id */
    @Excel(name = "主机扫描任务id")
    private Integer taskId;

    /** ip地址 */
    @Excel(name = "ip地址")
    private String ip;

    /** 系统名称 */
    @Excel(name = "系统名称")
    private String systemName;

    /** 系统版本 */
    @Excel(name = "系统版本")
    private String systemVersion;

    /** 高风险漏洞数 */
    @Excel(name = "高风险漏洞数")
    private Integer highRiskNum;

    /** 中风险漏洞数 */
    @Excel(name = "中风险漏洞数")
    private Integer middleRiskNum;

    /** 低风险漏洞数 */
    @Excel(name = "低风险漏洞数")
    private Integer lowRiskNum;

    /** 可利用漏洞数 */
    @Excel(name = "可利用漏洞数")
    private Integer pocRiskNum;

    /** 弱口令数 */
    @Excel(name = "弱口令数")
    private Integer pwNum;

    /** 端口数 */
    @Excel(name = "端口数")
    private Integer portNum;

    /** 汇总记录ID */
    @Excel(name = "汇总记录ID")
    private Long summaryId;
}
