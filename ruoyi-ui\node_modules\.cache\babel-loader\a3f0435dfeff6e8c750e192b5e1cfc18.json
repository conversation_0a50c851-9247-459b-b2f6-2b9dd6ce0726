{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasksDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasksDetail.vue", "mtime": 1755743179263}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_wpresult", "require", "name", "props", "form", "type", "Array", "default", "jobRow", "Object", "macAipList", "data", "activeNames", "ls", "collapseLabelSpan", "collapseContentSpan", "levelList", "label", "value", "hostLevelList", "hostStrategyList", "level", "headerCellStyle", "color", "cellStyle", "queryParams", "pageNum", "pageSize", "queryWpParams", "queryPortParams", "queryScanParams", "jobTaskList", "wpResultList", "scanList", "portResultList", "holeMapData", "xAsisArr", "pieMapData", "portTotal", "wpTotal", "total", "scanTotal", "loading", "mounted", "getIpList", "getWpresultList", "getPortresultList", "getScanList", "getMiddlehighlevelstat", "methods", "_this", "taskId", "jobType", "listIpTaskresult", "then", "response", "rows", "$nextTick", "getHostriskstat", "catch", "_this2", "listIpWpresult", "_this3", "listIpPortresult", "_this4", "hostriskstat", "for<PERSON>ach", "e", "max_risk_level", "push", "low", "host_num", "itemStyle", "middle", "high", "poc", "barMapInit", "pocRiskNum", "highRiskNum", "middleRiskNum", "lowRiskNum", "_this5", "middlehighlevelstat", "pieMapInit", "infoRiskNum", "_this6", "listVulnresult", "highRisk", "middleRisk", "lowRisk", "pocRisk", "listWebresult", "evidence", "decodeBase64", "base64String", "decodeURIComponent", "escape", "window", "atob", "pieMapChar", "$echarts", "init", "$refs", "pieMap", "title", "setOption", "text", "left", "tooltip", "trigger", "legend", "orient", "x", "y", "series", "radius", "avoidLabelOverlap", "borderRadius", "borderColor", "borderWidth", "show", "position", "labelLine", "holeMapChar", "holeMap", "axisPointer", "grid", "top", "right", "bottom", "containLabel", "xAxis", "yAxis", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/frailty/monitor/ffJobTasksDetail.vue"], "sourcesContent": ["<template>\n  <div>\n    <el-row class=\"s_d_row\">\n      <el-col>\n        <span class=\"s_span_tag\">漏洞结果综述</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"15\" class=\"row-item\">\n      <el-col :span=\"12\">\n        <div ref=\"holeMap\" id=\"holeMap\" style=\"height: 300px\"></div>\n      </el-col>\n      <el-col :span=\"12\">\n        <div ref=\"pieMap\" id=\"pieMap\" style=\"height: 300px\"></div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"form\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\" border v-if=\"jobRow.jobType === 1\">\n            <el-table-column label=\"任务名称\">\n              <template slot-scope=\"scope\">\n                <span> {{ jobRow.jobName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"存活主机\" >\n              <template slot-scope=\"scope\">\n                <span>{{ total }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"ipv4\" label=\"弱口令\" >\n              <template slot-scope=\"scope\">\n                <span>{{ wpTotal }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"pocRiskNum\" label=\"可入侵漏洞\" />\n            <el-table-column prop=\"highRiskNum\" label=\"高风险漏洞\" />\n            <el-table-column prop=\"middleRiskNum\" label=\"中风险漏洞\" />\n            <el-table-column prop=\"lowRiskNum\" label=\"低风险漏洞\" />\n          </el-table>\n          <el-table :data=\"form\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\" border v-if=\"jobRow.jobType === 2\">\n            <el-table-column label=\"任务名称\">\n              <template slot-scope=\"scope\">\n                <span> {{ jobRow.jobName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"网站\" >\n              <template slot-scope=\"scope\">\n                <div v-html=\"jobRow.ipOver\" slot=\"content\"></div>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"highRiskNum\" label=\"高风险\" />\n            <el-table-column prop=\"middleRiskNum\" label=\"中风险\" />\n            <el-table-column prop=\"lowRiskNum\" label=\"低风险\" />\n            <el-table-column prop=\"infoRiskNum\" label=\"信息风险\" />\n            <el-table-column prop=\"startTime\" label=\"开始时间\" />\n            <el-table-column prop=\"endTime\" label=\"结束时间\" />\n          </el-table>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\">\n      <el-col>\n        <span class=\"s_span_tag\">任务参数</span>\n      </el-col>\n    </el-row>\n    <el-row style=\"height: 40px;line-height: 40px;margin-top: 20px;border: solid 1px #e7e3e361;\">\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">任务类型</span>\n          <span v-if=\"jobRow.jobType === 1\">基础服务漏洞扫描</span>\n          <span v-if=\"jobRow.jobType === 2\">基础Web漏洞扫描</span>\n        </div>\n      </el-col>\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">任务目标</span>\n          <span v-html=\"jobRow.ipShow\" slot=\"content\" class=\"truncate\" :title=\"jobRow.ipShow\"></span>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row style=\"height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;\">\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">任务状态</span>\n          <el-tag type=\"danger\" v-if=\"form[0].taskType === 2 && form[0].taskStatus === 3\">任务异常</el-tag>\n          <el-tag v-else-if=\"form[0].taskType === 2 && form[0].taskStatus === 1\">扫描中</el-tag>\n          <el-tag v-else-if=\"form[0].taskType === 2 && form[0].taskStatus === 2\">扫描中</el-tag>\n          <el-tag type=\"success\" v-else-if=\"form[0].taskType === 2 && form[0].taskStatus === 4\">已扫描</el-tag>\n          <el-tag type=\"danger\" v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 3\">任务异常</el-tag>\n          <el-tag type=\"danger\" v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 4\">任务终止</el-tag>\n          <el-tag v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 0\">扫描中</el-tag>\n          <el-tag v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 1\">扫描中</el-tag>\n          <el-tag type=\"success\" v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 2\">已扫描</el-tag>\n        </div>\n      </el-col>\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">开始时间</span>\n          <span>{{ form[0].startTime }}</span>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row style=\"height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;\">\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">探测参数</span>\n          <span>  快速/存活探测启用/全连接</span>\n        </div>\n      </el-col>\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">结束时间</span>\n          <span>{{ form[0].endTime }}</span>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row style=\"height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;\">\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">端口范围</span>\n          <span>常规端口</span>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">主机漏洞列表</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"jobTaskList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"index\" label=\"序号\" width=\"70\" />\n            <el-table-column prop=\"ip\" label=\"IP\" />\n            <el-table-column prop=\"systemName\" label=\"操作系统  \" />\n            <el-table-column prop=\"pocRiskNum\" label=\"可入侵漏洞\" />\n            <el-table-column prop=\"highRiskNum\" label=\"高风险\" />\n            <el-table-column prop=\"middleRiskNum\" label=\"中风险\" />\n            <el-table-column prop=\"lowRiskNum\" label=\"低风险\" />\n            <el-table-column prop=\"pwNum\" label=\"弱口令数量\" />\n            <el-table-column prop=\"portNum\" label=\"开放端口数量\" />\n          </el-table>\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getIpList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\">\n      <el-col>\n        <span class=\"s_span_tag\">漏洞分布列表</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"scanList\" v-loading=\"loading\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"expand\" v-if=\"jobRow.jobType === 1\">\n              <template slot-scope=\"props\">\n                <el-descriptions :column=\"1\" border :label-style=\"ls\">\n                  <el-descriptions-item label=\"受影响IP\">{{ props.row.hostIp }}</el-descriptions-item>\n                  <el-descriptions-item label=\"受影响端口\">{{ props.row.hostPort }}</el-descriptions-item>\n                  <el-descriptions-item label=\"受影响版本信息\">{{ props.row.versionInfo }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞发布时间\">{{ props.row.publishDate }}</el-descriptions-item>\n                  <el-descriptions-item label=\"威胁类型\">{{ props.row.vulnType }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞简介\">\n                    {{ props.row.vulnInfo }}\n                  </el-descriptions-item>\n                  <el-descriptions-item label=\"解决方案\"><span v-html=\"props.row.vulnSolve\"></span></el-descriptions-item>\n                </el-descriptions>\n              </template>\n            </el-table-column>\n            <el-table-column type=\"index\" label=\"序号\" width=\"70\" />\n            <el-table-column prop=\"vulnName\" label=\"漏洞名称\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.riskLevel == 1\" style=\"color: #13ce66\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.riskLevel == 2\" style=\"color: #ffba00\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.riskLevel == 3\" style=\"color: #ff4949\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.riskLevel == 4\"  style=\"color: #ff4949\">{{ scope.row.vulnName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"vulnType\" label=\"漏洞类型\" />\n            <el-table-column prop=\"riskLevel\" label=\"风险级别\"  >\n              <template slot-scope=\"scope\">\n                <el-tag v-if=\"scope.row.riskLevel == 1\" type=\"success\"\n                >低风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.riskLevel == 2\" type=\"warning\"\n                >中风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.riskLevel == 3\" type=\"danger\"\n                >高风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.riskLevel == 4\" type=\"danger\">严重</el-tag>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"scanTotal>0\"\n            :total=\"scanTotal\"\n            :page.sync=\"queryScanParams.pageNum\"\n            :limit.sync=\"queryScanParams.pageSize\"\n            @pagination=\"getScanList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 2\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"scanList\" v-loading=\"loading\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"expand\">\n              <template slot-scope=\"props\">\n                <el-descriptions :column=\"1\" border :label-style=\"ls\">\n                  <el-descriptions-item label=\"漏洞描述\">{{ props.row.description }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞危害\">{{ props.row.impact }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞解决方案\">{{ props.row.recommendation }}</el-descriptions-item>\n                  <el-descriptions-item label=\"扫描目标\">{{ props.row.url }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞构造请求\">\n                    <span v-html=\"props.row.evidence\" :style=\"props.row.evidence.includes('Host') ? { whiteSpace: 'pre-wrap' } : { whiteSpace: 'normal' }\"></span>\n                  </el-descriptions-item>\n\n                </el-descriptions>\n              </template>\n            </el-table-column>\n            <el-table-column type=\"index\" label=\"序号\" width=\"70\" />\n            <el-table-column prop=\"vulnName\" label=\"漏洞名称\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.severity == 1\" style=\"color: #1890ff\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.severity == 2\" style=\"color: #13ce66\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.severity == 3\" style=\"color: #ffba00\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.severity == 4\"  style=\"color: #ff4949\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.severity == 0\"  style=\"color: #ff4949\">{{ scope.row.vulnName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"vulnType\" label=\"漏洞类型  \" />\n            <el-table-column prop=\"severity\" label=\"风险级别\"  >\n              <template slot-scope=\"scope\">\n                <el-tag v-if=\"scope.row.severity == 1\" type=\"primary\"\n                >信息风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.severity == 2\" type=\"success\"\n                >低风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.severity == 3\" type=\"warning\"\n                >中风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.severity == 4\" type=\"danger\">高风险</el-tag>\n                <el-tag v-if=\"scope.row.severity == 0\" type=\"danger\">信息风险</el-tag>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"scanTotal>0\"\n            :total=\"scanTotal\"\n            :page.sync=\"queryScanParams.pageNum\"\n            :limit.sync=\"queryScanParams.pageSize\"\n            @pagination=\"getScanList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">弱口令列表</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"wpResultList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"index\" label=\"序号\" width=\"70px\" />\n            <el-table-column prop=\"hostIp\" label=\"IP\" />\n            <el-table-column prop=\"serviceType\" label=\"服务类型  \" />\n            <el-table-column prop=\"hostPort\" label=\"端口\" />\n            <el-table-column prop=\"username\" label=\"用户名\" />\n            <el-table-column prop=\"weakPassword\" label=\"密码\" />\n          </el-table>\n          <pagination\n            v-show=\"wpTotal>0\"\n            :total=\"wpTotal\"\n            :page.sync=\"queryWpParams.pageNum\"\n            :limit.sync=\"queryWpParams.pageSize\"\n            @pagination=\"getWpresultList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">开放端口列表</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"portResultList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"index\" label=\"序号\" width=\"70px\" />\n            <el-table-column prop=\"hostIp\" label=\"IP\" />\n            <el-table-column prop=\"hostPort\" label=\"端口  \" />\n            <el-table-column prop=\"procotol\" label=\"传输协议\" />\n            <el-table-column prop=\"serviceName\" label=\"服务名称\" />\n            <el-table-column prop=\"product\" label=\"服务与版本\" />\n          </el-table>\n          <pagination\n            v-show=\"portTotal>0\"\n            :total=\"portTotal\"\n            :page.sync=\"queryPortParams.pageNum\"\n            :limit.sync=\"queryPortParams.pageSize\"\n            @pagination=\"getPortresultList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 2\">\n      <el-col>\n        <span class=\"s_span_tag\">附录-漏洞等级风险说明</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 2\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          漏洞是与信息资产有关的弱点或安全隐患。漏洞本身并不对资产构成危害，但是在一定条件得到满是时，漏洞会被威胁加以利用来对信息资产造成危险!本报告的漏洞共分了以下4种漏洞风险等级。\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 2\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"levelList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column prop=\"label\" label=\"危险程度\" width=\"120px\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.label === '高'\" style=\"color: #ff4949\">高</span>\n                <span v-if=\"scope.row.label === '中'\" style=\"color: #ffba00\">中</span>\n                <span v-if=\"scope.row.label === '低'\" style=\"color: #13ce66\">低</span>\n                <span v-if=\"scope.row.label === '风险信息'\" style=\"color: #1890ff\">风险信息</span>\n              </template>\n            </el-table-column>>\n            <el-table-column prop=\"value\" label=\"危险程度说明\" />\n          </el-table>\n        </div>\n      </el-col>\n    </el-row>\n\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">附录1-漏洞等级风险说明</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          漏洞是与信息资产有关的弱点或安全隐患。漏洞本身并不对资产构成危害，但是在一定条件得到满是时，漏洞会被威胁加以利用来对信息资产造成危险!本报告的漏洞共分了以下4种漏洞风险等级。\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"hostLevelList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column prop=\"label\" label=\"危险程度\" width=\"160px\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.label === '可入侵漏洞(严重)'\" style=\"color: #ff4949\">可入侵漏洞(严重)</span>\n                <span v-if=\"scope.row.label === '高'\" style=\"color: #ff4949\">高</span>\n                <span v-if=\"scope.row.label === '中'\" style=\"color: #ffba00\">中</span>\n                <span v-if=\"scope.row.label === '低'\" style=\"color: #13ce66\">低</span>\n              </template>\n            </el-table-column>>\n            <el-table-column prop=\"value\" label=\"危险程度说明\" />\n          </el-table>\n        </div>\n      </el-col>\n    </el-row>\n\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">附录2-主机漏洞加固策略</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          基于多年的安全整改经验，提供了以下5种安全加固整改策略，并对不同整改建议的有效的防护度、整改困难度作了评级参考，有效防护度越高则表示加固效果越好，整改困难度越高则表示整改方案实施越难。您可以根据实际的业务情况，参考以下表，选择加固整改策略。\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"hostStrategyList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"index\" label=\"序号\" width=\"80px\" />\n            <el-table-column prop=\"value\" label=\"加固整改策略\">\n            </el-table-column>>\n            <el-table-column prop=\"label\" label=\"有效防护度\" align=\"center\"  width=\"130px\" />\n            <el-table-column prop=\"level\" label=\"整改困难度\" align=\"center\" width=\"130px\"/>\n          </el-table>\n        </div>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\n  import {\n    hostriskstat,\n    listIpPortresult,\n    listIpTaskresult,\n    listIpWpresult,\n    listVulnresult,\n    listWebresult,\n    middlehighlevelstat\n  } from '@/api/monitor2/wpresult'\n\n  export default {\n    name: 'ffJobTasksDetail',\n    props: {\n      form: {\n        type: Array,\n        default: null\n      },\n      jobRow: {\n        type: Object,\n        default: null\n      },\n      macAipList: {\n        type: Array,\n        default: null\n      }\n    },\n    data() {\n      return {\n        activeNames: ['1', '2', '3', '4'],\n        ls: {\n          'width': '130px',\n        },\n        collapseLabelSpan: 4,\n        collapseContentSpan: 8,\n        levelList: [\n          {\n            label: '高',\n            value: '攻击者可以远程操作系统文件、读写后台数据库、执行任意命令或进行远程拒绝服务攻击。'\n          },\n          {\n            label: '中',\n            value: '攻击者可以利用Web网站攻击其他用户，读取系统文件或后台数据库。'\n          },\n          {\n            label: '低',\n            value: '攻击者可以获取某些系统、网站、文件的信息或冒用身份。'\n          },\n          {\n            label: '风险信息',\n            value: '攻击者可以获取网站相关信息，可能是非敏感信息。'\n          }\n        ],\n        hostLevelList: [\n          {\n            label: '可入侵漏洞(严重)',\n            value: '有公开利用方法，已验证可远程执行任意命令或者代码，或对系统进行远程拒绝服务攻击，或可获取重要敏感效据。此部分漏洞基于漏洞原理验证识别，在漏洞名称中，会标识【原理扫描】。'\n          },\n          {\n            label: '高',\n            value: '攻击者可以远程执行任意命令或者代码，或对系统进行远程拒绝服务攻击，或可获取重要教感数据。此部分漏洞主要通过服务版本识别，可能会有一定的误报。'\n          },\n          {\n            label: '中',\n            value: '攻击者可以远程创建、修改、删除部分文件或数据，或对普通服务进行拒绝服务攻击。此部分温洞主要通过服务版本识别，可能会有一定的误报。'\n          },\n          {\n            label: '低',\n            value: '攻击者可以获取某些系统、服务的信息，或读取某些系统文件和教据。此部分漏洞主要通过服务版本识别，可能会有一定的误报。'\n          }\n        ],\n        hostStrategyList: [\n          {\n            label: '高',\n            level: '高',\n            value: '根据漏洞整改建议打补丁或者修改配置进行安全加固，加固前建议作好相关备份以使回退;建议所有 Windows 系统使用\"Windows Update\"进行更新。'\n          },\n          {\n            label: '高',\n            level: '低',\n            value: '若存在漏洞的应用服务平时不需要使用，建议关闭这些不必要的服务或应用。'\n          },\n          {\n            label: '中',\n            level: '低',\n            value: '若存在漏洞的应用服务不需要对外开放或者只对部分用户开放，建议在主机防火墙上进行访问控制，限定只有合法IP才能访问此应用。'\n          },\n          {\n            label: '低',\n            level: '低',\n            value: '若不便在主机防火墙上配置，建议在网络/出口防火墙上做白名单访问控制。'\n          },\n          {\n            label: '低',\n            level: '中',\n            value: '建议修改应用的banner信息，隐藏应用名称、版本号等信息，让攻击者无法识别目标系统，便之难以进行针对性的攻击入侵。'\n          }\n        ],\n        headerCellStyle: { 'font-weight': 'normal', color: '#979797' },\n        cellStyle: { 'font-weight': 'bold' },\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        queryWpParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        queryPortParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        queryScanParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        jobTaskList: [],\n        wpResultList: [],\n        scanList: [],\n        portResultList: [],\n        holeMapData: [],\n        xAsisArr: [],\n        pieMapData: [],\n        portTotal: 0,\n        wpTotal: 0,\n        total: 0,\n        scanTotal: 0,\n        loading: false\n      }\n    },\n    mounted() {\n      this.getIpList()\n      this.getWpresultList()\n      this.getPortresultList()\n      this.getScanList()\n      this.getMiddlehighlevelstat()\n    },\n    methods: {\n      getIpList() {\n        this.queryParams.taskId = this.form[0].taskId\n        if (this.jobRow.jobType === 1) {\n          listIpTaskresult(this.queryParams).then(response => {\n            this.jobTaskList = response.rows\n            this.total = response.total;\n            this.$nextTick(() => this.getHostriskstat())\n          }).catch(() => {\n          });\n        }\n      },\n      getWpresultList() {\n        this.queryWpParams.taskId = this.form[0].taskId\n        if (this.jobRow.jobType === 1) {\n          listIpWpresult(this.queryWpParams).then(response => {\n            this.wpResultList = response.rows\n            this.wpTotal = response.total;\n          }).catch(() => {\n          });\n        }\n      },\n      getPortresultList() {\n        this.queryPortParams.taskId = this.form[0].taskId\n        if (this.jobRow.jobType === 1) {\n          listIpPortresult(this.queryPortParams).then(response => {\n            this.portResultList = response.rows\n            this.portTotal = response.total;\n          }).catch(() => {\n          });\n        }\n      },\n      getHostriskstat() {\n        if (this.jobRow.jobType === 1) {\n          this.xAsisArr = []\n          this.holeMapData = []\n          if (this.total > 1) {\n            hostriskstat(this.form[0].taskId).then(response => {\n              response.data.forEach(e => {\n                if (e.max_risk_level === 1) {\n                  this.xAsisArr.push('低风险主机数')\n                  const low = {\n                    value: e.host_num,\n                    itemStyle: {\n                      color: '#13ce66'\n                    }\n                  }\n                  this.holeMapData.push(low)\n                }\n                if (e.max_risk_level === 2) {\n                  this.xAsisArr.push('中风险主机数')\n                  const middle = {\n                    value: e.host_num,\n                    itemStyle: {\n                      color: '#ffba00'\n                    }\n                  }\n                  this.holeMapData.push(middle)\n                }\n                if (e.max_risk_level === 3) {\n                  this.xAsisArr.push('高风险主机数')\n                  const high = {\n                    value: e.host_num,\n                    itemStyle: {\n                      color: '#ff4949'\n                    }\n                  }\n                  this.holeMapData.push(high)\n                }\n                if (e.max_risk_level === 4) {\n                  this.xAsisArr.push('可入侵主机数')\n                  const poc = {\n                    value: e.host_num,\n                    itemStyle: {\n                      color: '#1890FF'\n                    }\n                  }\n                  this.holeMapData.push(poc)\n                }\n              })\n              this.$nextTick(() => this.barMapInit())\n            })\n\n          } else {\n            this.xAsisArr.push('可入侵漏洞')\n            const poc = {\n              value: this.form[0].pocRiskNum,\n              itemStyle: {\n                color: '#1890FF'\n              }\n            }\n            this.holeMapData.push(poc)\n\n            this.xAsisArr.push('高风险')\n            const high = {\n              value: this.form[0].highRiskNum,\n              itemStyle: {\n                color: '#ff4949'\n              }\n            }\n            this.holeMapData.push(high)\n            this.xAsisArr.push('中风险')\n            const middle = {\n              value: this.form[0].middleRiskNum,\n              itemStyle: {\n                color: '#ffba00'\n              }\n            }\n            this.holeMapData.push(middle)\n            this.xAsisArr.push('低风险')\n            const low = {\n              value: this.form[0].lowRiskNum,\n              itemStyle: {\n                color: '#13ce66'\n              }\n            }\n            this.holeMapData.push(low)\n            this.barMapInit()\n          }\n        }\n      },\n      getMiddlehighlevelstat() {\n        if (this.jobRow.jobType !== 1) {\n          this.pieMapData =[]\n          middlehighlevelstat(this.form[0].taskId).then(response => {\n            this.pieMapData = response.data\n            this.$nextTick(() => this.pieMapInit())\n          })\n          this.xAsisArr = []\n          this.holeMapData = []\n          this.xAsisArr.push('高风险')\n          const high = {\n            value: this.form[0].highRiskNum,\n            itemStyle: {\n              color: '#ff4949'\n            }\n          }\n          this.holeMapData.push(high)\n          this.xAsisArr.push('中风险')\n          const middle = {\n            value: this.form[0].middleRiskNum,\n            itemStyle: {\n              color: '#ffba00'\n            }\n          }\n          this.holeMapData.push(middle)\n          this.xAsisArr.push('低风险')\n          const low = {\n            value: this.form[0].lowRiskNum,\n            itemStyle: {\n              color: '#13ce66'\n            }\n          }\n          this.holeMapData.push(low)\n          this.xAsisArr.push('风险信息')\n          const poc = {\n            value: this.form[0].infoRiskNum,\n            itemStyle: {\n              color: '#1890FF'\n            }\n          }\n          this.holeMapData.push(poc)\n          this.barMapInit()\n        }\n      },\n      getScanList() {\n        this.xAsisArr = []\n        this.holeMapData = []\n        this.pieMapData = []\n        this.loading = true;\n        this.queryScanParams.taskId = this.form[0].taskId\n        if (this.jobRow.jobType === 1) {\n          listVulnresult(this.queryScanParams).then(response => {\n            this.scanList = response.rows\n            this.scanTotal = response.total;\n            this.loading = false;\n          }).catch(() => {\n            this.loading = false;\n          });\n          const highRisk = {\n            name : '高风险',\n            value: this.form[0].highRiskNum || 0\n          }\n          const middleRisk = {\n            name : '中风险',\n            value: this.form[0].middleRiskNum || 0\n          }\n          const lowRisk = {\n            name : '低风险',\n            value: this.form[0].lowRiskNum || 0\n          }\n          const pocRisk = {\n            name : '可入侵漏洞',\n            value: this.form[0].pocRiskNum || 0\n          }\n          this.pieMapData.push(highRisk)\n          this.pieMapData.push(middleRisk)\n          this.pieMapData.push(lowRisk)\n          this.pieMapData.push(pocRisk)\n          this.pieMapInit()\n\n\n        } else {\n          listWebresult(this.queryScanParams).then(response => {\n            this.scanList = response.rows\n            this.scanList.forEach(e => {\n              e.evidence = this.decodeBase64(e.evidence)\n            })\n            this.scanTotal = response.total;\n            this.loading = false;\n          }).catch(() => {\n            this.loading = false;\n          });\n        }\n\n      },\n      decodeBase64(base64String) {\n        return decodeURIComponent(escape(window.atob(base64String)));\n      },\n      pieMapInit() {\n        const pieMapChar = this.$echarts.init(this.$refs.pieMap)\n        let title = ''\n        if (this.jobRow.jobType === 1) {\n          title =  '风险漏洞比例'\n        } else {\n          title =  '高中风险漏洞类型比例'\n        }\n        pieMapChar.setOption({\n          title: {\n            text: title,\n            left: 'center'\n          },\n          tooltip: {\n            trigger: 'item'\n          },\n          legend: {\n            orient: 'vertical',\n            x: 'right',\n            y: 'center'\n          },\n          color: ['#ff4949','#ffba00','#13ce66','#1890FF','#ea7ccc','#fc8452','#3ba272','#73c0de','#fac858','#91cc75','#ee6666','#9a60b4'],\n          series: [\n            {\n              name: title,\n              type: 'pie',\n              radius: ['40%', '70%'],\n              avoidLabelOverlap: false,\n              itemStyle: {\n                borderRadius: 10,\n                borderColor: '#fff',\n                borderWidth: 2\n              },\n              label: {\n                show: false,\n                position: 'center'\n              },\n              labelLine: {\n                show: false\n              },\n              data: this.pieMapData\n            }\n          ]\n        })\n      },\n      barMapInit() {\n        const holeMapChar = this.$echarts.init(this.$refs.holeMap)\n        let title = ''\n        if (this.jobRow.jobType === 1) {\n          title =  '风险主机数'\n        } else {\n          title =  '漏洞风险级别'\n        }\n        holeMapChar.setOption({\n          title: {\n            text:title,\n            left: 'center'\n          },\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          legend: {\n            orient: 'vertical',\n            x: 'right',\n            y: 'center'\n          },\n          color: ['#1890FF'],\n          grid: {\n            top: 30,\n            left: 20,\n            right: 20,\n            bottom: 10,\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            data: this.xAsisArr\n          },\n          yAxis: {\n            type: 'value',\n            show: false,\n          },\n          series: [\n            {\n              type: 'bar',\n              data: this.holeMapData,\n              barWidth: '30%'\n            }\n          ]\n        })\n      }\n    }\n  }\n</script>\n\n<style scoped>\n  .back_card {\n    background-color: #f3f3f3;\n  }\n\n  .s_d_row {\n    height: 50px;\n    line-height: 50px;\n    color: #1890ff;\n    background-color: #f3f3f3;\n    margin-top: 10px;\n    border-bottom: solid 2px #1890ff;\n  }\n  .s_span_tag {\n    margin-left: 10px;\n    font-weight: 600;\n  }\n  ::v-deep .el-card__body {\n    padding: 15px 20px;\n  }\n  .b_tag {\n    display: inline-block;\n    padding: 0px 20px;\n    font-weight: 600;\n    width: 150px;\n    background-color: #f9f7f7;\n    margin-right: 10px;\n  }\n  .truncate {\n    display: inline-flex;\n    width: 70%; /* 定义容器宽度 */\n    white-space: nowrap; /* 不换行 */\n    overflow: hidden; /* 超出部分隐藏 */\n    text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */\n    text-align: center; /* 文本居中 */\n  }\n</style>\n\n<style lang=\"scss\" scoped>\n  .asset-tag{\n    margin-left: 5px;\n    max-width: 100%;\n    overflow: hidden;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    vertical-align: middle;\n  }\n  .overflow-tag:not(:first-child){\n    margin-top: 5px;\n  }\n  .my-descriptions .fixed-width-label {\n    width: 140px;\n  }\n</style>\n"], "mappings": ";;;;;;;;;;AAwZA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAC,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACAG,UAAA;MACAL,IAAA,EAAAC,KAAA;MACAC,OAAA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,EAAA;QACA;MACA;MACAC,iBAAA;MACAC,mBAAA;MACAC,SAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,aAAA,GACA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAE,gBAAA,GACA;QACAH,KAAA;QACAI,KAAA;QACAH,KAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,KAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,KAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,KAAA;MACA,GACA;QACAD,KAAA;QACAI,KAAA;QACAH,KAAA;MACA,EACA;MACAI,eAAA;QAAA;QAAAC,KAAA;MAAA;MACAC,SAAA;QAAA;MAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,aAAA;QACAF,OAAA;QACAC,QAAA;MACA;MACAE,eAAA;QACAH,OAAA;QACAC,QAAA;MACA;MACAG,eAAA;QACAJ,OAAA;QACAC,QAAA;MACA;MACAI,WAAA;MACAC,YAAA;MACAC,QAAA;MACAC,cAAA;MACAC,WAAA;MACAC,QAAA;MACAC,UAAA;MACAC,SAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,eAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,WAAA;IACA,KAAAC,sBAAA;EACA;EACAC,OAAA;IACAL,SAAA,WAAAA,UAAA;MAAA,IAAAM,KAAA;MACA,KAAAzB,WAAA,CAAA0B,MAAA,QAAA/C,IAAA,IAAA+C,MAAA;MACA,SAAA3C,MAAA,CAAA4C,OAAA;QACA,IAAAC,0BAAA,OAAA5B,WAAA,EAAA6B,IAAA,WAAAC,QAAA;UACAL,KAAA,CAAAnB,WAAA,GAAAwB,QAAA,CAAAC,IAAA;UACAN,KAAA,CAAAV,KAAA,GAAAe,QAAA,CAAAf,KAAA;UACAU,KAAA,CAAAO,SAAA;YAAA,OAAAP,KAAA,CAAAQ,eAAA;UAAA;QACA,GAAAC,KAAA,cACA;MACA;IACA;IACAd,eAAA,WAAAA,gBAAA;MAAA,IAAAe,MAAA;MACA,KAAAhC,aAAA,CAAAuB,MAAA,QAAA/C,IAAA,IAAA+C,MAAA;MACA,SAAA3C,MAAA,CAAA4C,OAAA;QACA,IAAAS,wBAAA,OAAAjC,aAAA,EAAA0B,IAAA,WAAAC,QAAA;UACAK,MAAA,CAAA5B,YAAA,GAAAuB,QAAA,CAAAC,IAAA;UACAI,MAAA,CAAArB,OAAA,GAAAgB,QAAA,CAAAf,KAAA;QACA,GAAAmB,KAAA,cACA;MACA;IACA;IACAb,iBAAA,WAAAA,kBAAA;MAAA,IAAAgB,MAAA;MACA,KAAAjC,eAAA,CAAAsB,MAAA,QAAA/C,IAAA,IAAA+C,MAAA;MACA,SAAA3C,MAAA,CAAA4C,OAAA;QACA,IAAAW,0BAAA,OAAAlC,eAAA,EAAAyB,IAAA,WAAAC,QAAA;UACAO,MAAA,CAAA5B,cAAA,GAAAqB,QAAA,CAAAC,IAAA;UACAM,MAAA,CAAAxB,SAAA,GAAAiB,QAAA,CAAAf,KAAA;QACA,GAAAmB,KAAA,cACA;MACA;IACA;IACAD,eAAA,WAAAA,gBAAA;MAAA,IAAAM,MAAA;MACA,SAAAxD,MAAA,CAAA4C,OAAA;QACA,KAAAhB,QAAA;QACA,KAAAD,WAAA;QACA,SAAAK,KAAA;UACA,IAAAyB,sBAAA,OAAA7D,IAAA,IAAA+C,MAAA,EAAAG,IAAA,WAAAC,QAAA;YACAA,QAAA,CAAA5C,IAAA,CAAAuD,OAAA,WAAAC,CAAA;cACA,IAAAA,CAAA,CAAAC,cAAA;gBACAJ,MAAA,CAAA5B,QAAA,CAAAiC,IAAA;gBACA,IAAAC,GAAA;kBACApD,KAAA,EAAAiD,CAAA,CAAAI,QAAA;kBACAC,SAAA;oBACAjD,KAAA;kBACA;gBACA;gBACAyC,MAAA,CAAA7B,WAAA,CAAAkC,IAAA,CAAAC,GAAA;cACA;cACA,IAAAH,CAAA,CAAAC,cAAA;gBACAJ,MAAA,CAAA5B,QAAA,CAAAiC,IAAA;gBACA,IAAAI,MAAA;kBACAvD,KAAA,EAAAiD,CAAA,CAAAI,QAAA;kBACAC,SAAA;oBACAjD,KAAA;kBACA;gBACA;gBACAyC,MAAA,CAAA7B,WAAA,CAAAkC,IAAA,CAAAI,MAAA;cACA;cACA,IAAAN,CAAA,CAAAC,cAAA;gBACAJ,MAAA,CAAA5B,QAAA,CAAAiC,IAAA;gBACA,IAAAK,IAAA;kBACAxD,KAAA,EAAAiD,CAAA,CAAAI,QAAA;kBACAC,SAAA;oBACAjD,KAAA;kBACA;gBACA;gBACAyC,MAAA,CAAA7B,WAAA,CAAAkC,IAAA,CAAAK,IAAA;cACA;cACA,IAAAP,CAAA,CAAAC,cAAA;gBACAJ,MAAA,CAAA5B,QAAA,CAAAiC,IAAA;gBACA,IAAAM,GAAA;kBACAzD,KAAA,EAAAiD,CAAA,CAAAI,QAAA;kBACAC,SAAA;oBACAjD,KAAA;kBACA;gBACA;gBACAyC,MAAA,CAAA7B,WAAA,CAAAkC,IAAA,CAAAM,GAAA;cACA;YACA;YACAX,MAAA,CAAAP,SAAA;cAAA,OAAAO,MAAA,CAAAY,UAAA;YAAA;UACA;QAEA;UACA,KAAAxC,QAAA,CAAAiC,IAAA;UACA,IAAAM,GAAA;YACAzD,KAAA,OAAAd,IAAA,IAAAyE,UAAA;YACAL,SAAA;cACAjD,KAAA;YACA;UACA;UACA,KAAAY,WAAA,CAAAkC,IAAA,CAAAM,GAAA;UAEA,KAAAvC,QAAA,CAAAiC,IAAA;UACA,IAAAK,IAAA;YACAxD,KAAA,OAAAd,IAAA,IAAA0E,WAAA;YACAN,SAAA;cACAjD,KAAA;YACA;UACA;UACA,KAAAY,WAAA,CAAAkC,IAAA,CAAAK,IAAA;UACA,KAAAtC,QAAA,CAAAiC,IAAA;UACA,IAAAI,MAAA;YACAvD,KAAA,OAAAd,IAAA,IAAA2E,aAAA;YACAP,SAAA;cACAjD,KAAA;YACA;UACA;UACA,KAAAY,WAAA,CAAAkC,IAAA,CAAAI,MAAA;UACA,KAAArC,QAAA,CAAAiC,IAAA;UACA,IAAAC,GAAA;YACApD,KAAA,OAAAd,IAAA,IAAA4E,UAAA;YACAR,SAAA;cACAjD,KAAA;YACA;UACA;UACA,KAAAY,WAAA,CAAAkC,IAAA,CAAAC,GAAA;UACA,KAAAM,UAAA;QACA;MACA;IACA;IACA5B,sBAAA,WAAAA,uBAAA;MAAA,IAAAiC,MAAA;MACA,SAAAzE,MAAA,CAAA4C,OAAA;QACA,KAAAf,UAAA;QACA,IAAA6C,6BAAA,OAAA9E,IAAA,IAAA+C,MAAA,EAAAG,IAAA,WAAAC,QAAA;UACA0B,MAAA,CAAA5C,UAAA,GAAAkB,QAAA,CAAA5C,IAAA;UACAsE,MAAA,CAAAxB,SAAA;YAAA,OAAAwB,MAAA,CAAAE,UAAA;UAAA;QACA;QACA,KAAA/C,QAAA;QACA,KAAAD,WAAA;QACA,KAAAC,QAAA,CAAAiC,IAAA;QACA,IAAAK,IAAA;UACAxD,KAAA,OAAAd,IAAA,IAAA0E,WAAA;UACAN,SAAA;YACAjD,KAAA;UACA;QACA;QACA,KAAAY,WAAA,CAAAkC,IAAA,CAAAK,IAAA;QACA,KAAAtC,QAAA,CAAAiC,IAAA;QACA,IAAAI,MAAA;UACAvD,KAAA,OAAAd,IAAA,IAAA2E,aAAA;UACAP,SAAA;YACAjD,KAAA;UACA;QACA;QACA,KAAAY,WAAA,CAAAkC,IAAA,CAAAI,MAAA;QACA,KAAArC,QAAA,CAAAiC,IAAA;QACA,IAAAC,GAAA;UACApD,KAAA,OAAAd,IAAA,IAAA4E,UAAA;UACAR,SAAA;YACAjD,KAAA;UACA;QACA;QACA,KAAAY,WAAA,CAAAkC,IAAA,CAAAC,GAAA;QACA,KAAAlC,QAAA,CAAAiC,IAAA;QACA,IAAAM,GAAA;UACAzD,KAAA,OAAAd,IAAA,IAAAgF,WAAA;UACAZ,SAAA;YACAjD,KAAA;UACA;QACA;QACA,KAAAY,WAAA,CAAAkC,IAAA,CAAAM,GAAA;QACA,KAAAC,UAAA;MACA;IACA;IACA7B,WAAA,WAAAA,YAAA;MAAA,IAAAsC,MAAA;MACA,KAAAjD,QAAA;MACA,KAAAD,WAAA;MACA,KAAAE,UAAA;MACA,KAAAK,OAAA;MACA,KAAAZ,eAAA,CAAAqB,MAAA,QAAA/C,IAAA,IAAA+C,MAAA;MACA,SAAA3C,MAAA,CAAA4C,OAAA;QACA,IAAAkC,wBAAA,OAAAxD,eAAA,EAAAwB,IAAA,WAAAC,QAAA;UACA8B,MAAA,CAAApD,QAAA,GAAAsB,QAAA,CAAAC,IAAA;UACA6B,MAAA,CAAA5C,SAAA,GAAAc,QAAA,CAAAf,KAAA;UACA6C,MAAA,CAAA3C,OAAA;QACA,GAAAiB,KAAA;UACA0B,MAAA,CAAA3C,OAAA;QACA;QACA,IAAA6C,QAAA;UACArF,IAAA;UACAgB,KAAA,OAAAd,IAAA,IAAA0E,WAAA;QACA;QACA,IAAAU,UAAA;UACAtF,IAAA;UACAgB,KAAA,OAAAd,IAAA,IAAA2E,aAAA;QACA;QACA,IAAAU,OAAA;UACAvF,IAAA;UACAgB,KAAA,OAAAd,IAAA,IAAA4E,UAAA;QACA;QACA,IAAAU,OAAA;UACAxF,IAAA;UACAgB,KAAA,OAAAd,IAAA,IAAAyE,UAAA;QACA;QACA,KAAAxC,UAAA,CAAAgC,IAAA,CAAAkB,QAAA;QACA,KAAAlD,UAAA,CAAAgC,IAAA,CAAAmB,UAAA;QACA,KAAAnD,UAAA,CAAAgC,IAAA,CAAAoB,OAAA;QACA,KAAApD,UAAA,CAAAgC,IAAA,CAAAqB,OAAA;QACA,KAAAP,UAAA;MAGA;QACA,IAAAQ,uBAAA,OAAA7D,eAAA,EAAAwB,IAAA,WAAAC,QAAA;UACA8B,MAAA,CAAApD,QAAA,GAAAsB,QAAA,CAAAC,IAAA;UACA6B,MAAA,CAAApD,QAAA,CAAAiC,OAAA,WAAAC,CAAA;YACAA,CAAA,CAAAyB,QAAA,GAAAP,MAAA,CAAAQ,YAAA,CAAA1B,CAAA,CAAAyB,QAAA;UACA;UACAP,MAAA,CAAA5C,SAAA,GAAAc,QAAA,CAAAf,KAAA;UACA6C,MAAA,CAAA3C,OAAA;QACA,GAAAiB,KAAA;UACA0B,MAAA,CAAA3C,OAAA;QACA;MACA;IAEA;IACAmD,YAAA,WAAAA,aAAAC,YAAA;MACA,OAAAC,kBAAA,CAAAC,MAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAJ,YAAA;IACA;IACAX,UAAA,WAAAA,WAAA;MACA,IAAAgB,UAAA,QAAAC,QAAA,CAAAC,IAAA,MAAAC,KAAA,CAAAC,MAAA;MACA,IAAAC,KAAA;MACA,SAAAhG,MAAA,CAAA4C,OAAA;QACAoD,KAAA;MACA;QACAA,KAAA;MACA;MACAL,UAAA,CAAAM,SAAA;QACAD,KAAA;UACAE,IAAA,EAAAF,KAAA;UACAG,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;QACA;QACAC,MAAA;UACAC,MAAA;UACAC,CAAA;UACAC,CAAA;QACA;QACA1F,KAAA;QACA2F,MAAA,GACA;UACAhH,IAAA,EAAAsG,KAAA;UACAnG,IAAA;UACA8G,MAAA;UACAC,iBAAA;UACA5C,SAAA;YACA6C,YAAA;YACAC,WAAA;YACAC,WAAA;UACA;UACAtG,KAAA;YACAuG,IAAA;YACAC,QAAA;UACA;UACAC,SAAA;YACAF,IAAA;UACA;UACA7G,IAAA,OAAA0B;QACA;MAEA;IACA;IACAuC,UAAA,WAAAA,WAAA;MACA,IAAA+C,WAAA,QAAAvB,QAAA,CAAAC,IAAA,MAAAC,KAAA,CAAAsB,OAAA;MACA,IAAApB,KAAA;MACA,SAAAhG,MAAA,CAAA4C,OAAA;QACAoD,KAAA;MACA;QACAA,KAAA;MACA;MACAmB,WAAA,CAAAlB,SAAA;QACAD,KAAA;UACAE,IAAA,EAAAF,KAAA;UACAG,IAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAgB,WAAA;YACAxH,IAAA;UACA;QACA;QACAyG,MAAA;UACAC,MAAA;UACAC,CAAA;UACAC,CAAA;QACA;QACA1F,KAAA;QACAuG,IAAA;UACAC,GAAA;UACApB,IAAA;UACAqB,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACA9H,IAAA;UACAM,IAAA,OAAAyB;QACA;QACAgG,KAAA;UACA/H,IAAA;UACAmH,IAAA;QACA;QACAN,MAAA,GACA;UACA7G,IAAA;UACAM,IAAA,OAAAwB,WAAA;UACAkG,QAAA;QACA;MAEA;IACA;EACA;AACA", "ignoreList": []}]}