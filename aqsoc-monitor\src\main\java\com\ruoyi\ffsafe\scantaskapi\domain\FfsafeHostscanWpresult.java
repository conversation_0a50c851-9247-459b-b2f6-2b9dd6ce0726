package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class FfsafeHostscanWpresult extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 任务id */
    @Excel(name = "任务id")
    private Integer taskId;

    /** 服务类型 */
    @Excel(name = "服务类型")
    private String serviceType;

    /** 用户名 */
    @Excel(name = "用户名")
    private String username;

    /** 弱口令密码 */
    @Excel(name = "弱口令密码")
    private String weakPassword;

    /** 主机ip */
    @Excel(name = "主机ip")
    private String hostIp;

    /** 主机端口 */
    @Excel(name = "主机端口")
    private Integer hostPort;

    /** 汇总记录ID */
    @Excel(name = "汇总记录ID")
    private Long summaryId;
}
