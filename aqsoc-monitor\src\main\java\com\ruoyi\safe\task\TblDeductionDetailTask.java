package com.ruoyi.safe.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.monitor2.domain.MonitorBssVulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWebvulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWpDeal;
import com.ruoyi.monitor2.service.IMonitorBssVulnDealService;
import com.ruoyi.monitor2.service.IMonitorBssWebvulnDealService;
import com.ruoyi.monitor2.service.IMonitorBssWpDealService;
import com.ruoyi.safe.domain.TblDeductionDetail;
import com.ruoyi.safe.domain.TblThreatDeductionStandard;
import com.ruoyi.safe.mapper.TblDeductionDetailMapper;
import com.ruoyi.safe.service.ITblDeductionDetailService;
import com.ruoyi.safe.service.ITblThreatDeductionStandardService;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.service.ITblThreatenAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TblDeductionDetailTask {
    @Resource(name = "deductionDetailTaskExecutor")
    private ExecutorService deductionDetailTaskExecutor;
    @Resource
    private TblDeductionDetailMapper tblDeductionDetailMapper;
    @Resource
    private ITblDeductionDetailService tblDeductionDetailService;
    @Resource
    private ITblThreatenAlarmService tblThreatenAlarmService;
    @Autowired
    private ITblThreatDeductionStandardService tblThreatDeductionStandardService;
    @Autowired
    private IMonitorBssVulnDealService monitorBssVulnDealService;
    @Autowired
    private IMonitorBssWpDealService monitorBssWpDealService;
    @Autowired
    private IMonitorBssWebvulnDealService monitorBssWebvulnDealService;

    private static final String DEDUCTION_TYPE_HOST_VULN = "主机漏洞";
    private static final String DEDUCTION_TYPE_WEAK_PASSWORD = "弱口令";
    private static final String DEDUCTION_TYPE_WEB_VULN = "WEB漏洞";
    private static final String RISK_TYPE_INTERNAL = "内部漏洞";

    @PostConstruct
    public void init() {
        deductionDetailTaskExecutor.execute(this::synchronizationVulnerabilityRisk);
    }


    public void synchronizationVulnerabilityRisk(){
        log.info("开始处理同步漏洞风险数据");
        //查询扣分记录有无主机风险的数据
        TblDeductionDetail tblDeductionDetail = new TblDeductionDetail();
        tblDeductionDetail.setDeductionType("主机漏洞");
        List<TblDeductionDetail> tblDeductionDetails = tblDeductionDetailMapper.selectTblDeductionDetailList(tblDeductionDetail);
        List<TblThreatDeductionStandard> tblThreatDeductionStandards = tblThreatDeductionStandardService.selectTblThreatDeductionStandardList(new TblThreatDeductionStandard());
        if(CollUtil.isEmpty(tblDeductionDetails)){
            log.info("开始处理同步主机漏洞数据");
            //表示安全评价记录表中无web漏洞相关数据，需要进行旧数据同步
            //查询所有主机风险数据
            List<MonitorBssVulnDeal> monitorBssVulnDeals = monitorBssVulnDealService.selectMonitorBssVulnDealList(new MonitorBssVulnDeal());
            if (CollUtil.isNotEmpty(monitorBssVulnDeals)) {
                List<MonitorBssVulnDeal> vulnDeals = monitorBssVulnDeals.stream()
                        .filter(vulnDeal -> !"1".equals(vulnDeal.getHandleState()) && !"0".equals(vulnDeal.getHandleState()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(vulnDeals)){
                    List<TblDeductionDetail> deductionDetailList = vulnDeals.stream().map(vulnDeal -> {
                        TblDeductionDetail tblDeductionDetailInsert = new TblDeductionDetail();
                        tblDeductionDetailInsert.setDeductionDate(vulnDeal.getCreateTime());
                        tblDeductionDetailInsert.setDeductionType("主机漏洞");
                        tblDeductionDetailInsert.setDeductionLevel(vulnDeal.getSeverity() != null ? vulnDeal.getSeverity().toString() : "");
                        tblDeductionDetailInsert.setRiskType("内部漏洞");
                        tblDeductionDetailInsert.setReferenceId(vulnDeal.getId().toString());
                        tblDeductionDetailService.scoringRuleProcessing(tblDeductionDetailInsert, tblThreatDeductionStandards);
                        return tblDeductionDetailInsert;
                    }).collect(Collectors.toList());
                    List<List<TblDeductionDetail>> split = CollUtil.split(deductionDetailList, 1000);
                    split.forEach(list -> {
                        tblDeductionDetailMapper.batchInsert(list);
                    });
                }
            }
            log.info("主机漏洞数据同步完成");
        }
        //查询扣分记录有无弱口令风险的数据
        tblDeductionDetail.setDeductionType("弱口令");
        List<TblDeductionDetail> weakPasswordsDeductionDetails = tblDeductionDetailMapper.selectTblDeductionDetailList(tblDeductionDetail);
        if (CollUtil.isEmpty(weakPasswordsDeductionDetails)){
            log.info("开始处理同步弱口令数据");
            //评分记录中没有弱口令风险数据，需要进行旧数据同步
            //查询所有弱口令数据
            List<MonitorBssWpDeal> monitorBssWpDealList = monitorBssWpDealService.selectMonitorBssWpDealList(new MonitorBssWpDeal());
            if (CollUtil.isNotEmpty(monitorBssWpDealList)){
                List<MonitorBssWpDeal> wpDeals = monitorBssWpDealList.stream()
                        .filter(wpDeal -> !"1".equals(wpDeal.getHandleState().toString()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(wpDeals)){
                    List<TblDeductionDetail> deductionDetailList = wpDeals.stream().map(wpDeal -> {
                        TblDeductionDetail tblDeductionDetailInsert = new TblDeductionDetail();
                        tblDeductionDetailInsert.setDeductionDate(wpDeal.getCreateTime());
                        tblDeductionDetailInsert.setDeductionType("弱口令");
                        tblDeductionDetailInsert.setDeductionLevel("2");
                        tblDeductionDetailInsert.setRiskType("内部漏洞");
                        tblDeductionDetailInsert.setReferenceId(wpDeal.getId().toString());
                        tblDeductionDetailService.scoringRuleProcessing(tblDeductionDetailInsert, tblThreatDeductionStandards);
                        return tblDeductionDetailInsert;
                    }).collect(Collectors.toList());
                    List<List<TblDeductionDetail>> split = CollUtil.split(deductionDetailList, 1000);
                    split.forEach(list -> {
                        tblDeductionDetailMapper.batchInsert(list);
                    });
                }
            }
            log.info("弱口令数据同步完成");
        }
        //查询扣分记录有无Web漏洞风险的数据
        tblDeductionDetail.setDeductionType("WEB漏洞");
        List<TblDeductionDetail> WEBLeakDetails = tblDeductionDetailMapper.selectTblDeductionDetailList(tblDeductionDetail);
        if (CollUtil.isEmpty(WEBLeakDetails)){
            log.info("开始处理同步Web漏洞数据");
            //评分记录中没有WEB漏洞风险数据，需要进行旧数据同步
            List<MonitorBssWebvulnDeal> monitorBssWebLeakDeals = monitorBssWebvulnDealService.selectMonitorBssWebvulnDealList(new MonitorBssWebvulnDeal());
            if (CollUtil.isNotEmpty(monitorBssWebLeakDeals)){
                List<MonitorBssWebvulnDeal> webLeakDeals = monitorBssWebLeakDeals.stream()
                        .filter(webLeakDeal -> !"1".equals(webLeakDeal.getHandleState().toString()) && webLeakDeal.getSeverity() != 0)
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(webLeakDeals)){
                    List<TblDeductionDetail> deductionDetailList = webLeakDeals.stream().map(webLeakDeal -> {
                        TblDeductionDetail tblDeductionDetailInsert = new TblDeductionDetail();
                        tblDeductionDetailInsert.setDeductionDate(webLeakDeal.getCreateTime());
                        tblDeductionDetailInsert.setDeductionType("WEB漏洞");
                        tblDeductionDetailInsert.setDeductionLevel(webLeakDeal.getSeverity() != null ? webLeakDeal.getSeverity().toString() : "");
                        tblDeductionDetailInsert.setRiskType("内部漏洞");
                        tblDeductionDetailInsert.setReferenceId(webLeakDeal.getId().toString());
                        tblDeductionDetailService.scoringRuleProcessing(tblDeductionDetailInsert, tblThreatDeductionStandards);
                        return tblDeductionDetailInsert;
                    }).collect(Collectors.toList());
                    List<List<TblDeductionDetail>> split = CollUtil.split(deductionDetailList, 1000);
                    split.forEach(list -> {
                        tblDeductionDetailMapper.batchInsert(list);
                    });
                }
            }
        }
        log.info("web漏洞数据同步完成");
    }

    /**
     * 同步威胁告警
     */
    public synchronized void syncThreaten(){
        Long threatenAlarmId = 0L;
        //查询最后一条外部威胁扣分记录
        TblDeductionDetail last = tblDeductionDetailService.selectLastThreatenDeductionDetail();
        if(last != null && StrUtil.isNotBlank(last.getReferenceId())){
            //有记录，获取ID
            threatenAlarmId = Long.valueOf(last.getReferenceId());
        }
        //查询还没有同步的威胁告警
        List<TblThreatenAlarm> alarmList = tblThreatenAlarmService.selectListByGreaterThanId(threatenAlarmId);
        if(CollUtil.isNotEmpty(alarmList)){
            List<TblThreatDeductionStandard> tblThreatDeductionStandards = tblThreatDeductionStandardService.selectTblThreatDeductionStandardList(new TblThreatDeductionStandard());
            log.info("处理威胁告警扣分开始: " + alarmList.size());
            List<TblDeductionDetail> saveList = new ArrayList<>(alarmList.size());
            for (TblThreatenAlarm alarm : alarmList) {
                TblDeductionDetail tblDeductionDetail = new TblDeductionDetail();
                tblDeductionDetail.setDeductionDate(alarm.getCreateTime());
                tblDeductionDetail.setDeductionType(alarm.getTypeSegStr());
                tblDeductionDetail.setDeductionLevel(alarm.getAlarmLevel() != null ? alarm.getAlarmLevel().toString() : "");
                tblDeductionDetail.setRiskType("外部威胁");
                tblDeductionDetail.setReferenceId(alarm.getId().toString());
                tblDeductionDetailService.scoringRuleProcessing(tblDeductionDetail, tblThreatDeductionStandards);
                if(tblDeductionDetail.getDeductionScore() != null){
                    saveList.add(tblDeductionDetail);
                }
            }
            List<List<TblDeductionDetail>> split = CollUtil.split(saveList, 1000);
            split.forEach(list -> {
                tblDeductionDetailService.batchInsert(list);
            });
        }
        log.info("处理威胁告警扣分结束");
    }
}
