package com.ruoyi.safe.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.monitor2.domain.MonitorBssVulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWebvulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWpDeal;
import com.ruoyi.monitor2.mapper.MonitorBssWpDealMapper;
import com.ruoyi.monitor2.service.IMonitorBssWebvulnDealService;
import com.ruoyi.monitor2.service.impl.MonitorBssWebvulnDealServiceImpl;
import com.ruoyi.safe.domain.NetworkIpMacInfo;
import com.ruoyi.safe.domain.TblBusinessApplication;
import com.ruoyi.safe.domain.TblThreatDeductionStandard;
import com.ruoyi.safe.service.ITblBusinessApplicationService;
import com.ruoyi.safe.service.ITblNetworkIpMacService;
import com.ruoyi.safe.service.ITblThreatDeductionStandardService;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.safe.mapper.TblDeductionDetailMapper;
import com.ruoyi.safe.domain.TblDeductionDetail;
import com.ruoyi.safe.service.ITblDeductionDetailService;

import javax.annotation.Resource;

/**
 * 扣分详情记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Service
public class TblDeductionDetailServiceImpl implements ITblDeductionDetailService
{
    @Autowired
    private TblDeductionDetailMapper tblDeductionDetailMapper;

    @Autowired
    private ITblThreatDeductionStandardService tblThreatDeductionStandardService;

    @Resource
    private ITblBusinessApplicationService businessApplicationService;
    @Resource
    private ITblNetworkIpMacService netIpMacService;
    @Autowired
    private MonitorBssWpDealMapper monitorBssWpDealMapper;

    /**
     * 查询扣分详情记录
     *
     * @param id 扣分详情记录主键
     * @return 扣分详情记录
     */
    @Override
    public TblDeductionDetail selectTblDeductionDetailById(Long id)
    {
        return tblDeductionDetailMapper.selectTblDeductionDetailById(id);
    }

    /**
     * 批量查询扣分详情记录
     *
     * @param ids 扣分详情记录主键集合
     * @return 扣分详情记录集合
     */
    @Override
    public List<TblDeductionDetail> selectTblDeductionDetailByIds(Long[] ids)
    {
        return tblDeductionDetailMapper.selectTblDeductionDetailByIds(ids);
    }

    /**
     * 查询扣分详情记录列表
     *
     * @param tblDeductionDetail 扣分详情记录
     * @return 扣分详情记录
     */
    @Override
    public List<TblDeductionDetail> selectTblDeductionDetailList(TblDeductionDetail tblDeductionDetail)
    {
        handlePermissionControlParameters(tblDeductionDetail);
        if (tblDeductionDetail.getIsPage() != null && tblDeductionDetail.getIsPage()){
            PageUtils.startPage();
        }
        return tblDeductionDetailMapper.selectTblDeductionDetailList(tblDeductionDetail);
    }

    public void handlePermissionControlParameters(TblDeductionDetail tblDeductionDetail) {
        if (tblDeductionDetail.getDeptId() != null && tblDeductionDetail.getDeptId() != 100L){
            tblDeductionDetail.setDepartmentId(tblDeductionDetail.getDeptId());
        }
        //ip漏洞权限查询
        MonitorBssVulnDeal monitorBssVulnDeal = new MonitorBssVulnDeal();
        monitorBssVulnDeal.setDeptId(tblDeductionDetail.getDepartmentId());
        handleAssetInfo(monitorBssVulnDeal);
        tblDeductionDetail.setHotsIpv4List(monitorBssVulnDeal.getIpv4List());
        //弱口令权限查询
        MonitorBssWpDeal monitorBssWpDeal = new MonitorBssWpDeal();
        monitorBssWpDeal.setDeptId(tblDeductionDetail.getDepartmentId());
        List<MonitorBssWpDeal> monitorBssWpDeals = monitorBssWpDealMapper.selectMonitorBssWpDealList(monitorBssWpDeal);
        if (CollUtil.isNotEmpty(monitorBssWpDeals)){
            List<String> stringList = monitorBssWpDeals.stream().map(monitorBssWpDealItem -> {
                return monitorBssWpDealItem.getId().toString();
            }).collect(Collectors.toList());
            tblDeductionDetail.setWeakPasswordsIdList(stringList);
        }
        //web漏洞权限权限查询
        MonitorBssWebvulnDeal monitorBssWebvulnDeal = new MonitorBssWebvulnDeal();
        monitorBssWebvulnDeal.setDeptId(tblDeductionDetail.getDepartmentId());
        IMonitorBssWebvulnDealService monitorBssWebvulnDealService = SpringUtil.getBean(IMonitorBssWebvulnDealService.class);
        List<MonitorBssWebvulnDeal> monitorBssWebvulnDealList = monitorBssWebvulnDealService.selectMonitorBssWebvulnDealList(monitorBssWebvulnDeal);
        if (CollUtil.isNotEmpty(monitorBssWebvulnDealList)){
            List<String> stringList = monitorBssWebvulnDealList.stream().map(monitorBssWebvulnDealItem -> {
                return monitorBssWebvulnDealItem.getId().toString();
            }).collect(Collectors.toList());
            tblDeductionDetail.setWebLeakIdList(stringList);
        }
        //威胁告警权限查询
        TblThreatenAlarm tblThreatenAlarm = new TblThreatenAlarm();
        tblThreatenAlarm.setDeptId(tblDeductionDetail.getDepartmentId());
        handleAssetInfo(tblThreatenAlarm);
        tblDeductionDetail.setThreatIpv4List(tblThreatenAlarm.getIpv4List());
    }

    private List<NetworkIpMacInfo> handleAssetInfo(TblThreatenAlarm threatenAlarm){
        //查询netIpMac
        JSONObject queryAssetInfoParams = new JSONObject();
        queryAssetInfoParams.put("domainId",threatenAlarm.getDomainId());
        queryAssetInfoParams.put("deptId",threatenAlarm.getDeptId());
        queryAssetInfoParams.put("applicationId",threatenAlarm.getApplicationId());
        List<NetworkIpMacInfo> assetInfoList = netIpMacService.selectAssetInfoList(queryAssetInfoParams);
        if(CollUtil.isNotEmpty(assetInfoList)){
            threatenAlarm.setIpv4List(assetInfoList.stream().map(NetworkIpMacInfo::getIpv4).collect(Collectors.toList()));
            //查询业务系统
            List<Long> businessAssetIds = new ArrayList<>();
            assetInfoList.forEach(assetInfo -> {
                String assetIdList = assetInfo.getAssetIdList();
                if(StrUtil.isNotBlank(assetIdList)){
                    businessAssetIds.addAll(StrUtil.split(assetIdList, ",").stream().map(Long::valueOf).collect(Collectors.toList()));
                }
            });
            if(CollUtil.isNotEmpty(businessAssetIds)){
                List<TblBusinessApplication> applicationList = businessApplicationService.selectByAssetIds(CollUtil.distinct(businessAssetIds));
                if(CollUtil.isNotEmpty(applicationList)){
                    assetInfoList.forEach(assetInfo -> {
                        String assetIdList = assetInfo.getAssetIdList();
                        if(StrUtil.isNotBlank(assetIdList)){
                            List<TblBusinessApplication> matchAppList = applicationList.stream().filter(application -> assetIdList.contains(application.getAssetId().toString())).collect(Collectors.toList());
                            assetInfo.setBusinessApplications(matchAppList);
                        }
                    });
                }
            }
        }
        /*if((threatenAlarm.getApplicationId() != null || threatenAlarm.getDeptId() != null || StrUtil.isNotBlank(threatenAlarm.getDomainId())) && CollUtil.isEmpty(threatenAlarm.getIpv4List())){
            threatenAlarm.setIpv4List(CollUtil.toList("noIP"));
        }*/
        boolean isAll = false;
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser != null){
            SysUser user = loginUser.getUser();
            isAll = user.haveAllData();
        }
        if(CollUtil.isEmpty(threatenAlarm.getIpv4List()) && !isAll){
            threatenAlarm.setIpv4List(CollUtil.toList("noIP"));
        }
        if((threatenAlarm.getApplicationId() == null && threatenAlarm.getDeptId() == null && StrUtil.isBlank(threatenAlarm.getDomainId())) && isAll){
            threatenAlarm.setIpv4List(null);
        }
        return assetInfoList;
    }

    public List<NetworkIpMacInfo> handleAssetInfo(MonitorBssVulnDeal monitorBssVulnDeal){
        //查询netIpMac
        JSONObject queryAssetInfoParams = new JSONObject();
        queryAssetInfoParams.put("domainId",monitorBssVulnDeal.getDomainId());
        queryAssetInfoParams.put("deptId",monitorBssVulnDeal.getDeptId());
        queryAssetInfoParams.put("applicationId",monitorBssVulnDeal.getApplicationId());
        List<NetworkIpMacInfo> assetInfoList = netIpMacService.selectAssetInfoList(queryAssetInfoParams);
        if(CollUtil.isNotEmpty(assetInfoList)){
            monitorBssVulnDeal.setIpv4List(assetInfoList.stream().map(NetworkIpMacInfo::getIpv4).collect(Collectors.toList()));
            //查询业务系统
            List<Long> businessAssetIds = new ArrayList<>();
            assetInfoList.forEach(assetInfo -> {
                String assetIdList = assetInfo.getAssetIdList();
                if(StrUtil.isNotBlank(assetIdList)){
                    businessAssetIds.addAll(StrUtil.split(assetIdList, ",").stream().map(Long::valueOf).collect(Collectors.toList()));
                }
            });
            if(CollUtil.isNotEmpty(businessAssetIds)){
                List<TblBusinessApplication> applicationList = businessApplicationService.selectByAssetIds(CollUtil.distinct(businessAssetIds));
                if(CollUtil.isNotEmpty(applicationList)){
                    assetInfoList.forEach(assetInfo -> {
                        String assetIdList = assetInfo.getAssetIdList();
                        if(StrUtil.isNotBlank(assetIdList)){
                            List<TblBusinessApplication> matchAppList = applicationList.stream().filter(application -> assetIdList.contains(application.getAssetId().toString())).collect(Collectors.toList());
                            assetInfo.setBusinessApplications(matchAppList);
                        }
                    });
                }
            }
        }

        boolean isAll = false;
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser != null){
            SysUser user = loginUser.getUser();
            isAll = user.haveAllData();
        }
        if(CollUtil.isEmpty(monitorBssVulnDeal.getIpv4List()) && !isAll){
            monitorBssVulnDeal.setIpv4List(CollUtil.toList("noIP"));
        }
        if((monitorBssVulnDeal.getApplicationId() == null && monitorBssVulnDeal.getDeptId() == null && StrUtil.isBlank(monitorBssVulnDeal.getDomainId())) && isAll){
            monitorBssVulnDeal.setIpv4List(null);
        }
        return assetInfoList;
    }

    /**
     * 新增扣分详情记录
     *
     * @param tblDeductionDetail 扣分详情记录
     * @return 结果
     */
    @Override
    public int insertTblDeductionDetail(TblDeductionDetail tblDeductionDetail)
    {
        return tblDeductionDetailMapper.insertTblDeductionDetail(tblDeductionDetail);
    }

    /**
     * 修改扣分详情记录
     *
     * @param tblDeductionDetail 扣分详情记录
     * @return 结果
     */
    @Override
    public int updateTblDeductionDetail(TblDeductionDetail tblDeductionDetail)
    {
        return tblDeductionDetailMapper.updateTblDeductionDetail(tblDeductionDetail);
    }

    /**
     * 删除扣分详情记录信息
     *
     * @param id 扣分详情记录主键
     * @return 结果
     */
    @Override
    public int deleteTblDeductionDetailById(Long id)
    {
        return tblDeductionDetailMapper.deleteTblDeductionDetailById(id);
    }

    /**
     * 批量删除扣分详情记录
     *
     * @param ids 需要删除的扣分详情记录主键
     * @return 结果
     */
    @Override
    public int deleteTblDeductionDetailByIds(Long[] ids)
    {
        return tblDeductionDetailMapper.deleteTblDeductionDetailByIds(ids);
    }

    @Override
    public void scoringRuleProcessing(TblDeductionDetail tblDeductionDetail,List<TblThreatDeductionStandard> tblThreatDeductionStandardList) {
        if (CollUtil.isEmpty(tblThreatDeductionStandardList)) {
            return; // 没有找到对应标准，直接返回
        }

        List<TblThreatDeductionStandard> tblThreatDeductionStandards = tblThreatDeductionStandardList.stream()
                .filter(standard -> standard.getContentName().equals(tblDeductionDetail.getRiskType()) && standard.getType().equals(tblDeductionDetail.getDeductionType())).collect(Collectors.toList());
        if(CollUtil.isEmpty(tblThreatDeductionStandards)){
            return; // 没有找到对应标准，直接返回
        }
        TblThreatDeductionStandard threatDeductionStandardInfo = tblThreatDeductionStandards.get(0);
        if (threatDeductionStandardInfo == null) {
            throw new IllegalStateException("获取到的扣分标准信息为空");
        }

        String deductionLevel = tblDeductionDetail.getDeductionLevel();
        String distributionFactor = "";
        String levelDesc = "";

        if ("内部漏洞".equals(tblDeductionDetail.getRiskType())) {
            switch (deductionLevel) {
                case "1":
                    distributionFactor = threatDeductionStandardInfo.getLow();
                    levelDesc = "低危";
                    break;
                case "2":
                    distributionFactor = threatDeductionStandardInfo.getMediumHazard();
                    levelDesc = "中危";
                    break;
                case "3":
                    distributionFactor = threatDeductionStandardInfo.getHigh();
                    levelDesc = "高危";
                    break;
                case "4":
                    distributionFactor = threatDeductionStandardInfo.getCritical();
                    levelDesc = "严重";
                    break;
                default:
                    log.info("未知的漏洞等级：" + deductionLevel);
            }
        } else if ("外部威胁".equals(tblDeductionDetail.getRiskType())) {
            switch (deductionLevel) {
                case "2":
                    distributionFactor = threatDeductionStandardInfo.getLow();
                    levelDesc = "低危";
                    break;
                case "3":
                    distributionFactor = threatDeductionStandardInfo.getMediumHazard();
                    levelDesc = "中危";
                    break;
                case "4":
                    distributionFactor = threatDeductionStandardInfo.getHigh();
                    levelDesc = "高危";
                    break;
                case "5":
                    distributionFactor = threatDeductionStandardInfo.getCritical();
                    levelDesc = "严重";
                    break;
                default:
                    log.info("未知的漏洞等级：" + deductionLevel);
            }
        } else {
            throw new IllegalArgumentException("未知的风险类型：" + tblDeductionDetail.getRiskType());
        }

        if (distributionFactor.isEmpty()) {
            throw new IllegalStateException("无法获取有效的扣分系数");
        }

        double multiplier;
        try {
            multiplier = getMultiplier(distributionFactor);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("扣分系数格式错误：" + distributionFactor, e);
        }

        BigDecimal deductionPerEvent = threatDeductionStandardInfo.getDeductionPerEvent();
        tblDeductionDetail.setDeductionLevel(levelDesc);
        tblDeductionDetail.setDeductionScore(deductionPerEvent.multiply(new BigDecimal(multiplier)));
        tblDeductionDetail.setIsDel("1");
        //tblDeductionDetailMapper.insertTblDeductionDetail(tblDeductionDetail);
    }

    @Override
    public void deleteTblDeductionDetailByDeductionDetail(TblDeductionDetail tblDeductionDetail) {
        tblDeductionDetailMapper.deleteTblDeductionDetailByDeductionDetail(tblDeductionDetail);
    }

    @Override
    public List<JSONObject> getDeductionTotalList(TblDeductionDetail tblDeductionDetail) {
        handlePermissionControlParameters(tblDeductionDetail);
        return tblDeductionDetailMapper.getDeductionTotalList(tblDeductionDetail);
    }

    public static double getMultiplier(String percentage) {
        if (percentage == null || !percentage.endsWith("%")) {
            throw new IllegalArgumentException("输入必须是有效的百分比格式（例如：100%）");
        }

        String numberPart = percentage.substring(0, percentage.length() - 1); // 去掉%
        try {
            double value = Double.parseDouble(numberPart);
            return value / 100.0;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("百分比格式不正确: " + numberPart);
        }
    }

    @Override
    public TblDeductionDetail selectLastThreatenDeductionDetail() {
        return tblDeductionDetailMapper.selectLastThreatenDeductionDetail();
    }

    @Override
    public void batchInsert(List<TblDeductionDetail> saveList) {
        tblDeductionDetailMapper.batchInsert(saveList);
    }
}
