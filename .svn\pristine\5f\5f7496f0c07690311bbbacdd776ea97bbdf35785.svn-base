package com.ruoyi.monitor2.changting.client;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.ffsafe.api.domain.*;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.ffsafe.api.mapper.FfsafeFlowRiskAssetsMapper;
import com.ruoyi.ffsafe.api.service.IApiResultSevice;
import com.ruoyi.ffsafe.component.FFSafeRequestComponent;
import com.ruoyi.threaten.domain.TblThreatenAlarm;


@Service
public class FfsafeClientService {
    private Logger logger = LoggerFactory.getLogger(FfsafeClientService.class);

    @Autowired
    IApiResultSevice apiResultSevice;
    @Resource
    private FFSafeRequestComponent ffSafeRequestComponent;
    @Resource
    private SqlSessionFactory sqlSessionFactory;
    @Resource
    private FfsafeFlowRiskAssetsMapper ffsafeFlowRiskAssetsMapper;
    public static ThreadLocal<TblDeviceConfig> deviceConfigThreadLocal = new ThreadLocal<>();

    private String getPathParam(String path, String param, FfsafeApiConfig ffsafeApiConfig) {
        String temp = ffsafeApiConfig.getUrl();
        temp += path;
        temp += "?access_token=" + ffsafeApiConfig.getToken();
        temp += param;
        return temp;
    }


    private HttpRequestBase getFlowDetailRequestBase(FlowDetailParam flowDetailParam, FfsafeApiConfig ffsafeApiConfig) {
        String param = flowDetailParam.toString();
        String path = "/v2/flow-alarm-detail";
        String url = getPathParam(path, param, ffsafeApiConfig);

        return new HttpGet(url);
    }

    private HttpRequestBase getIpFilterRequestBase(IpFilterParam ipFilterParam, FfsafeApiConfig ffsafeApiConfig) {
        String param = ipFilterParam.toString();
        String path = "/v2/flow-bypass-filtering-log";
        String url = getPathParam(path, param, ffsafeApiConfig);

        return new HttpGet(url);
    }

    private HttpRequestBase getWebMonitorDetailRequestBase(WebMonitorDetailParam webMonitorDetailParam, FfsafeApiConfig ffsafeApiConfig) {
        String param = webMonitorDetailParam.toString();
        String path = "/v1/web-monitor-summary/detail";
        String url = getPathParam(path, param, ffsafeApiConfig);
        return new HttpGet(url);
    }

    private HttpRequestBase getFlowRiskAssetsRequestBase(FlowRiskAssetsParam flowRiskAssetsParam, FfsafeApiConfig ffsafeApiConfig) {
        return flowRiskAssetsParam.getRequestBase(flowRiskAssetsParam.getDeviceConfigId());
    }

    /* 不分页处理 */
    public boolean pullFlowDetailLog(FlowDetailParam flowDetailParam, FfsafeApiConfig ffsafeApiConfig) {
        CloseableHttpClient httpClient = null;
        HttpResponse response =null;
        FlowDetailResult flowDetailResult = new FlowDetailResult();
        boolean bRet = true;

        try {
            httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();
            //String param = "start_time=2024-09-03%2011%3A30%3A00&end_time=2024-09-04%2011%3A30%3A00&page=1";

            HttpRequestBase requestBase = getFlowDetailRequestBase(flowDetailParam, ffsafeApiConfig);
            response = httpClient.execute(requestBase);

            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    String result = EntityUtils.toString(resEntity, "utf-8");
                    int nRet = flowDetailResult.parseResult(result);
                    if (nRet == 0) {
                        return false;
                    }
                    if (nRet == -1) {  // msg = 'fail' 跳过
                        logger.warn("非凡数据接入返回异常： param info: " + flowDetailParam.toString());
                        return true;
                    }
                }
            }

            Map<String, TblThreatenAlarm> threatenAlarmMap = flowDetailResult.getThreatenAlarmMap();
            Date lastDataTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, flowDetailParam.getEndTime());
            bRet = apiResultSevice.dealFlowDetailResult(flowDetailResult.getFlowDetailLogList(), threatenAlarmMap, lastDataTime);
            /*
            if ((flowDetailResult.getFlowDetailLogList() != null) && (flowDetailResult.getFlowDetailLogList().size() > 0)) {
                // long begin = System.currentTimeMillis();
                Map<String, TblThreatenAlarm> threatenAlarmMap = flowDetailResult.getThreatenAlarmMap();
                Date lastDataTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, flowDetailParam.getEndTime());
                bRet = apiResultSevice.dealFlowDetailResult(flowDetailResult.getFlowDetailLogList(), threatenAlarmMap, lastDataTime);
                //long end = System.currentTimeMillis();
                //System.err.println("use time: " + (end - begin)/1000 + " list size: " + flowDetailResult.getFlowDetailLogList().size() + " map size: " + threatenAlarmMap.size());
            } */
        } catch (Exception e) {
            e.printStackTrace();
            logger.warn("pullFlowDetailLog： param info: " + flowDetailParam.toString() + " err info: " + e.getMessage());
        } finally {
            deviceConfigThreadLocal.remove();
            try {
                if (null != httpClient)
                    httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
                logger.warn("pullFlowDetailLog： param info: " + flowDetailParam.toString() + " err info: " + e.getMessage());
            }
        }
        return bRet;
    }

    /*  分页处理  目前非凡接口有问题。 暂不使用
    public boolean pullFlowDetailLog(FlowDetailParam flowDetailParam) {
        CloseableHttpClient httpClient = null;
        HttpResponse response =null;
        HttpGet httpGet = null;
        FlowDetailResult flowDetailResult = new FlowDetailResult();
        boolean bRet = true;

        try {
            httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();
            //String param = "start_time=2024-09-03%2011%3A30%3A00&end_time=2024-09-04%2011%3A30%3A00&page=1";
            boolean bFinish = false;
            while (!bFinish) {
                long iBegin = System.currentTimeMillis();
                HttpRequestBase requestBase = getHttpRequestBaseByParam(flowDetailParam);
                response = httpClient.execute(requestBase);
                long iEnd = System.currentTimeMillis();
                System.err.println("inteface time: " + (iEnd - iBegin));
                if (response != null) {
                    HttpEntity resEntity = response.getEntity();
                    if (resEntity != null) {
                        String result = EntityUtils.toString(resEntity, "utf-8");
                        int nRet = flowDetailResult.parseResult(result);
                        if (nRet == 0) {
                            break;
                        }
                        flowDetailParam = flowDetailParam.getNextPageParam();
                        bFinish = flowDetailResult.checkFinish();
                    }
                }

            }
            if ((flowDetailResult.getFlowDetailLogList() != null) && (flowDetailResult.getFlowDetailLogList().size() > 0)) {
                long begin = System.currentTimeMillis();
                Map<String, TblThreatenAlarm> threatenAlarmMap = flowDetailResult.getThreatenAlarmMap();
                Date lastDataTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, flowDetailParam.getEndTime());
                bRet = flowDetailResultSevice.dealFlowDetailResult(flowDetailResult.getFlowDetailLogList(), threatenAlarmMap, lastDataTime);
                long end = System.currentTimeMillis();
                System.err.println("use time: " + (end - begin)/1000 + " list size: " + flowDetailResult.getFlowDetailLogList().size() + " map size: " + threatenAlarmMap.size());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return bRet;
    }
   */

    public boolean pullIpFilterLog(IpFilterParam ipFilterParam, FfsafeApiConfig ffsafeApiConfig) {
        CloseableHttpClient httpClient = null;
        HttpResponse response =null;
        ObjectMapper objectMapper = new ObjectMapper();
        boolean bRet = false;

        try {
            httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();
            HttpRequestBase requestBase = getIpFilterRequestBase(ipFilterParam, ffsafeApiConfig);
            response = httpClient.execute(requestBase);

            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    String result = EntityUtils.toString(resEntity, "utf-8");
                    List<IpFilterLogResult> ipFilterLogResultList = objectMapper.readValue(result,  new TypeReference<List<IpFilterLogResult>>() {});
                    List<FfsafeIpfilterLog> ffsafeIpfilterLogList = new ArrayList<FfsafeIpfilterLog>();
                    ipFilterLogResultList.forEach(element -> {
                        ffsafeIpfilterLogList.add(element.toIpFilterLog());
                    });

                    Date lastDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, ipFilterParam.getEndTime());

                    /*if(CollUtil.isNotEmpty(ffsafeIpfilterLogList)){
                        //查询阻断中列表
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("start_time",ipFilterParam.getStartTime());
                        jsonObject.put("end_time",ipFilterParam.getEndTime());
                        String body = ffSafeRequestComponent.sendPostRequest(FFSafeRequestComponent.BYPASS_FILTER_BLOCKING_LOG_URL, jsonObject, null);
                        if(StrUtil.isNotBlank(body) && JSON.isValidArray(body)){
                            List<JSONObject> blockingList = JSON.parseArray(body, JSONObject.class);
                            ffsafeIpfilterLogList.forEach(ffsafeIpfilterLog -> {
                                if("阻断".equals(ffsafeIpfilterLog.getAction())){
                                    blockingList.stream().filter(blocking -> blocking.getString("block_ip").equals(ffsafeIpfilterLog.getIp()))
                                            .findFirst().ifPresent(blocking -> ffsafeIpfilterLog.setOutId(blocking.getString("id")));
                                }
                            });
                        }
                    }*/
                    bRet = apiResultSevice.dealIpFilterLogResult(ffsafeIpfilterLogList, lastDateTime);
                }
                return bRet;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.warn("pullIpFilterLog： param info: " + ipFilterParam.toString() + " err info: " + e.getMessage());
        } finally {
            try {
                if (null != httpClient)
                    httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
                logger.warn("pullIpFilterLog： param info: " + ipFilterParam.toString() + " err info: " + e.getMessage());
            }
        }
        return bRet;
    }

    private boolean dealWebMonitorDetailResult(int type, String result, Date endDate) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        if (type == 1) {   // WebTamper
            WebTamperDetail webTamperDetail = objectMapper.readValue(result, WebTamperDetail.class);
            return apiResultSevice.dealWebtamperResult(type, webTamperDetail, endDate);
        }
        if (type == 2) {   // WebUsable
            List<WebUsableDetail> webUsableDetailList = objectMapper.readValue(result, new TypeReference<List<WebUsableDetail>>() {});
            return apiResultSevice.dealWebusableResult(type, webUsableDetailList, endDate);
        }
        if (type == 3) {   // hijacked
            List<WebHijackedDetail> webHijackedDetailList = objectMapper.readValue(result, new TypeReference<List<WebHijackedDetail>>() {});
            return apiResultSevice.dealWebhijackResult(type, webHijackedDetailList, endDate);
        }
        if (type == 4) {
            List<WebVulnDetail> webVulnDetailList = objectMapper.readValue(result, new TypeReference<List<WebVulnDetail>>() {});
            return true;
        }
        if (type == 5) {
            List<WebSensitiveWordDetail> webSensitiveWordDetailList = objectMapper.readValue(result, new TypeReference<List<WebSensitiveWordDetail>>() {});
            return apiResultSevice.dealWebSensitiveWordDetail(type, webSensitiveWordDetailList, endDate);
        }
        if (type == 6) {
            List<WebSensitiveFileDetail> webSensitiveFileDetailList = objectMapper.readValue(result, new TypeReference<List<WebSensitiveFileDetail>>() {});
            return apiResultSevice.dealWebSensitiveFileDetail(type, webSensitiveFileDetailList, endDate);
        }
        if (type == 7) {
            List<WebBlackChainDetail> webBlackChainDetailList = objectMapper.readValue(result, new TypeReference<List<WebBlackChainDetail>>() {});
            return apiResultSevice.dealWebBlackchainDetail(type, webBlackChainDetailList, endDate);
        }
        return false;
    }

    public boolean pullWebMonitorDetail(WebMonitorDetailParam webMonitorDetailParam, FfsafeApiConfig ffsafeApiConfig) {
        CloseableHttpClient httpClient = null;
        HttpResponse response =null;
        IpFilterLogResult ipFilterLogResult;
        ObjectMapper objectMapper = new ObjectMapper();
        boolean bRet = false;

        try {
            httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();
            HttpRequestBase requestBase = getWebMonitorDetailRequestBase(webMonitorDetailParam, ffsafeApiConfig);
            response = httpClient.execute(requestBase);

            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    String result = EntityUtils.toString(resEntity, "utf-8");
                    bRet = dealWebMonitorDetailResult(webMonitorDetailParam.getType(), result, webMonitorDetailParam.getEndDate());
                    /*
                    List<IpFilterLogResult> ipFilterLogResultList = objectMapper.readValue(result,  new TypeReference<List<IpFilterLogResult>>() {});
                    List<FfsafeIpfilterLog> ffsafeIpfilterLogList = new ArrayList<FfsafeIpfilterLog>();
                    ipFilterLogResultList.forEach(element -> {
                        ffsafeIpfilterLogList.add(element.toIpFilterLog());
                    });

                    Date lastDateTime = DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, ipFilterParam.getEndTime());
                    bRet = apiResultSevice.dealIpFilterLogResult(ffsafeIpfilterLogList, lastDateTime); */
                }
                return bRet;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.warn("pullWebMonitorDetail： param info: " + webMonitorDetailParam.toString() + " err info: " + e.getMessage());
        } finally {
            try {
                if (null != httpClient)
                    httpClient.close();
            } catch (Exception e) {
                e.printStackTrace();
                logger.warn("pullWebMonitorDetail： param info: " + webMonitorDetailParam.toString() + " err info: " + e.getMessage());
            }
        }
        return bRet;
    }

    /**
     * 拉取流量风险资产数据 - 支持分页获取所有数据，改进重试和错误处理机制
     */
    public boolean pullFlowRiskAssets(FlowRiskAssetsParam flowRiskAssetsParam, FfsafeApiConfig ffsafeApiConfig) {
        CloseableHttpClient httpClient = null;
        ObjectMapper objectMapper = new ObjectMapper();
        boolean bRet = false;

        // 记录开始时间用于日志
        long startTime = System.currentTimeMillis();
        logger.info("开始拉取流量风险资产数据，参数：{}", flowRiskAssetsParam);

        try {
            httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();

            // 用于存储所有页面的数据
            List<FfsafeFlowRiskAssets> allEntityList = new ArrayList<>();
            int totalRecords = 0;
            int currentPage = flowRiskAssetsParam.getPage();
            boolean hasMoreData = true;
            int maxRetries = 3; // 最大重试次数
            int consecutiveEmptyPages = 0; // 连续空页面计数
            int maxConsecutiveEmptyPages = 3; // 最大连续空页面数

            // 分页循环获取所有数据
            while (hasMoreData) {
                boolean pageProcessed = false;

                // 对每一页进行重试机制
                for (int retryCount = 0; retryCount < maxRetries && !pageProcessed; retryCount++) {
                    try {
                        flowRiskAssetsParam.setPage(currentPage);
                        HttpRequestBase requestBase = getFlowRiskAssetsRequestBase(flowRiskAssetsParam, ffsafeApiConfig);
                        HttpResponse response = httpClient.execute(requestBase);

                        if (response == null) {
                            logger.warn("HTTP响应为空，当前页: {}, 重试次数: {}", currentPage, retryCount + 1);
                            if (retryCount < maxRetries - 1) {
                                waitForRetry();
                                continue;
                            } else {
                                logger.error("HTTP响应为空，当前页: {}, 已达最大重试次数", currentPage);
                                return false;
                            }
                        }

                        HttpEntity resEntity = response.getEntity();
                        if (resEntity == null) {
                            logger.warn("响应实体为空，当前页: {}, 重试次数: {}", currentPage, retryCount + 1);
                            if (retryCount < maxRetries - 1) {
                                waitForRetry();
                                continue;
                            } else {
                                logger.error("响应实体为空，当前页: {}, 已达最大重试次数", currentPage);
                                return false;
                            }
                        }

                        String result = EntityUtils.toString(resEntity, "utf-8");
                        JSONObject json = new JSONObject(result);

                        // 处理无msg字段的情况
                        if (!json.has("msg")) {
                            String errMsg = json.has("message") ? json.getString("message") : "未知错误";
                            logger.warn("FlowRiskAssetsResult parse warning! message: {}, 当前页: {}, 重试次数: {}",
                                errMsg, currentPage, retryCount + 1);

                            if(errMsg.contains("无数据") || errMsg.contains("暂无数据")) {
                                logger.info("接口返回无数据，当前页: {}", currentPage);
                                consecutiveEmptyPages++;
                                if (consecutiveEmptyPages >= maxConsecutiveEmptyPages) {
                                    logger.info("连续{}页无数据，停止获取", maxConsecutiveEmptyPages);
                                    hasMoreData = false;
                                }
                                pageProcessed = true;
                                break;
                            } else if (retryCount < maxRetries - 1) {
                                logger.warn("接口返回错误，等待重试");
                                waitForRetry();
                                continue;
                            } else {
                                logger.error("接口返回错误，已达最大重试次数，错误信息: {}", errMsg);
                                return false;
                            }
                        }

                        String msg = json.getString("msg");
                        if ("fail".equals(msg)) {
                            if (retryCount < maxRetries - 1) {
                                logger.warn("接口返回fail，当前页: {}, 等待重试", currentPage);
                                waitForRetry();
                                continue;
                            } else {
                                logger.error("接口返回fail，当前页: {}, 已达最大重试次数", currentPage);
                                return false;
                            }
                        }

                        if (!"ok".equals(msg)) {
                            if (retryCount < maxRetries - 1) {
                                logger.warn("FlowRiskAssetsResult error: {}, 当前页: {}, 等待重试", msg, currentPage);
                                waitForRetry();
                                continue;
                            } else {
                                logger.error("FlowRiskAssetsResult error: {}, 当前页: {}, 已达最大重试次数", msg, currentPage);
                                return false;
                            }
                        }

                        // 解析成功的数据
                        FlowRiskAssetsResult flowRiskAssetsResult = objectMapper.readValue(result, FlowRiskAssetsResult.class);
                        if ("ok".equals(flowRiskAssetsResult.getMsg()) && flowRiskAssetsResult.getAlert() != null) {

                            // 第一次请求时记录总数
                            if (currentPage == 1) {
                                totalRecords = flowRiskAssetsResult.getTotal() != null ? flowRiskAssetsResult.getTotal() : 0;
                                logger.info("流量风险资产总数: {}", totalRecords);
                            }

                            // 处理当前页数据
                            for (FlowRiskAssetsResult.FlowRiskAssetDetail detail : flowRiskAssetsResult.getAlert()) {
                                FfsafeFlowRiskAssets entity = detail.toEntity();

                                // 设置风险类型字典值
                                entity.setRiskType(mapRiskTypeToDictValue(entity.getRiskType()));

                                // 设置默认处置状态为未处置
                                entity.setHandleState(0); // 未处置

                                allEntityList.add(entity);
                            }

                            // 重置连续空页面计数
                            consecutiveEmptyPages = 0;

                            // 判断是否还有更多数据
                            int currentPageSize = flowRiskAssetsResult.getAlert().size();
                            int pageSize = flowRiskAssetsParam.getPageSize();

                            logger.info("第{}页获取{}条数据", currentPage, currentPageSize);

                            // 改进的分页终止逻辑
                            if (currentPageSize == 0) {
                                logger.info("当前页无数据，停止获取");
                                hasMoreData = false;
                            } else if (currentPageSize < pageSize) {
                                logger.info("当前页数据量({})少于页大小({})，已获取完所有数据", currentPageSize, pageSize);
                                hasMoreData = false;
                            } else if (currentPage >= 10000) { // 防止无限循环的保护机制
                                logger.warn("已达到最大页数限制(10000页)，停止获取");
                                hasMoreData = false;
                            } else {
                                currentPage++;
                                logger.info("继续获取第{}页数据", currentPage);
                                // 避免请求过于频繁
                                Thread.sleep(100);
                            }
                        } else {
                            logger.warn("流量风险资产API返回异常，当前页: {}, 结果: {}", currentPage, result);
                            consecutiveEmptyPages++;
                            if (consecutiveEmptyPages >= maxConsecutiveEmptyPages) {
                                hasMoreData = false;
                            }
                        }

                        pageProcessed = true;

                    } catch (Exception e) {
                        if (retryCount < maxRetries - 1) {
                            logger.warn("处理第{}页数据时发生异常，等待重试: {}", currentPage, e.getMessage());
                            waitForRetry();
                        } else {
                            logger.error("处理第{}页数据时发生异常，已达最大重试次数: {}", currentPage, e.getMessage(), e);
                            return false;
                        }
                    }
                }

                // 如果页面未成功处理且未设置hasMoreData为false，则页面跳过
                if (!pageProcessed && hasMoreData) {
                    logger.warn("第{}页处理失败，跳到下一页", currentPage);
                    currentPage++;
                    consecutiveEmptyPages++;
                    if (consecutiveEmptyPages >= maxConsecutiveEmptyPages) {
                        logger.warn("连续{}页处理失败，停止获取", maxConsecutiveEmptyPages);
                        hasMoreData = false;
                    }
                }
            }

            // 处理所有收集到的数据
            if (!allEntityList.isEmpty()) {
                // 批量处理，每批最多500条
                boolean processResult = true;
                int batchSize = 500;
                for (int i = 0; i < allEntityList.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, allEntityList.size());
                    List<FfsafeFlowRiskAssets> batchEntityList = allEntityList.subList(i, endIndex);

                    // 处理一批数据
                    boolean batchResult = processBatch(batchEntityList);
                    if (!batchResult) {
                        processResult = false;
                        logger.warn("批量处理数据失败，批次: {}-{}", i, endIndex - 1);
                    }
                }

                bRet = processResult;
                long endTime = System.currentTimeMillis();
                logger.info("拉取流量风险资产数据完成，耗时{}ms，共{}页，共{}条数据，处理结果: {}",
                    endTime - startTime, currentPage, allEntityList.size(), processResult);
            } else {
                logger.info("拉取流量风险资产数据成功，但没有有效数据");
                bRet = true;
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("拉取流量风险资产数据失败，耗时{}ms", endTime - startTime, e);
            bRet = false;
        } finally {
            closeHttpClient(httpClient);
        }

        return bRet;
    }

    /**
     * 处理一批流量风险资产数据
     *
     * @param batchEntityList 实体列表
     * @return 处理结果
     */
    private boolean processBatch(List<FfsafeFlowRiskAssets> batchEntityList) {
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            FfsafeFlowRiskAssetsMapper mapper = sqlSession.getMapper(FfsafeFlowRiskAssetsMapper.class);

            // 分离特殊风险类型和普通风险类型
            List<FfsafeFlowRiskAssets> specialRiskList = new ArrayList<>();
            List<FfsafeFlowRiskAssets> normalRiskList = new ArrayList<>();

            TblDeviceConfig deviceConfig = FfsafeClientService.deviceConfigThreadLocal.get();
            for (FfsafeFlowRiskAssets entity : batchEntityList) {
                if(deviceConfig != null){
                    entity.setDeviceConfigId(deviceConfig.getId());
                }
                if (isSpecialRiskType(entity.getRiskType())) {
                    specialRiskList.add(entity);
                } else {
                    normalRiskList.add(entity);
                }
            }

            // 分离需要插入和更新的记录
            List<FfsafeFlowRiskAssets> insertList = new ArrayList<>();
            List<FfsafeFlowRiskAssets> updateList = new ArrayList<>();

            // 处理普通风险类型（完全匹配）
            if (!normalRiskList.isEmpty()) {
                List<FfsafeFlowRiskAssets> existingNormalList = mapper.selectByMultipleFields(normalRiskList);
                Map<String, Long> existingNormalKeyMap = existingNormalList.stream()
                    .collect(Collectors.toMap(
                        entity -> generateUniqueKey(entity),
                        FfsafeFlowRiskAssets::getId,
                        (existing, replacement) -> existing
                    ));

                for (FfsafeFlowRiskAssets entity : normalRiskList) {
                    String entityKey = generateUniqueKey(entity);
                    if (existingNormalKeyMap.containsKey(entityKey)) {
                        entity.setId(existingNormalKeyMap.get(entityKey));
                        updateList.add(entity);
                    } else {
                        insertList.add(entity);
                    }
                }
            }

            // 处理特殊风险类型（需要比较riskInfo内容）
            if (!specialRiskList.isEmpty()) {
                List<FfsafeFlowRiskAssets> existingSpecialList = mapper.selectByPartialFields(specialRiskList);

                for (FfsafeFlowRiskAssets entity : specialRiskList) {
                    boolean isExisting = false;

                    for (FfsafeFlowRiskAssets existing : existingSpecialList) {
                        if (isSameEntity(entity, existing)) {
                            // 匹配到重复记录
                            entity.setId(existing.getId());
                            updateList.add(entity);
                            isExisting = true;
                            break;
                        }
                    }

                    if (!isExisting) {
                        insertList.add(entity);
                    }
                }
            }

            // 批量插入
            if (!insertList.isEmpty()) {
                mapper.batchInsertFfsafeFlowRiskAssets(insertList);
                logger.info("批量插入{}条流量风险资产数据", insertList.size());
            }

            // 批量更新
            if (!updateList.isEmpty()) {
                // 分批更新，避免单次更新数据量过大
                int updateBatchSize = 200;
                for (int i = 0; i < updateList.size(); i += updateBatchSize) {
                    int endIndex = Math.min(i + updateBatchSize, updateList.size());
                    List<FfsafeFlowRiskAssets> updateBatch = updateList.subList(i, endIndex);
                    mapper.batchUpdateFfsafeFlowRiskAssets(updateBatch);
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
                logger.info("批量更新{}条流量风险资产数据", updateList.size());
            }

            // 提交事务
            sqlSession.commit();
            return true;
        } catch (Exception e) {
            if (sqlSession != null) {
                sqlSession.rollback();
            }
            logger.error("处理流量风险资产数据失败", e);
            return false;
        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }
    }

    /**
     * 生成唯一标识：riskAssets + riskType + riskInfo + engineName + startTime
     */
    private String generateUniqueKey(FfsafeFlowRiskAssets entity) {
        return String.join("|",
            entity.getRiskAssets() != null ? entity.getRiskAssets() : "",
            entity.getRiskType() != null ? entity.getRiskType() : "",
            entity.getRiskInfo() != null ? entity.getRiskInfo() : "",
            entity.getEngineName() != null ? entity.getEngineName() : "",
            entity.getStartTime() != null ? entity.getStartTime().toString() : ""
        );
    }

    /**
     * 判断是否为特殊风险类型（需要特殊处理riskInfo比较的类型）
     */
    private boolean isSpecialRiskType(String riskType) {
        return "weak_password".equals(riskType) || "sensitive_info".equals(riskType);
    }

    /**
     * 判断两个实体是否为同一条记录
     * 对于特殊风险类型，需要比较riskInfo内容是否一致（忽略顺序）
     */
    private boolean isSameEntity(FfsafeFlowRiskAssets entity1, FfsafeFlowRiskAssets entity2) {
        // 首先比较基础字段
        if (!Objects.equals(entity1.getRiskAssets(), entity2.getRiskAssets()) ||
            !Objects.equals(entity1.getRiskType(), entity2.getRiskType()) ||
            !Objects.equals(entity1.getEngineName(), entity2.getEngineName()) ||
            !Objects.equals(entity1.getStartTime(), entity2.getStartTime())) {
            return false;
        }

        // 对于特殊风险类型，比较riskInfo内容
        if (isSpecialRiskType(entity1.getRiskType())) {
            return isRiskInfoContentEqual(entity1.getRiskInfo(), entity2.getRiskInfo());
        }

        // 普通类型直接比较riskInfo
        return Objects.equals(entity1.getRiskInfo(), entity2.getRiskInfo());
    }

    /**
     * 比较两个riskInfo的内容是否一致（忽略顺序）
     * 解析格式如：['账号:18770257520, 密码:******', '账号:admin, 密码:******']
     */
    private boolean isRiskInfoContentEqual(String riskInfo1, String riskInfo2) {
        if (Objects.equals(riskInfo1, riskInfo2)) {
            return true;
        }

        if (riskInfo1 == null || riskInfo2 == null) {
            return false;
        }

        try {
            Set<String> set1 = parseRiskInfoToSet(riskInfo1);
            Set<String> set2 = parseRiskInfoToSet(riskInfo2);

            // 如果解析后的Set都为空，但原字符串不为空，说明解析失败，回退到字符串比较
            if (set1.isEmpty() && set2.isEmpty() &&
                !riskInfo1.trim().isEmpty() && !riskInfo2.trim().isEmpty()) {
                logger.warn("riskInfo解析后为空，回退到字符串比较: riskInfo1={}, riskInfo2={}", riskInfo1, riskInfo2);
                return riskInfo1.equals(riskInfo2);
            }

            return set1.equals(set2);
        } catch (Exception e) {
            logger.warn("解析riskInfo失败，使用字符串直接比较: {}", e.getMessage());
            return riskInfo1.equals(riskInfo2);
        }
    }

    /**
     * 解析riskInfo字符串为Set集合
     * 输入格式：['账号:18770257520, 密码:******', '账号:admin, 密码:******']
     */
    private Set<String> parseRiskInfoToSet(String riskInfo) {
        Set<String> resultSet = new HashSet<>();

        if (riskInfo == null || riskInfo.trim().isEmpty()) {
            return resultSet;
        }

        try {
            // 首先尝试使用JSON解析
            ObjectMapper objectMapper = new ObjectMapper();
            List<String> itemList = objectMapper.readValue(riskInfo, List.class);
            resultSet.addAll(itemList);
        } catch (Exception jsonException) {
            // JSON解析失败时，使用正则表达式解析
            logger.debug("JSON解析riskInfo失败，使用正则表达式解析: {}", jsonException.getMessage());

            try {
                String content = riskInfo.trim();
                if (content.startsWith("[") && content.endsWith("]")) {
                    content = content.substring(1, content.length() - 1);
                }

                // 使用正则表达式匹配单引号包裹的内容
                String pattern = "'([^']*)'";
                java.util.regex.Pattern compiledPattern = java.util.regex.Pattern.compile(pattern);
                java.util.regex.Matcher matcher = compiledPattern.matcher(content);

                while (matcher.find()) {
                    String item = matcher.group(1).trim();
                    if (!item.isEmpty()) {
                        resultSet.add(item);
                    }
                }
            } catch (Exception regexException) {
                // 正则表达式解析也失败，记录警告并返回空Set
                logger.warn("正则表达式解析riskInfo也失败，返回空集合: JSON异常={}, 正则异常={}, 原始数据={}",
                    jsonException.getMessage(), regexException.getMessage(), riskInfo);
            }
        }

        return resultSet;
    }

    /**
     * 重试等待
     */
    private void waitForRetry() {
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("重试等待被中断", e);
        }
    }

    /**
     * 关闭HTTP客户端
     */
    private void closeHttpClient(CloseableHttpClient httpClient) {
        if (httpClient != null) {
            try {
                httpClient.close();
            } catch (Exception e) {
                logger.error("关闭HTTP客户端失败", e);
            }
        }
    }

    /**
     * 映射风险类型到字典值
     */
    private String mapRiskTypeToDictValue(String riskType) {
        if (riskType == null) {
            return null;
        }

        if (riskType.contains("弱口令账号")) {
            return "weak_password";
        } else if (riskType.contains("敏感信息")) {
            return "sensitive_info";
        } else if (riskType.contains("高危风险资产") || riskType.contains("高危资产")) {
            return "high_risk_assets";
        }

        return riskType; // 保持原值
    }

    public static void main(String[] args) {
        FfsafeClientService ffsafeClientService = new FfsafeClientService();
        //ffsafeClientService.setUrlToken("https://***************:23000","lst2BAzxJTQL0eSZ");

        /*
        FlowDetailParam flowDetailParam = new FlowDetailParam();
        flowDetailParam.setStartTime("2024-09-03 11:30:00");
        flowDetailParam.setEndTime("2024-09-03 11:31:00");
        flowDetailParam.setPage(1);

        ffsafeClientService.pullFlowDetailLog(flowDetailParam); */

        IpFilterParam ipFilterParam = new IpFilterParam();
        ipFilterParam.setStartTime("2024-10-01 00:00:01");
        ipFilterParam.setEndTime("2024-11-01 00:00:01");
        //ffsafeClientService.pullIpFilterLog(ipFilterParam);

    }

}
