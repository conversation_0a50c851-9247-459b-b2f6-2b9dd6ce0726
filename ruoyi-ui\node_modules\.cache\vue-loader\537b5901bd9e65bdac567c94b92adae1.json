{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorIp.vue?vue&type=template&id=77ac47f0&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorIp.vue", "mtime": 1755768894570}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}