package com.ruoyi.monitor2.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;

/**
 * <AUTHOR>
 * @version 1.0
 * @project distributedNmap
 * @description:
 * @date 2025-08-07 11:42
 */
public class AesUtil {
    private static final AES NMAP_AES = SecureUtil.aes("4jRBNanhrC-893Xg".getBytes());
    public static String NmapAesEncrypt(String content) {
        if(StrUtil.isBlank(content)){
            return null;
        }
        return NMAP_AES.encryptHex(content);
    }

    public static String NmapAesDecrypt(String content) {
        if(StrUtil.isBlank(content)){
            return null;
        }
        return NMAP_AES.decryptStr(content);
    }

    public static void main(String[] args) {
        System.out.println(NmapAesEncrypt("123123"));
    }
}
