<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeScanReportRecordMapper">

    <resultMap type="FfsafeScanReportRecord" id="FfsafeScanReportRecordResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="generateTime"    column="generate_time"    />
        <result property="generateSource"    column="generate_source"    />
        <result property="reportType"    column="report_type"    />
        <result property="reportId"    column="report_id"    />
        <result property="reportStatus"    column="report_status"    />
        <result property="fileName"    column="file_name"    />
        <result property="downName"    column="down_name"    />
        <result property="reportPercent"    column="report_percent"    />
        <result property="minioPath"    column="minio_path"    />
    </resultMap>

    <!-- 报表记录与任务信息复合对象的结果映射 -->
    <resultMap type="com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecordWithTaskInfo" id="FfsafeScanReportRecordWithTaskInfoResult">
        <!-- 来自 ffsafe_scan_report_record 表的字段 -->
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="generateTime"    column="generate_time"    />
        <result property="generateSource"    column="generate_source"    />
        <result property="reportType"    column="report_type"    />
        <result property="reportId"    column="report_id"    />
        <result property="reportStatus"    column="report_status"    />
        <result property="fileName"    column="file_name"    />
        <result property="downName"    column="down_name"    />
        <result property="reportPercent"    column="report_percent"    />
        <result property="minioPath"    column="minio_path"    />
        <!-- 来自 ffsafe_scantask_summary 表的字段 -->
        <result property="jobId"    column="job_id"    />
        <result property="taskType"    column="task_type"    />
        <result property="taskStatus"    column="task_status"    />
        <result property="finishRate"    column="finish_rate"    />
        <result property="originalTaskId"    column="original_task_id"    />
    </resultMap>

    <resultMap type="FfsafeScanReportRecordVO" id="FfsafeScanReportRecordDetailResult" extends="FfsafeScanReportRecordResult">
        <result property="generateStatusDesc"    column="generate_status_desc"    />
        <result property="reportTypeDesc"    column="report_type_desc"    />
        <result property="generateSourceDesc"    column="generate_source_desc"    />
        <result property="reportPercentDisplay"    column="report_percent_display"    />
        <result property="targetCount"    column="target_count"    />
        <result property="fileSize"    column="file_size"    />
        <result property="duration"    column="duration"    />
    </resultMap>

    <sql id="selectFfsafeScanReportRecordVo">
        select id, create_time, generate_time, generate_source, report_type, report_id, report_status, file_name, down_name, report_percent, minio_path from ffsafe_scan_report_record
    </sql>

    <select id="selectFfsafeScanReportRecordList" parameterType="FfsafeScanReportRecord" resultMap="FfsafeScanReportRecordResult">
        SELECT
            r.id,
            r.create_time,
            r.generate_time,
            r.generate_source,
            r.report_type,
            r.report_id,
            r.report_status,
            r.file_name,
            r.down_name,
            r.report_percent,
            r.minio_path,
            GROUP_CONCAT(
                CASE
                    WHEN j.invoke_target LIKE '%|%' THEN
                        SUBSTRING_INDEX(SUBSTRING_INDEX(j.invoke_target, '|', 2), '|', -1)
                    ELSE
                        j.invoke_target
                END
                ORDER BY rel.task_summary_id
                SEPARATOR ';'
            ) as scan_target
        FROM ffsafe_scan_report_record r
        LEFT JOIN ffsafe_scan_report_task_relation rel ON r.id = rel.scan_report_record_id
        LEFT JOIN ffsafe_scantask_summary s ON rel.task_summary_id = s.id
        LEFT JOIN sys_job j ON s.job_id = j.job_id
        <where>
            <if test="scanTarget != null and scanTarget != ''"> and j.invoke_target LIKE CONCAT('%', #{scanTarget}, '%')</if>
            <if test="generateSource != null"> and r.generate_source = #{generateSource}</if>
            <if test="reportType != null"> and r.report_type = #{reportType}</if>
            <if test="reportId != null"> and r.report_id = #{reportId}</if>
            <if test="reportStatus != null"> and r.report_status = #{reportStatus}</if>
            <if test="fileName != null and fileName != ''"> and r.file_name LIKE CONCAT('%', #{fileName}, '%')</if>
        </where>
        GROUP BY r.id, r.create_time, r.generate_time, r.generate_source, r.report_type, r.report_id, r.report_status, r.file_name, r.down_name, r.report_percent, r.minio_path
        ORDER BY r.create_time DESC
    </select>

    <select id="selectFfsafeScanReportRecordById" parameterType="Long" resultMap="FfsafeScanReportRecordResult">
        <include refid="selectFfsafeScanReportRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeScanReportRecordByReportId" parameterType="Integer" resultMap="FfsafeScanReportRecordResult">
        <include refid="selectFfsafeScanReportRecordVo"/>
        where report_id = #{reportId}
    </select>

    <select id="selectFfsafeScanReportRecordByGenerateSource" parameterType="Integer" resultMap="FfsafeScanReportRecordResult">
        <include refid="selectFfsafeScanReportRecordVo"/>
        where generate_source = #{generateSource}
        order by create_time desc
    </select>

    <select id="selectFfsafeScanReportRecordDetailList" parameterType="FfsafeScanReportRecordQueryParam" resultMap="FfsafeScanReportRecordDetailResult">
        SELECT
            r.id,
            GROUP_CONCAT(
                CASE
                    WHEN j.invoke_target LIKE '%|%' THEN
                        SUBSTRING_INDEX(SUBSTRING_INDEX(j.invoke_target, '|', 2), '|', -1)
                    ELSE
                        j.invoke_target
                END
                ORDER BY rel.task_summary_id
                SEPARATOR ';'
            ) as scan_target,
            r.create_time,
            r.generate_time,
            r.generate_source,
            r.report_type,
            r.report_id,
            r.report_status,
            r.file_name,
            r.down_name,
            r.report_percent,
            r.minio_path,
            CASE r.report_status
                WHEN 0 THEN '生成中'
                WHEN 1 THEN '生成完毕'
                WHEN 2 THEN '下载完毕'
                ELSE '未知状态'
            END as generate_status_desc,
            CASE r.report_type
                WHEN 1 THEN 'web漏扫报表'
                WHEN 2 THEN '主机漏扫报表'
                ELSE '未知类型'
            END as report_type_desc,
            CASE r.generate_source
                WHEN 1 THEN '单条生成'
                WHEN 2 THEN '批量生成'
                ELSE '未知入口'
            END as generate_source_desc,
            CONCAT(COALESCE(r.report_percent, 0), '%') as report_percent_display,
            CASE
                WHEN GROUP_CONCAT(
                    CASE
                        WHEN j.invoke_target LIKE '%|%' THEN
                            SUBSTRING_INDEX(SUBSTRING_INDEX(j.invoke_target, '|', 2), '|', -1)
                        ELSE
                            j.invoke_target
                    END
                    ORDER BY rel.task_summary_id
                    SEPARATOR ';'
                ) IS NULL OR GROUP_CONCAT(
                    CASE
                        WHEN j.invoke_target LIKE '%|%' THEN
                            SUBSTRING_INDEX(SUBSTRING_INDEX(j.invoke_target, '|', 2), '|', -1)
                        ELSE
                            j.invoke_target
                    END
                    ORDER BY rel.task_summary_id
                    SEPARATOR ';'
                ) = '' THEN 0
                ELSE (LENGTH(GROUP_CONCAT(
                    CASE
                        WHEN j.invoke_target LIKE '%|%' THEN
                            SUBSTRING_INDEX(SUBSTRING_INDEX(j.invoke_target, '|', 2), '|', -1)
                        ELSE
                            j.invoke_target
                    END
                    ORDER BY rel.task_summary_id
                    SEPARATOR ';'
                )) - LENGTH(REPLACE(GROUP_CONCAT(
                    CASE
                        WHEN j.invoke_target LIKE '%|%' THEN
                            SUBSTRING_INDEX(SUBSTRING_INDEX(j.invoke_target, '|', 2), '|', -1)
                        ELSE
                            j.invoke_target
                    END
                    ORDER BY rel.task_summary_id
                    SEPARATOR ';'
                ), ';', '')) + 1)
            END as target_count,
            '' as file_size,
            CASE
                WHEN r.generate_time IS NOT NULL AND r.create_time IS NOT NULL
                THEN CONCAT(TIMESTAMPDIFF(MINUTE, r.create_time, r.generate_time), '分钟')
                ELSE ''
            END as duration
        FROM ffsafe_scan_report_record r
        LEFT JOIN ffsafe_scan_report_task_relation rel ON r.id = rel.scan_report_record_id
        LEFT JOIN ffsafe_scantask_summary s ON rel.task_summary_id = s.id
        LEFT JOIN sys_job j ON s.job_id = j.job_id
        <where>
            <if test="reportType != null"> and r.report_type = #{reportType}</if>
            <if test="scanTarget != null and scanTarget != ''"> and j.invoke_target LIKE CONCAT('%', #{scanTarget}, '%')</if>
            <if test="reportStatus != null"> and r.report_status = #{reportStatus}</if>
            <if test="generateSource != null"> and r.generate_source = #{generateSource}</if>
        </where>
        GROUP BY r.id, r.create_time, r.generate_time, r.generate_source, r.report_type, r.report_id, r.report_status, r.file_name, r.down_name, r.report_percent, r.minio_path
        ORDER BY r.create_time DESC
    </select>

    <insert id="insertFfsafeScanReportRecord" parameterType="FfsafeScanReportRecord" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_scan_report_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="generateTime != null">generate_time,</if>
            <if test="generateSource != null">generate_source,</if>
            <if test="reportType != null">report_type,</if>
            <if test="reportId != null">report_id,</if>
            <if test="reportStatus != null">report_status,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="downName != null and downName != ''">down_name,</if>
            <if test="reportPercent != null">report_percent,</if>
            <if test="minioPath != null and minioPath != ''">minio_path,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="generateTime != null">#{generateTime},</if>
            <if test="generateSource != null">#{generateSource},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="reportId != null">#{reportId},</if>
            <if test="reportStatus != null">#{reportStatus},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="downName != null and downName != ''">#{downName},</if>
            <if test="reportPercent != null">#{reportPercent},</if>
            <if test="minioPath != null and minioPath != ''">#{minioPath},</if>
        </trim>
    </insert>

    <update id="updateFfsafeScanReportRecord" parameterType="FfsafeScanReportRecord">
        update ffsafe_scan_report_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="generateTime != null">generate_time = #{generateTime},</if>
            <if test="generateSource != null">generate_source = #{generateSource},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="reportId != null">report_id = #{reportId},</if>
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="downName != null and downName != ''">down_name = #{downName},</if>
            <if test="reportPercent != null">report_percent = #{reportPercent},</if>
            <if test="minioPath != null and minioPath != ''">minio_path = #{minioPath},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateFfsafeScanReportRecordByReportId" parameterType="FfsafeScanReportRecord">
        update ffsafe_scan_report_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="reportStatus != null">report_status = #{reportStatus},</if>
            <if test="reportPercent != null">report_percent = #{reportPercent},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="downName != null and downName != ''">down_name = #{downName},</if>
            <if test="minioPath != null and minioPath != ''">minio_path = #{minioPath},</if>
            <if test="generateTime != null">generate_time = #{generateTime},</if>
        </trim>
        where report_id = #{reportId}
    </update>

    <delete id="deleteFfsafeScanReportRecordById" parameterType="Long">
        delete from ffsafe_scan_report_record where id = #{id}
    </delete>

    <delete id="deleteFfsafeScanReportRecordByIds" parameterType="String">
        delete from ffsafe_scan_report_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
