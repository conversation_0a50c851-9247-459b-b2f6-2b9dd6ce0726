#!/bin/bash

# ============================================================================
# Docker镜像激进模式备份脚本 (修复版)
#
# 功能：强制拉取最新镜像并进行安全备份
# 特性：
#   • 激进拉取：强制从远程仓库拉取最新镜像，自动覆盖本地版本
#   • 智能验证：全面验证镜像完整性，包括摘要、大小、架构等信息
#   • 严格模式：可配置的失败处理策略，确保备份质量
#   • 错误分类：智能识别6种常见错误类型，提供针对性解决方案
#   • 进度跟踪：实时显示处理进度和详细统计信息
#   • 重试机制：可配置的重试次数，提高成功率
#
# 作者：Claude 4.0 sonnet
# 版本：2.1 (修复while read bug)
# 修复：解决文件末尾没有换行符导致的镜像计数问题
# ============================================================================

# 设置日志文件
LOG_FILE="/var/log/docker-backup.log"

# ============================================================================
# 全局变量配置
# ============================================================================

# 基础配置变量
IMAGES=""
IMAGE_FILE=""
OUTPUT_DIR="."
OUTPUT_NAME=""
REGISTRY=""
AUTO_CONFIRM=false
TEMP_LIST=$(mktemp)

# 激进模式配置变量
STRICT_MODE=true              # 严格模式：任何镜像拉取失败都终止操作（默认启用）
RETRY_COUNT=3                 # 拉取失败时的重试次数
VERIFY_IMAGES=true            # 是否验证拉取后的镜像

# 状态跟踪变量
FAILED_IMAGES=()              # 拉取失败的镜像列表
PULLED_IMAGES=()              # 成功拉取的镜像列表

# ============================================================================
# 工具函数
# ============================================================================

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# 帮助信息
show_help() {
    echo "Docker镜像激进模式备份脚本 (修复版)"
    echo "强制拉取最新镜像并进行备份，确保备份的是最新版本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "基本选项:"
    echo "  -i, --images '镜像1 镜像2'    指定要导出的镜像列表（用空格分隔）"
    echo "  -f, --file 文件名             从文件读取镜像列表"
    echo "  -o, --output 目录             指定输出目录（默认为当前目录）"
    echo "  -n, --name 文件名             指定输出文件名（默认为 registry_images_日期.tar）"
    echo "  -r, --registry 地址           指定私有仓库地址（例如：192.168.200.12:5000）"
    echo "  -y, --yes                     自动确认，不提示"
    echo ""
    echo "激进模式选项:"
    echo "  --strict-mode                 严格模式：任何镜像拉取失败都终止操作（默认启用）"
    echo "  --no-strict-mode              禁用严格模式，允许部分镜像拉取失败"
    echo "  --retry-count N               拉取失败时的重试次数（默认3次）"
    echo "  --no-verify                   跳过拉取后的镜像验证"
    echo ""
    echo "  -h, --help                    显示此帮助信息"
    echo ""
    echo "特性说明:"
    echo "  • 强制拉取最新镜像，自动覆盖本地旧版本"
    echo "  • 智能错误分类，提供针对性解决建议"
    echo "  • 严格验证机制，确保镜像完整性"
    echo "  • 详细进度反馈，实时显示处理状态"
    echo "  • 修复了文件读取bug，支持没有换行符结尾的镜像列表文件"
    echo ""
    echo "示例:"
    echo "  $0 -r 192.168.200.12:5000 -i 'aqsoc-server:2.2.0'"
    echo "  $0 -f images.txt -o /backup"
    echo "  $0 -r 192.168.200.12:5000 -i 'app:latest' --no-strict-mode"
    echo "  $0 --retry-count 5 -f images.txt --no-verify"
}

# 检查Docker服务状态
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log "错误: Docker服务未运行"
        exit 1
    fi
}

# 检查私有仓库连接
check_registry() {
    local registry=$1
    if ! curl -s "http://$registry/v2/_catalog" >/dev/null 2>&1; then
        log "警告: 无法连接到私有仓库 $registry"
        if [ "$AUTO_CONFIRM" = false ]; then
            read -p "是否继续? (y/n) " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    fi
}

# ============================================================================
# 新增功能函数（占位符，将在后续任务中实现）
# ============================================================================

# 注意：简化方案已移除容器检测和镜像清理功能
# docker pull 会自动覆盖本地镜像，无需手动清理
# 正在运行的容器不会受到影响，它们继续使用旧镜像运行

# 严格拉取镜像（带重试和错误分类）
strict_pull_image() {
    local image=$1
    local retry_count=${RETRY_COUNT:-3}
    local attempt=1
    local temp_error_file=$(mktemp)

    log "🔄 开始拉取镜像: $image"

    # 记录拉取前的镜像信息（如果存在）
    local old_digest=""
    if docker image inspect "$image" >/dev/null 2>&1; then
        old_digest=$(docker image inspect "$image" --format='{{index .RepoDigests 0}}' 2>/dev/null || echo "N/A")
        log "📋 本地镜像摘要: $old_digest"
    else
        log "📋 本地无此镜像，将进行首次拉取"
    fi

    while [ $attempt -le $retry_count ]; do
        log "🔄 拉取尝试 $attempt/$retry_count: $image"

        # 执行拉取操作，捕获错误输出
        if docker pull "$image" 2>"$temp_error_file"; then
            log "✅ 成功拉取镜像: $image"

            # 获取拉取后的镜像信息
            local new_digest=$(docker image inspect "$image" --format='{{index .RepoDigests 0}}' 2>/dev/null || echo "N/A")
            log "📋 新镜像摘要: $new_digest"

            # 比较摘要，判断是否获取了新版本
            if [ "$old_digest" != "$new_digest" ] && [ "$old_digest" != "" ]; then
                log "🆕 检测到镜像更新，已获取最新版本"
            elif [ "$old_digest" = "$new_digest" ] && [ "$old_digest" != "N/A" ]; then
                log "ℹ️  镜像摘要未变化，可能已是最新版本"
            fi

            # 验证拉取的镜像（如果启用验证）
            if [ "$VERIFY_IMAGES" = true ]; then
                if ! verify_pulled_image "$image"; then
                    log "❌ 镜像验证失败: $image"
                    FAILED_IMAGES+=("$image")
                    rm -f "$temp_error_file"

                    # 根据严格模式决定是否继续
                    if [ "$STRICT_MODE" = true ]; then
                        log "🚫 严格模式已启用，验证失败将终止整个备份过程"
                        return 1
                    else
                        log "⚠️  非严格模式，将跳过此镜像继续处理其他镜像"
                        return 0
                    fi
                fi
            else
                log "ℹ️  跳过镜像验证（--no-verify 选项已启用）"
            fi

            # 添加到成功列表
            PULLED_IMAGES+=("$image")
            rm -f "$temp_error_file"
            return 0
        else
            # 拉取失败，读取错误信息
            local error_msg=$(cat "$temp_error_file" 2>/dev/null || echo "未知错误")
            log "❌ 拉取失败 (尝试 $attempt/$retry_count): $image"

            # 调用错误分类函数
            classify_pull_error "$error_msg" "$image"
            local error_type=$?

            # 如果是认证错误或镜像不存在，不需要重试
            if [ $error_type -eq 2 ] || [ $error_type -eq 3 ]; then
                log "⚠️  错误类型不适合重试，停止尝试"
                break
            fi

            # 如果还有重试机会，等待后重试
            if [ $attempt -lt $retry_count ]; then
                local wait_time=$((attempt * 2))
                log "⏳ 等待 ${wait_time} 秒后重试..."
                sleep $wait_time
            fi

            attempt=$((attempt + 1))
        fi
    done

    # 所有重试都失败了
    log "💥 镜像拉取最终失败: $image"
    FAILED_IMAGES+=("$image")
    rm -f "$temp_error_file"

    # 根据严格模式决定是否继续
    if [ "$STRICT_MODE" = true ]; then
        log "🚫 严格模式已启用，拉取失败将终止整个备份过程"
        return 1
    else
        log "⚠️  非严格模式，将跳过此镜像继续处理其他镜像"
        return 0
    fi
}

# 分类拉取错误
classify_pull_error() {
    local error_msg=$1
    local image=$2

    log "分析拉取错误: $image"

    # 网络连接错误
    if echo "$error_msg" | grep -qi "network\|timeout\|connection\|dial\|refused\|unreachable"; then
        log "❌ 网络错误: 无法连接到镜像仓库"
        log "💡 建议解决方案:"
        log "   - 检查网络连接是否正常"
        log "   - 检查防火墙设置"
        log "   - 验证仓库地址是否正确: $REGISTRY"
        log "   - 尝试ping仓库地址测试连通性"
        return 1

    # 认证错误
    elif echo "$error_msg" | grep -qi "unauthorized\|authentication\|access denied\|forbidden\|401\|403"; then
        log "❌ 认证错误: 无权限访问镜像仓库"
        log "💡 建议解决方案:"
        if [ ! -z "$REGISTRY" ]; then
            log "   - 执行登录命令: docker login $REGISTRY"
        else
            log "   - 执行登录命令: docker login"
        fi
        log "   - 检查用户名和密码是否正确"
        log "   - 确认账户是否有访问该镜像的权限"
        return 2

    # 镜像不存在错误
    elif echo "$error_msg" | grep -qi "not found\|does not exist\|404\|no such image\|repository.*not found"; then
        log "❌ 镜像不存在: 指定的镜像未找到"
        log "💡 建议解决方案:"
        log "   - 检查镜像名称是否正确: $image"
        log "   - 检查镜像标签是否存在"
        log "   - 确认镜像是否已推送到仓库"
        log "   - 尝试列出可用镜像: docker search ${image%:*}"
        return 3

    # 仓库服务错误
    elif echo "$error_msg" | grep -qi "registry\|repository\|server\|500\|502\|503\|504"; then
        log "❌ 仓库服务错误: 镜像仓库服务异常"
        log "💡 建议解决方案:"
        log "   - 检查仓库服务是否正常运行"
        log "   - 稍后重试，可能是临时服务中断"
        log "   - 联系仓库管理员检查服务状态"
        return 4

    # TLS/SSL证书错误
    elif echo "$error_msg" | grep -qi "tls\|ssl\|certificate\|x509"; then
        log "❌ TLS/SSL错误: 证书验证失败"
        log "💡 建议解决方案:"
        log "   - 检查仓库证书是否有效"
        log "   - 如果是自签名证书，考虑添加 --insecure-registry 选项"
        log "   - 更新系统时间，确保时间同步"
        return 5

    # 磁盘空间不足
    elif echo "$error_msg" | grep -qi "no space\|disk\|storage"; then
        log "❌ 存储错误: 磁盘空间不足"
        log "💡 建议解决方案:"
        log "   - 清理不需要的Docker镜像: docker image prune"
        log "   - 检查磁盘空间: df -h"
        log "   - 清理Docker系统: docker system prune"
        return 6

    # 其他未知错误
    else
        log "❌ 未知错误: 无法识别的拉取错误"
        log "💡 错误详情: $error_msg"
        log "💡 建议解决方案:"
        log "   - 检查Docker服务状态: docker info"
        log "   - 查看Docker日志获取更多信息"
        log "   - 尝试手动拉取镜像进行调试"
        return 99
    fi
}

# 验证拉取的镜像
verify_pulled_image() {
    local image=$1

    log "🔍 开始验证镜像: $image"

    # 检查镜像是否存在
    if ! docker image inspect "$image" >/dev/null 2>&1; then
        log "❌ 验证失败: 镜像不存在于本地"
        log "💡 可能原因:"
        log "   - 镜像拉取失败但未被检测到"
        log "   - 镜像名称或标签错误"
        log "   - Docker守护进程异常"
        return 1
    fi

    # 获取镜像的详细信息
    local image_info=$(docker image inspect "$image" 2>/dev/null)
    if [ $? -ne 0 ] || [ -z "$image_info" ]; then
        log "❌ 验证失败: 无法获取镜像详细信息"
        return 2
    fi

    # 提取关键信息
    local image_id=$(echo "$image_info" | grep '"Id"' | head -1 | sed 's/.*"Id": *"\([^"]*\)".*/\1/' | cut -c1-12)
    local created=$(echo "$image_info" | grep '"Created"' | head -1 | sed 's/.*"Created": *"\([^"]*\)".*/\1/')
    local size=$(echo "$image_info" | grep '"Size"' | head -1 | sed 's/.*"Size": *\([0-9]*\).*/\1/')
    local architecture=$(echo "$image_info" | grep '"Architecture"' | head -1 | sed 's/.*"Architecture": *"\([^"]*\)".*/\1/')
    local os=$(echo "$image_info" | grep '"Os"' | head -1 | sed 's/.*"Os": *"\([^"]*\)".*/\1/')

    # 获取镜像摘要（如果可用）
    local digest=$(docker image inspect "$image" --format='{{index .RepoDigests 0}}' 2>/dev/null || echo "N/A")

    # 获取镜像标签
    local tags=$(docker image inspect "$image" --format='{{join .RepoTags ", "}}' 2>/dev/null || echo "N/A")

    # 计算人类可读的大小
    local size_mb=""
    if [ ! -z "$size" ] && [ "$size" != "null" ] && [ "$size" -gt 0 ]; then
        size_mb=$(echo "scale=2; $size/1024/1024" | bc 2>/dev/null || echo "计算失败")
    else
        size_mb="未知"
    fi

    # 格式化创建时间
    local created_formatted=""
    if [ ! -z "$created" ] && [ "$created" != "null" ]; then
        # 尝试格式化时间（简单处理）
        created_formatted=$(echo "$created" | cut -c1-19 | tr 'T' ' ')
    else
        created_formatted="未知"
    fi

    # 输出验证结果
    log "✅ 镜像验证成功: $image"
    log "📋 镜像详细信息:"
    log "   🆔 镜像ID: $image_id"
    log "   🏷️  标签: $tags"
    log "   📅 创建时间: $created_formatted"
    log "   📦 大小: ${size_mb}MB"
    log "   🏗️  架构: $architecture"
    log "   💻 操作系统: $os"
    log "   🔐 摘要: $digest"

    # 检查镜像是否过旧（可选警告）
    if [ ! -z "$created" ] && [ "$created" != "null" ]; then
        # 简单的时间检查：如果创建时间包含年份，检查是否是当前年份
        local current_year=$(date +%Y)
        local image_year=$(echo "$created" | cut -c1-4)

        if [ ! -z "$image_year" ] && [ "$image_year" -lt "$current_year" ]; then
            local year_diff=$((current_year - image_year))
            if [ $year_diff -gt 1 ]; then
                log "⚠️  注意: 镜像创建于 $image_year 年，可能较旧（${year_diff}年前）"
            fi
        fi
    fi

    # 检查镜像完整性（基本检查）
    local layers_count=$(echo "$image_info" | grep -c '"sha256:')
    if [ "$layers_count" -gt 0 ]; then
        log "🔍 镜像层数: $layers_count 层"
    else
        log "⚠️  警告: 无法确定镜像层信息"
    fi

    # 验证镜像是否可以被使用（尝试获取入口点信息）
    local entrypoint=$(docker image inspect "$image" --format='{{.Config.Entrypoint}}' 2>/dev/null || echo "[]")
    local cmd=$(docker image inspect "$image" --format='{{.Config.Cmd}}' 2>/dev/null || echo "[]")

    if [ "$entrypoint" != "[]" ] || [ "$cmd" != "[]" ]; then
        log "✅ 镜像配置完整，包含执行入口点"
    else
        log "ℹ️  镜像无默认入口点（可能是基础镜像）"
    fi

    log "🎯 镜像验证完成: $image"
    return 0
}

# ============================================================================
# 命令行参数解析
# ============================================================================

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--images)
            IMAGES="$2"
            shift 2
            ;;
        -f|--file)
            IMAGE_FILE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -n|--name)
            OUTPUT_NAME="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -y|--yes)
            AUTO_CONFIRM=true
            shift
            ;;
        --strict-mode)
            STRICT_MODE=true
            shift
            ;;
        --no-strict-mode)
            STRICT_MODE=false
            shift
            ;;
        --retry-count)
            RETRY_COUNT="$2"
            shift 2
            ;;
        --no-verify)
            VERIFY_IMAGES=false
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# ============================================================================
# 主流程（修复版）
# ============================================================================

# 检查Docker服务
check_docker

# 检查输出目录
if [ ! -d "$OUTPUT_DIR" ]; then
    log "创建输出目录: $OUTPUT_DIR"
    mkdir -p "$OUTPUT_DIR"
fi

# 设置输出文件名
if [ -z "$OUTPUT_NAME" ]; then
    OUTPUT_NAME="registry_images_$(date +%Y%m%d).tar"
fi

OUTPUT_TAR="$OUTPUT_DIR/$OUTPUT_NAME"

# 生成镜像列表
if [ ! -z "$IMAGES" ]; then
    if [ ! -z "$REGISTRY" ]; then
        # 添加registry前缀到每个镜像
        echo "$IMAGES" | tr ' ' '\n' | sed "s#^#$REGISTRY/#" > $TEMP_LIST
    else
        echo "$IMAGES" | tr ' ' '\n' > $TEMP_LIST
    fi
elif [ ! -z "$IMAGE_FILE" ]; then
    if [ ! -f "$IMAGE_FILE" ]; then
        log "错误: 镜像列表文件 '$IMAGE_FILE' 不存在"
        exit 1
    fi
    if [ ! -z "$REGISTRY" ]; then
        # 添加registry前缀到文件中的每个镜像
        sed "s#^#$REGISTRY/#" "$IMAGE_FILE" > $TEMP_LIST
    else
        cat "$IMAGE_FILE" > $TEMP_LIST
    fi
else
    log "错误: 请指定镜像列表（使用 -i 或 -f 选项）"
    show_help
    exit 1
fi

# 确保临时文件以换行符结尾（修复关键点）
echo "" >> $TEMP_LIST

# 如果指定了registry，检查连接性
if [ ! -z "$REGISTRY" ]; then
    check_registry "$REGISTRY"
fi

# 显示将要导出的镜像
log "将要导出以下镜像:"
cat $TEMP_LIST

# ============================================================================
# 激进模式主流程：强制拉取 + 严格验证 + 直接打包 (修复版)
# ============================================================================

log "🚀 启动激进模式镜像备份流程 (修复版)"
log "📋 配置信息:"
log "   🔄 重试次数: $RETRY_COUNT"
log "   🔒 严格模式: $STRICT_MODE"
log "   ✅ 镜像验证: $VERIFY_IMAGES"

# 初始化状态统计
total_images=0
processed_images=0
success_count=0
failed_count=0

# 计算总镜像数 (修复：处理文件末尾没有换行符的情况)
while read image || [[ -n $image ]]; do
    [[ -n $image ]] && total_images=$((total_images + 1))
done < $TEMP_LIST

log "📊 将处理 $total_images 个镜像"
echo ""

# 主处理循环：对每个镜像执行 强制拉取 -> 验证 -> 记录状态 (修复版)
while read image || [[ -n $image ]]; do
    # 跳过空行
    [[ -z $image ]] && continue

    processed_images=$((processed_images + 1))
    log "🔄 处理镜像 [$processed_images/$total_images]: $image"

    # 调用严格拉取函数
    if strict_pull_image "$image"; then
        success_count=$((success_count + 1))
        log "✅ 镜像处理成功: $image"
    else
        failed_count=$((failed_count + 1))
        log "❌ 镜像处理失败: $image"

        # 严格模式下，任何失败都终止流程
        if [ "$STRICT_MODE" = true ]; then
            log "🚫 严格模式：检测到失败，终止备份流程"
            log "📊 处理统计: 成功 $success_count, 失败 $failed_count, 总计 $processed_images/$total_images"
            exit 1
        fi
    fi

    echo ""
done < $TEMP_LIST

# 处理完成，显示统计信息
log "🎯 镜像处理完成"
log "📊 最终统计:"
log "   ✅ 成功: $success_count 个镜像"
log "   ❌ 失败: $failed_count 个镜像"
log "   📦 总计: $total_images 个镜像"

# 检查是否有成功拉取的镜像
if [ $success_count -eq 0 ]; then
    log "💥 错误: 没有成功拉取任何镜像，无法进行备份"
    exit 1
fi

# 如果有失败但非严格模式，询问是否继续
if [ $failed_count -gt 0 ] && [ "$STRICT_MODE" = false ]; then
    log "⚠️  检测到 $failed_count 个镜像处理失败"
    if [ "$AUTO_CONFIRM" = false ]; then
        read -p "是否继续使用 $success_count 个成功的镜像进行备份? (y/n) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "操作已取消"
            exit 1
        fi
    else
        log "🤖 自动确认模式：继续使用成功的镜像进行备份"
    fi
fi

# 准备最终的镜像列表（只包含成功的镜像）
FINAL_IMAGE_LIST=$(mktemp)
for image in "${PULLED_IMAGES[@]}"; do
    echo "$image" >> $FINAL_IMAGE_LIST
done

log "📋 最终备份镜像列表:"
cat $FINAL_IMAGE_LIST

# 计算成功镜像的总大小 (修复版)
log "📏 计算备份文件大小..."
total_size=0
calculated_images=0

while read image || [[ -n $image ]]; do
    [[ -z $image ]] && continue
    if docker image inspect "$image" >/dev/null 2>&1; then
        size=$(docker image inspect "$image" --format='{{.Size}}')
        total_size=$((total_size + size))
        calculated_images=$((calculated_images + 1))
        log "   📦 $image: $(echo "scale=2; $size/1024/1024" | bc)MB"
    else
        log "   ⚠️  无法获取镜像大小: $image"
    fi
done < $FINAL_IMAGE_LIST

# 显示总大小
size_mb=$(echo "scale=2; $total_size/1024/1024" | bc)
log "📊 备份大小统计:"
log "   📦 镜像数量: $calculated_images 个"
log "   💾 预计总大小: ${size_mb}MB"

# 检查磁盘空间
available_space=$(df -m "$OUTPUT_DIR" | awk 'NR==2 {print $4}')
if [ $(echo "$size_mb * 1.2" | bc | cut -d. -f1) -gt $available_space ]; then
    log "警告: 可用空间可能不足（建议预留20%额外空间）"
    if [ "$AUTO_CONFIRM" = false ]; then
        read -p "是否继续? (y/n) " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "操作已取消"
            exit 1
        fi
    fi
fi

# 确认是否继续
if [ "$AUTO_CONFIRM" = false ]; then
    read -p "是否开始导出镜像? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "操作已取消"
        exit 1
    fi
fi

# 导出成功拉取的镜像
log "📦 开始导出镜像到: $OUTPUT_TAR"
log "🔄 正在打包 $success_count 个镜像..."

if docker save -o "$OUTPUT_TAR" $(cat $FINAL_IMAGE_LIST); then
    log "✅ 导出完成: $OUTPUT_TAR"

    # 计算MD5校验和
    log "🔐 计算文件校验和..."
    if command -v md5sum >/dev/null 2>&1; then
        md5_hash=$(md5sum "$OUTPUT_TAR" | cut -d' ' -f1)
        log "🔐 MD5校验和: $md5_hash"
        echo "$md5_hash  $OUTPUT_TAR" | tee -a $LOG_FILE
    else
        log "⚠️  md5sum命令不可用，跳过校验和计算"
    fi

    # 显示最终结果
    log "🎉 备份完成！"
    log "📋 备份文件信息:"
    if ls -lh "$OUTPUT_TAR" 2>/dev/null; then
        ls -lh "$OUTPUT_TAR" | tee -a $LOG_FILE
    else
        log "   文件: $OUTPUT_TAR"
        log "   大小: ${size_mb}MB (预估)"
    fi

    log "📊 备份摘要:"
    log "   ✅ 成功镜像: $success_count 个"
    log "   ❌ 失败镜像: $failed_count 个"
    log "   📦 备份文件: $OUTPUT_TAR"
    log "   💾 文件大小: ${size_mb}MB"

else
    log "💥 错误: 导出失败"
    log "🔍 可能原因:"
    log "   - 磁盘空间不足"
    log "   - Docker服务异常"
    log "   - 镜像损坏或不完整"
    log "   - 权限问题"
    exit 1
fi

# 清理临时文件
log "🧹 清理临时文件..."
rm -f $TEMP_LIST $FINAL_IMAGE_LIST

log "🎯 激进模式备份流程完成！(修复版)"
