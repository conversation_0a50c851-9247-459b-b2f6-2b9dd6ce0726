<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeHostscanWpresultMapper">

    <resultMap type="FfsafeHostscanWpresult" id="FfsafeHostscanWpresultResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="serviceType"    column="service_type"    />
        <result property="username"    column="username"    />
        <result property="weakPassword"    column="weak_password"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="hostPort"    column="host_port"    />
        <result property="summaryId"    column="summary_id"    />
    </resultMap>

    <sql id="selectFfsafeHostscanWpresultVo">
        select id, task_id, service_type, username, weak_password, host_ip, host_port, summary_id from ffsafe_hostscan_wpresult
    </sql>

    <select id="selectFfsafeHostscanWpresultList" parameterType="FfsafeHostscanWpresult" resultMap="FfsafeHostscanWpresultResult">
        <include refid="selectFfsafeHostscanWpresultVo"/>
        <where>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="serviceType != null  and serviceType != ''"> and service_type = #{serviceType}</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="weakPassword != null  and weakPassword != ''"> and weak_password = #{weakPassword}</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="hostPort != null "> and host_port = #{hostPort}</if>
        </where>
    </select>

    <select id="selectFfsafeHostscanWpresultById" parameterType="Long" resultMap="FfsafeHostscanWpresultResult">
        <include refid="selectFfsafeHostscanWpresultVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeHostscanWpresultByIds" parameterType="Long" resultMap="FfsafeHostscanWpresultResult">
        <include refid="selectFfsafeHostscanWpresultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeHostscanWpresult" parameterType="FfsafeHostscanWpresult" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_hostscan_wpresult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="serviceType != null">service_type,</if>
            <if test="username != null">username,</if>
            <if test="weakPassword != null">weak_password,</if>
            <if test="hostIp != null">host_ip,</if>
            <if test="hostPort != null">host_port,</if>
            <if test="summaryId != null">summary_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="serviceType != null">#{serviceType},</if>
            <if test="username != null">#{username},</if>
            <if test="weakPassword != null">#{weakPassword},</if>
            <if test="hostIp != null">#{hostIp},</if>
            <if test="hostPort != null">#{hostPort},</if>
            <if test="summaryId != null">#{summaryId},</if>
        </trim>
    </insert>

    <update id="updateFfsafeHostscanWpresult" parameterType="FfsafeHostscanWpresult">
        update ffsafe_hostscan_wpresult
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="serviceType != null">service_type = #{serviceType},</if>
            <if test="username != null">username = #{username},</if>
            <if test="weakPassword != null">weak_password = #{weakPassword},</if>
            <if test="hostIp != null">host_ip = #{hostIp},</if>
            <if test="hostPort != null">host_port = #{hostPort},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeHostscanWpresultById" parameterType="Long">
        delete from ffsafe_hostscan_wpresult where id = #{id}
    </delete>

    <delete id="deleteFfsafeHostscanWpresultByIds" parameterType="String">
        delete from ffsafe_hostscan_wpresult where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>