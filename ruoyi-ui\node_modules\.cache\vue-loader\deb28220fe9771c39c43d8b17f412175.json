{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue?vue&type=template&id=f8fdff14&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue", "mtime": 1755768894571}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}