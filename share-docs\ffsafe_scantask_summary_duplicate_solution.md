# ffsafe_scantask_summary 表重复数据问题解决方案

## 📋 概述

本文档提供了针对 `ffsafe_scantask_summary` 表重复数据问题的完整解决方案，包括数据库层面、代码层面和运维层面的综合治理方案。

## 🚨 问题回顾

**核心问题**：
- 表中存在大量 `task_id + task_type` 相同的重复记录
- 缺少数据库唯一性约束
- 并发调度和异常处理导致重复插入

## 🎯 解决方案总体架构

### 解决方案优先级
1. **紧急修复**：数据库约束 + 代码防护（1-2天）
2. **中期优化**：事务优化 + 并发控制（1周）
3. **长期改进**：架构重构 + 监控告警（2-4周）

## 🔧 阶段一：紧急修复方案（1-2天）

### 1.1 数据库层面修复

#### 步骤1：清理现有重复数据
```sql
-- 1. 备份现有数据
CREATE TABLE ffsafe_scantask_summary_backup_20250820 AS 
SELECT * FROM ffsafe_scantask_summary;

-- 2. 查看重复数据统计
SELECT task_id, task_type, COUNT(*) as count, 
       GROUP_CONCAT(id ORDER BY id) as ids
FROM ffsafe_scantask_summary 
GROUP BY task_id, task_type 
HAVING COUNT(*) > 1;

-- 3. 删除重复数据（保留最早的记录）
DELETE t1 FROM ffsafe_scantask_summary t1
INNER JOIN ffsafe_scantask_summary t2 
WHERE t1.task_id = t2.task_id 
  AND t1.task_type = t2.task_type 
  AND t1.id > t2.id;

-- 4. 验证清理结果
SELECT task_id, task_type, COUNT(*) as count 
FROM ffsafe_scantask_summary 
GROUP BY task_id, task_type 
HAVING COUNT(*) > 1;
```

#### 步骤2：添加唯一性约束
```sql
-- 添加唯一性约束
ALTER TABLE ffsafe_scantask_summary 
ADD CONSTRAINT uk_task_id_type UNIQUE (task_id, task_type);

-- 验证约束创建
SHOW INDEX FROM ffsafe_scantask_summary;
```

### 1.2 代码层面防护

#### 新增防重复插入方法

**位置**：`FfsafeScantaskSummaryMapper.xml`
```xml
<!-- 新增：根据task_id和task_type查询 -->
<select id="selectByTaskIdAndType" resultMap="FfsafeScantaskSummaryResult">
    <include refid="selectFfsafeScantaskSummaryVo"/>
    WHERE s.task_id = #{taskId} AND s.task_type = #{taskType}
    LIMIT 1
</select>

<!-- 新增：插入或更新 -->
<insert id="insertOrUpdate" parameterType="FfsafeScantaskSummary">
    INSERT INTO ffsafe_scantask_summary (
        job_id, task_id, task_type, task_status, finish_rate,
        high_risk_num, middle_risk_num, low_risk_num, poc_risk_num, 
        info_risk_num, start_time, end_time
    ) VALUES (
        #{jobId}, #{taskId}, #{taskType}, #{taskStatus}, #{finishRate},
        #{highRiskNum}, #{middleRiskNum}, #{lowRiskNum}, #{pocRiskNum},
        #{infoRiskNum}, #{startTime}, #{endTime}
    )
    ON DUPLICATE KEY UPDATE
        task_status = VALUES(task_status),
        finish_rate = VALUES(finish_rate),
        high_risk_num = VALUES(high_risk_num),
        middle_risk_num = VALUES(middle_risk_num),
        low_risk_num = VALUES(low_risk_num),
        poc_risk_num = VALUES(poc_risk_num),
        info_risk_num = VALUES(info_risk_num),
        end_time = VALUES(end_time)
</insert>
```

**位置**：`FfsafeScantaskSummaryMapper.java`
```java
/**
 * 根据task_id和task_type查询记录
 */
FfsafeScantaskSummary selectByTaskIdAndType(@Param("taskId") Integer taskId, 
                                           @Param("taskType") Integer taskType);

/**
 * 插入或更新记录（防重复）
 */
int insertOrUpdate(FfsafeScantaskSummary ffsafeScantaskSummary);
```

#### 修改业务逻辑

**位置**：`FfsafeScantaskSummaryServiceImpl.java`
```java
/**
 * 安全插入方法（防重复）
 */
@Override
public int safeInsertFfsafeScantaskSummary(FfsafeScantaskSummary ffsafeScantaskSummary) {
    try {
        // 方案1：使用 INSERT ... ON DUPLICATE KEY UPDATE
        return ffsafeScantaskSummaryMapper.insertOrUpdate(ffsafeScantaskSummary);
    } catch (Exception e) {
        log.warn("插入扫描任务汇总失败，taskId: {}, taskType: {}, 错误: {}", 
                ffsafeScantaskSummary.getTaskId(), 
                ffsafeScantaskSummary.getTaskType(), 
                e.getMessage());
        return 0;
    }
}

/**
 * 检查并插入方法
 */
@Override
public int checkAndInsertFfsafeScantaskSummary(FfsafeScantaskSummary ffsafeScantaskSummary) {
    // 方案2：先查询再决定插入或更新
    FfsafeScantaskSummary existing = ffsafeScantaskSummaryMapper
        .selectByTaskIdAndType(ffsafeScantaskSummary.getTaskId(), 
                              ffsafeScantaskSummary.getTaskType());
    
    if (existing == null) {
        return ffsafeScantaskSummaryMapper.insertFfsafeScantaskSummary(ffsafeScantaskSummary);
    } else {
        // 更新现有记录
        ffsafeScantaskSummary.setId(existing.getId());
        return ffsafeScantaskSummaryMapper.updateFfsafeScantaskSummary(ffsafeScantaskSummary);
    }
}
```

#### 修改调用点

**位置**：`HostVulnScan.java`
```java
// 原代码（第79行）
// if (scantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary) > 0) {

// 修改为：
if (scantaskSummaryService.safeInsertFfsafeScantaskSummary(ffsafeScantaskSummary) > 0) {
    JSONObject summaryParams = new JSONObject();
    summaryParams.put("taskId", taskId);
    summaryParams.put("jobId", jobId);
    summaryParams.put("deviceConfigId", sysJob.getDeviceConfigId());
    hostScanResultMonitorEvent.addHostScanTask(summaryParams);
    sysJob.setCurrentStatus(SysJob.PROCESS_RUNNING);
}
```

**位置**：`WebVulnScan.java`
```java
// 原代码（第74行）
// if (scantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary) > 0) {

// 修改为：
if (scantaskSummaryService.safeInsertFfsafeScantaskSummary(ffsafeScantaskSummary) > 0) {
    JSONObject scanTaskSummaryParams = new JSONObject();
    scanTaskSummaryParams.put("taskId", taskId);
    scanTaskSummaryParams.put("jobId", jobId);
    scanTaskSummaryParams.put("deviceConfigId", sysJob.getDeviceConfigId());
    hostScanResultMonitorEvent.addWebScanTask(scanTaskSummaryParams);
    sysJob.setCurrentStatus(SysJob.PROCESS_RUNNING);
}
```

## 🔄 阶段二：中期优化方案（1周）

### 2.1 事务边界优化

#### 创建统一的扫描任务创建服务

**新建**：`ScanTaskCreationService.java`
```java
@Service
@Transactional(rollbackFor = Exception.class)
public class ScanTaskCreationService {
    
    @Autowired
    private IScanTaskService scanTaskService;
    
    @Autowired
    private IFfsafeScantaskSummaryService scantaskSummaryService;
    
    @Autowired
    private ScanResultMonitorEvent scanResultMonitorEvent;
    
    @Autowired
    private ISysJobService sysJobService;
    
    /**
     * 原子化创建主机扫描任务
     */
    public CreateHostScanTaskResult createHostScanTaskAtomic(
            String jobId, HostScanTaskParam param, SysJob sysJob) {
        
        try {
            // 1. 创建扫描任务
            CreateHostScanTaskResult result = scanTaskService.createHostScanTask(param);
            if (result == null) {
                throw new RuntimeException("创建扫描任务失败");
            }
            
            // 2. 创建汇总记录
            int taskId = result.getTaskId();
            FfsafeScantaskSummary summary = new FfsafeScantaskSummary();
            summary.setJobId(Integer.valueOf(jobId));
            summary.setTaskId(taskId);
            summary.setTaskType(HostVulnScan.HOST_SCAN);
            summary.setTaskStatus(ScanResultMonitorEvent.SCHEDULING);
            
            int insertResult = scantaskSummaryService.safeInsertFfsafeScantaskSummary(summary);
            if (insertResult <= 0) {
                throw new RuntimeException("插入任务汇总失败");
            }
            
            // 3. 更新任务状态
            sysJob.setCurrentStatus(SysJob.PROCESS_RUNNING);
            sysJobService.updateJob(sysJob);
            
            // 4. 添加监控（在事务提交后执行）
            TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        JSONObject summaryParams = new JSONObject();
                        summaryParams.put("taskId", taskId);
                        summaryParams.put("jobId", jobId);
                        summaryParams.put("deviceConfigId", sysJob.getDeviceConfigId());
                        scanResultMonitorEvent.addHostScanTask(summaryParams);
                    }
                }
            );
            
            return result;
            
        } catch (Exception e) {
            log.error("原子化创建主机扫描任务失败，jobId: {}, 错误: {}", jobId, e.getMessage());
            throw new RuntimeException("创建扫描任务失败: " + e.getMessage(), e);
        }
    }
}
```

### 2.2 并发控制优化

#### 添加分布式锁机制

**位置**：`HostVulnScan.java`
```java
@Component("HostVulnScan")
public class HostVulnScan {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String SCAN_LOCK_PREFIX = "scan_lock:";
    private static final int LOCK_TIMEOUT = 300; // 5分钟
    
    public void scan(String jobId, String scanParam) throws Exception {
        String lockKey = SCAN_LOCK_PREFIX + jobId;
        
        // 获取分布式锁
        Boolean lockAcquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, System.currentTimeMillis(), 
                        Duration.ofSeconds(LOCK_TIMEOUT));
        
        if (!lockAcquired) {
            throw new Exception("任务正在执行中，请勿重复调度，jobId: " + jobId);
        }
        
        try {
            // 原有的扫描逻辑
            performScan(jobId, scanParam);
        } finally {
            // 释放锁
            redisTemplate.delete(lockKey);
        }
    }
    
    private void performScan(String jobId, String scanParam) throws Exception {
        // 原有的 scan 方法逻辑
        // ...
    }
}
```

## 🏗️ 阶段三：长期改进方案（2-4周）

### 3.1 架构重构

#### 引入状态机模式

**新建**：`ScanTaskStateMachine.java`
```java
@Component
public class ScanTaskStateMachine {
    
    public enum TaskState {
        CREATED,      // 已创建
        SCHEDULED,    // 已调度
        RUNNING,      // 运行中
        COMPLETED,    // 已完成
        FAILED,       // 失败
        CANCELLED     // 已取消
    }
    
    public enum TaskEvent {
        SCHEDULE,     // 调度
        START,        // 开始
        COMPLETE,     // 完成
        FAIL,         // 失败
        CANCEL        // 取消
    }
    
    /**
     * 状态转换
     */
    public TaskState transition(TaskState currentState, TaskEvent event) {
        switch (currentState) {
            case CREATED:
                if (event == TaskEvent.SCHEDULE) return TaskState.SCHEDULED;
                break;
            case SCHEDULED:
                if (event == TaskEvent.START) return TaskState.RUNNING;
                if (event == TaskEvent.CANCEL) return TaskState.CANCELLED;
                break;
            case RUNNING:
                if (event == TaskEvent.COMPLETE) return TaskState.COMPLETED;
                if (event == TaskEvent.FAIL) return TaskState.FAILED;
                break;
        }
        throw new IllegalStateException("无效的状态转换: " + currentState + " -> " + event);
    }
}
```

### 3.2 监控告警系统

#### 重复数据监控

**新建**：`DuplicateDataMonitor.java`
```java
@Component
@Slf4j
public class DuplicateDataMonitor {
    
    @Autowired
    private FfsafeScantaskSummaryMapper summaryMapper;
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void checkDuplicateData() {
        try {
            List<Map<String, Object>> duplicates = summaryMapper.findDuplicateRecords();
            
            if (!duplicates.isEmpty()) {
                log.error("发现重复数据记录: {}", duplicates);
                
                // 发送告警
                sendAlert("ffsafe_scantask_summary表发现重复数据", duplicates);
            }
        } catch (Exception e) {
            log.error("检查重复数据失败", e);
        }
    }
    
    private void sendAlert(String title, Object data) {
        // 实现告警逻辑（邮件、钉钉、短信等）
    }
}
```

## 📋 实施计划

### 第1天：紧急修复
- [ ] 备份现有数据
- [ ] 清理重复数据
- [ ] 添加唯一性约束
- [ ] 部署代码防护

### 第2-3天：验证测试
- [ ] 功能测试
- [ ] 并发测试
- [ ] 回归测试

### 第4-7天：中期优化
- [ ] 事务边界优化
- [ ] 并发控制实现
- [ ] 性能测试

### 第2-4周：长期改进
- [ ] 架构重构
- [ ] 监控系统
- [ ] 文档完善

## ⚠️ 风险评估与回滚方案

### 风险点
1. **数据库约束添加失败**：可能存在无法清理的重复数据
2. **代码修改影响现有功能**：可能影响扫描任务创建
3. **性能影响**：唯一性约束可能影响插入性能

### 回滚方案
```sql
-- 如果需要回滚唯一性约束
ALTER TABLE ffsafe_scantask_summary DROP INDEX uk_task_id_type;

-- 恢复备份数据
-- TRUNCATE TABLE ffsafe_scantask_summary;
-- INSERT INTO ffsafe_scantask_summary SELECT * FROM ffsafe_scantask_summary_backup_20250820;
```

## 📊 效果评估指标

### 技术指标
- 重复数据数量：目标为 0
- 插入成功率：> 99.9%
- 响应时间：< 100ms

### 业务指标
- 扫描任务创建成功率：> 99%
- 数据一致性：100%
- 系统稳定性：无因重复数据导致的故障

## 📝 总结

本解决方案采用分阶段实施策略，优先解决紧急问题，然后逐步优化系统架构。通过数据库约束、代码防护、事务优化和监控告警的综合治理，彻底解决重复数据问题，提升系统的稳定性和可靠性。

## 🔗 相关文档

- [重复数据问题分析报告](./ffsafe_scantask_summary_duplicate_analysis.md)
- [数据库表结构文档](./database_schema.md)
- [扫描任务流程文档](./scan_task_workflow.md)

## 📞 联系方式

如有问题请联系：
- 技术负责人：Claude 4.0 sonnet
- 文档更新时间：2025-08-20
- 版本：v1.0
