<template>
    <div style="padding: 0 15px">
      <div style="margin-bottom: 10px;">
        <h3 style="font-weight: bold">扫描任务名称：{{ jobRow.jobName}}</h3>
      </div>
      <el-table height="100%" v-loading="loading" :data="jobTaskList">
        <el-table-column
          label="任务名称"
        >
          <template slot-scope="scope">
            <span> {{ jobRow.jobName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="扫描目标"
        >
          <template slot-scope="scope">
            <el-tooltip v-if="jobRow.jobType !== 3" class="item" placement="top">
              <div v-html="jobRow.ipOver" slot="content"></div>
              <div class="oneLine">
                {{ jobRow.ipShow }}
              </div>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          label="执行周期"
          prop="username"
          width="120"
        >
          <template slot-scope="scope">
            <span> {{ jobRow.cronTransfer }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="最近扫描状态"
          prop="taskStatus">
          <template slot-scope="scope">
            <el-tag type="danger" v-if="scope.row.taskType === 2 && scope.row.taskStatus === 3">任务异常</el-tag>
            <el-tag v-else-if="scope.row.taskType === 2 && scope.row.taskStatus === 1">扫描中</el-tag>
            <el-tag v-else-if="scope.row.taskType === 2 && scope.row.taskStatus === 2">扫描中</el-tag>
            <el-tag type="success" v-else-if="scope.row.taskType === 2 && scope.row.taskStatus === 4">已扫描</el-tag>
            <el-tag type="danger" v-else-if="scope.row.taskType === 1 && scope.row.taskStatus === 3">任务异常</el-tag>
            <el-tag type="danger" v-else-if="scope.row.taskType === 1 && scope.row.taskStatus === 4">任务终止</el-tag>
            <el-tag v-else-if="scope.row.taskType === 1 && scope.row.taskStatus === 0">扫描中</el-tag>
            <el-tag v-else-if="scope.row.taskType === 1 && scope.row.taskStatus === 1">扫描中</el-tag>
            <el-tag type="success" v-else-if="scope.row.taskType === 1 && scope.row.taskStatus === 2">已扫描</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="扫描进度"
          prop="finishRate"
          width="120"
        >
          <template slot-scope="scope">
            <el-progress :text-inside="true" :stroke-width="18" :percentage="scope.row.finishRate"></el-progress>
          </template>
        </el-table-column>
        <el-table-column
          label="执行时间"
          prop="endTime"
          width="160"
        >
          <template slot-scope="scope">
            {{ parseTime(scope.row.endTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="160"
          fixed="right"
          class-name="small-padding fixed-width"
          :show-overflow-tooltip="false"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="taskDetail(scope.row)"
            >详情
            </el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.taskType === 1 && scope.row.taskStatus === 2 && scope.row.reportStatus === null"
              @click="createReport(scope.row)"
            >生成报告
            </el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.taskType === 2 && scope.row.taskStatus === 4 && scope.row.reportStatus === null"
              @click="createReport(scope.row)"
            >生成报告
            </el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.reportStatus !== null && scope.row.reportStatus !== 2"
            >报告生成中
            </el-button>
            <el-button
              size="mini"
              type="text"
              v-if="scope.row.reportStatus === 2"
              @click="downReport(scope.row)"
            >下载报告
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <el-dialog title="任务详情" :visible.sync="detailDialog" width="70%" append-to-body>
        <ff-job-tasks-detail v-if="detailDialog" :form="taskRow" :job-row="jobRow" />
        <!--<detail-info v-if="detailDialog" :host-ip="hostIp" :detail-type="detailType" :dept-name="deptName" :is-asset="isAsset" :current-asset-data="currentAssetData" />-->
      </el-dialog>
    </div>
</template>

<script>
  import { listScantaskSummary, taskCreatereport, taskDownReport } from '../../../api/monitor2/wpresult'
  import FfJobTasksDetail from './ffJobTasksDetail'

  export default {
    name: 'ffJobTasks',
    components: { FfJobTasksDetail },
    props: {
      jobRow: {
        type: Object,
        default: {}
      },
      jobId: {
        type: [String,Number],
        required: true,
      },
      jobType:{
        type:Number,
        default:undefined,
      }
    },
    data() {
      return {
        //下拉框所选中的扫描实例id
        exploreJobId: '',
        //任务详细信息
        jobDetail:{},
        //任务列表
        jobTaskList: [],
        queryParams:{
          pageNum: 1,
          pageSize: 10,
        },
        // 总条数
        total: 0,
        detailDialog: false,
        processStatus: undefined,
        processId: '',
        loading: false,
        exploreDisabled: false,
        taskRow: [],
        timer: null,
        reportType: ''
        // jobType: 'tanHuo'
      }
    },
    created() {
      this.getList()
      var _this = this;
      this.timer = setInterval(() => {
        _this.getList()
      }, 10000);
    },
    beforeDestroy() {
      // 清除定时器
      if (this.timer) {
        clearInterval(this.timer);
      }
    },
    methods: {
      getList() {
        this.loading = true;
        this.queryParams.taskType = this.jobType
        this.queryParams.jobId = this.jobId
        listScantaskSummary(this.queryParams).then(response => {
          this.jobTaskList = response.rows
          this.total = response.total;
          this.loading = false;
        }).catch(() => {
          this.loading = false;
        });
      },
      createReport(row) {
        taskCreatereport(row).then(response => {
          this.getList()
        })
      },
      downReport(row) {
        taskDownReport(row).then(response => {
          if (response.code === 200) {
            window.open(response.msg, "_blank")
          }
        })
      },
      taskDetail(row) {
        this.taskRow = []
        this.taskRow.push(row)
        this.detailDialog = true
      }
    }
  }
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 20px 30px;
  height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
