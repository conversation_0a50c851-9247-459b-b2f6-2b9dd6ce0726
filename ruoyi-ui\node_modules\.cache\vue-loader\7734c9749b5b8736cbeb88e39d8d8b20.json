{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\threat\\overview\\component\\PointsOverview.vue?vue&type=style&index=0&id=18ad52a9&scoped=true&lang=scss", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\threat\\overview\\component\\PointsOverview.vue", "mtime": 1755762306448}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5wb2ludHMtb3ZlcnZpZXcgewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiA4cHg7CiAgLnBvaW50cy1vdmVydmlldy1oZWFkZXIgewogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDU3cHg7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIC5wcm9wb3J0aW9uIHsKICAgICAgd2lkdGg6IDEwMCU7CiAgICAgIGhlaWdodDogMzBweDsKICAgICAgZmxleDogMTsKICAgICAgbWFyZ2luLXRvcDogMjdweDsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgLnNjb3JlLWluZm8gewogICAgICAgIHdpZHRoOiA1MCU7CiAgICAgICAgaGVpZ2h0OiAzMHB4OwogICAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4OwogICAgICAgIGJhY2tncm91bmQ6ICNFOEY0RkU7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgY29sb3I6ICMxMDEwMTA7CiAgICAgICAgZGl2IHsKICAgICAgICAgIHNwYW4gewogICAgICAgICAgICBjb2xvcjogI2JkMzEyNDsKICAgICAgICAgIH0KICAgICAgICAgICY6Zmlyc3QtY2hpbGQgewogICAgICAgICAgICB0ZXh0LWluZGVudDogMTVweDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5ncmFkZS1wcm9wb3J0aW9uIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGhlaWdodDogMzBweDsKICAgICAgICBsaW5lLWhlaWdodDogMzBweDsKICAgICAgICBmbGV4OiAxOwogICAgICAgIC5ncmFkZS1pdGVtIHsKICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgICBjb2xvcjogI2ZmZjsKICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgIGZsZXg6IDE7CiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CgogICAgICAgICAgLmdyYWRlLWFycm93IHsKICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICAgICAgICB0b3A6IC0yN3B4OyAvKiDlrprkvY3liLDnrYnnuqfmnaHkuIrmlrkgKi8KICAgICAgICAgICAgbGVmdDogNTAlOwogICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7CiAgICAgICAgICAgIHdpZHRoOiAyNHB4OwogICAgICAgICAgICBoZWlnaHQ6IDI0cHg7CiAgICAgICAgICB9CgogICAgICAgICAgJjpmaXJzdC1jaGlsZCB7CiAgICAgICAgICAgIHdpZHRoOiAyMDBweCAhaW1wb3J0YW50OyAvLyDnrKzkuIDkuKrlhYPntKDlm7rlrprlrr3luqbkuLogMjAwcHgKICAgICAgICAgICAgZmxleDogbm9uZTsgLy8g5LiN5Y+C5LiO5by55oCn5biD5bGA55qE6Ieq5Yqo5YiG6YWNCiAgICAgICAgICB9CgogICAgICAgICAgJjpub3QoOmZpcnN0LWNoaWxkKSB7CiAgICAgICAgICAgIGZsZXg6IDE7IC8vIOWFtuS9meWFg+e0oOWdh+WIhuWJqeS9meepuumXtAogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KICAucG9pbnRzLW92ZXJ2aWV3LWNvbnRhaW5lciB7CiAgICBoZWlnaHQ6IDYwMHB4OwogICAgZGlzcGxheTogZmxleDsKICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICBnYXA6IDhweDsKICAgIC5zY29yZS1kZXRhaWxzIHsKICAgICAgd2lkdGg6IDUwJTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDIwNiwyMDYsMjA2LDEpOwogICAgICAuc2NvcmUtZGV0YWlscy1jb250YWluZXIgewogICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgIGhlaWdodDogY2FsYygxMDAlIC0gMzBweCk7CiAgICAgICAgcGFkZGluZzogMTJweDsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgICAgIC5jaGFydC1jb250YWluZXIgewogICAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAgICBoZWlnaHQ6IDQ4JTsKICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjQyLDI0NCwyNDgsMC44KTsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICAgIC5wb2ludHMtbW9kdWxlIHsKICAgICAgd2lkdGg6IDUwJTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDIwNiwyMDYsMjA2LDEpOwogICAgICAucG9pbnRzLW1vZHVsZS1jb250YWluZXIgewogICAgICAgIGhlaWdodDogMTAwJTsKICAgICAgICBwYWRkaW5nOiAwIDEycHggMTJweDsKICAgICAgICAudGItZGl2LXRhYmxlIHsKICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgaGVpZ2h0OiBjYWxjKDEwMCUgLSAzMHB4KTsKICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87CiAgICAgICAgICAudGItZGl2LXRhYmxlLWl0ZW0gewogICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICBoZWlnaHQ6IDI1cHg7CiAgICAgICAgICAgIG1hcmdpbjogMjBweCAxMHB4OwogICAgICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgICAgIGNvbG9yOiByZ2JhKDE1NCwxNTQsMTU0LDEpOwogICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICAgIGltZyB7CiAgICAgICAgICAgICAgd2lkdGg6IDE1cHg7CiAgICAgICAgICAgICAgaGVpZ2h0OiAxNXB4OwogICAgICAgICAgICB9CiAgICAgICAgICAgIC50Yi1kaXYtdGFibGUtaXRlbS1kaXYgewogICAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAyMHB4OwogICAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7ICAgICAgICAgLy8g6Ziy5q2i5paH5pys5o2i6KGMCiAgICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsgICAgICAgICAgICAvLyDpmpDol4/otoXlh7rpg6jliIYKICAgICAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsgICAgIC8vIOi2heWHuumDqOWIhuaYvuekuuecgeeVpeWPtwogICAgICAgICAgICAgIHNwYW4gewogICAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgICAgICAgICAgY29sb3I6ICMzNDdDQUY7CiAgICAgICAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgLnRiLWRpdi10YWJsZS1pdGVtLWRpdi1udW0gewogICAgICAgICAgICAgIHNwYW4gewogICAgICAgICAgICAgICAgY29sb3I6ICNCRDMxMjQ7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICAudGItZGl2LXRhYmxlLWl0ZW06Zmlyc3QtY2hpbGQgewogICAgICAgICAgICBtYXJnaW46IDEwcHggMTBweCAyMHB4OwogICAgICAgICAgfQogICAgICAgICAgLnRiLWRpdi10YWJsZS1pdGVtOmxhc3QtY2hpbGQgewogICAgICAgICAgICBtYXJnaW46IDIwcHggMTBweCAwOwogICAgICAgICAgfQogICAgICAgICAgLmxvYWQtbW9yZSB7CiAgICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQoKICAuY2hhcnQtdGl0bGUtZGl2IHsKICAgIHdpZHRoOiAxMzBweDsKICAgIGhlaWdodDogMzBweDsKICAgIGxpbmUtaGVpZ2h0OiAzMHB4OwogICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgY29sb3I6IHJnYmEoNDgsMTE0LDE5OCwxKTsKICAgIGZvbnQtc2l6ZTogMTJweDsKICAgIHRleHQtYWxpZ246IHJpZ2h0OwogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyMzIsMjQ0LDI1NCwwLjIpOwogICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxODcsMTg3LDE4NywwLjUpOwogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBpbWcgewogICAgICB3aWR0aDogMThweDsKICAgICAgaGVpZ2h0OiAxOHB4OwogICAgICBtYXJnaW46IDAgMTBweDsKICAgIH0KICB9Cn0KCg=="}, {"version": 3, "sources": ["PointsOverview.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PointsOverview.vue", "sourceRoot": "src/views/threat/overview/component", "sourcesContent": ["<!--扣分总览-->\n<template>\n  <div class=\"points-overview\">\n    <div class=\"points-overview-header\">\n      <div class=\"proportion\">\n        <div class=\"score-info\">\n          <div>您的安全总体得分<span style=\"margin: 0 5px\">{{ scoringScale }}</span>；</div>\n          <div>您的资产安全状况<span>{{ scaleState }}</span><span>{{scoringComment}}</span>，建议您及时关注安全。</div>\n        </div>\n        <div class=\"grade-proportion\">\n          <div class=\"grade-item\" v-for=\"(item, index) in gradeItems\" :key=\"index\" :style=\"{ background: item.color }\">\n            <span style=\"color: #101010\">{{ item.label }}</span>\n            <!-- 添加箭头指示 -->\n            <img\n              v-if=\"item.label === scoringScale\"\n              class=\"grade-arrow\"\n              src=\"@/assets/images/overview/arrow-up-fill.png\"\n              alt=\"当前等级\"\n            />\n          </div>\n        </div>\n\n      </div>\n    </div>\n    <div class=\"points-overview-container\">\n      <div class=\"score-details\">\n        <div class=\"chart-title-div\"><img src=\"@/assets/images/overview/alarmRanking.png\" alt=\"\"/>得分详情</div>\n        <div class=\"score-details-container\" v-loading=\"scoreLoading\">\n          <!--得分-->\n          <div class=\"chart-container\">\n            <div style=\"width: 100%; height: 100%\" ref=\"speedGaugeChart\"></div>\n          </div>\n          <!--得分详情雷达图-->\n          <div class=\"chart-container\">\n            <div style=\"width: 100%; height: 100%\" ref=\"basicRadarChart\"></div>\n          </div>\n        </div>\n      </div>\n      <div class=\"points-module\">\n        <div class=\"chart-title-div\"><img src=\"@/assets/images/overview/alarmRanking.png\" alt=\"\"/>扣分详情</div>\n        <div class=\"points-module-container\" v-loading=\"pointsLoading\">\n          <div\n            class=\"tb-div-table\"\n            ref=\"scrollableTable\"\n            @scroll=\"handleScroll\"\n            v-if=\"pointsModuleData.length\">\n            <div class=\"tb-div-table-item\" v-for=\"(item, index) in pointsModuleData\" :key=\"index\">\n              <img src=\"@/assets/images/overview/fab-fa-windows.png\" alt=\"\"/>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionDate || '--' }}</div>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionType || '--' }}</div>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionLevel || '--' }}</div>\n              <div class=\"tb-div-table-item-div-num\"><span>{{ '-'+item.deductionScore || 0 }}</span></div>\n              <div class=\"tb-div-table-item-div\"><span @click=\"handleDetail(item)\">查看详情</span></div>\n            </div>\n            <div v-if=\"!isEmpty\" class=\"load-more\">\n              加载中...\n            </div>\n            <div v-if=\"isEmpty && pointsModuleData.length\" class=\"load-more\">\n              没有更多数据了\n            </div>\n          </div>\n          <div class=\"tb-div-table\" v-else>\n            <el-empty description=\"暂无数据\" :image-size=\"120\"></el-empty>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {win} from \"codemirror/src/util/dom\";\nimport { listTblDeductionDetail ,getScoreDetails } from \"@/api/aqsoc/deduction-detail/tblDeductionDetail\";\n\nexport default {\n  name: \"PointsOverview\",\n  props: {\n    deptId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n  },\n  data() {\n    return {\n      scoreLoading: false,\n      pointsLoading: false,\n      // 等级\n      gradeItems: [\n        { label: 'E', color: '#DE868F' },\n        { label: 'D', color: '#FCCA00' },\n        { label: 'C', color: '#F4CE98' },\n        { label: 'B', color: '#9ACD32' },\n        { label: 'A', color: '#CCF783' }\n      ],\n      // 扣分详情\n      pointsModuleData:[],\n      totalCount: 0,\n      //得分等级\n      scoringScale: 'A',\n      //得分评价\n      scoringComment: '优秀',\n      totalNumber: 100,\n      indicator: [],\n      value:[],\n      speedGaugeChart: null,\n      basicRadarChart: null,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20\n      },\n      scoreQueryParams: {\n        deptId: null\n      },\n      loadingMore: false,\n      hasMoreData: true,\n    }\n  },\n  async mounted() {\n    await this.getList();\n    await this.getScoreDetails();\n    this.getSpeedGaugeChart();\n    this.getBasicRadarChart();\n  },\n  created() {\n    //this.getList();\n  },\n  beforeDestroy() {\n    this.destroyCharts();\n  },\n  watch: {\n    deptId: {\n      handler(val) {\n        this.queryParams.deptId = val;\n        this.scoreQueryParams.deptId = val;\n        this.getList();\n        this.getScoreDetails();\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    scaleState() {\n      let state;\n      switch (this.scoringScale) {\n        case 'A':\n          state = '优秀';\n          break;\n        case 'B':\n          state = '良好';\n          break;\n        case 'C':\n          state = '一般';\n          break;\n        case 'D':\n          state = '差';\n          break;\n        case 'E':\n          state = '极差';\n          break;\n        default:\n          state = '未知';\n      }\n      return state;\n    },\n    isEmpty() {\n      return this.pointsModuleData.length === this.totalCount;\n    }\n  },\n  methods: {\n    destroyCharts() {\n      if (this.speedGaugeChart) {\n        window.removeEventListener('resize', this.speedGaugeChart.resizeHandler);\n        this.speedGaugeChart.dispose();\n        this.speedGaugeChart = null;\n      }\n      if (this.basicRadarChart) {\n        window.removeEventListener('resize', this.basicRadarChart.resizeHandler);\n        this.basicRadarChart.dispose();\n        this.basicRadarChart = null;\n      }\n    },\n\n    // 处理滚动事件\n    handleScroll() {\n      const scrollableDiv = this.$refs.scrollableTable;\n      // 计算是否滚动到底部（距离底部50px范围内）\n      const isBottom = scrollableDiv.scrollHeight - scrollableDiv.scrollTop <= scrollableDiv.clientHeight + 50;\n\n      if (isBottom && !this.loadingMore && this.hasMoreData) {\n        this.loadMoreData();\n      }\n    },\n\n    // 加载更多数据\n    async loadMoreData() {\n      this.loadingMore = true;\n\n      try {\n        // 增加页码\n        this.queryParams.pageNum += 1;\n\n        const response = await listTblDeductionDetail(this.queryParams);\n\n        if (response.rows && response.rows.length > 0) {\n          // 将新数据追加到现有数据\n          this.pointsModuleData = [...this.pointsModuleData, ...response.rows];\n\n          // 检查是否还有更多数据\n          this.hasMoreData = response.rows.length >= this.queryParams.pageSize;\n        } else {\n          this.hasMoreData = false;\n        }\n      } catch (error) {\n        console.error('加载更多数据失败:', error);\n        // 回退页码\n        this.queryParams.pageNum -= 1;\n      } finally {\n        this.loadingMore = false;\n      }\n    },\n\n    async getList() {\n      this.queryParams.pageNum = 1;\n      this.scoreLoading = true;\n      this.pointsLoading = true;\n      await listTblDeductionDetail(this.queryParams).then(response => {\n        this.scoreLoading = false;\n        this.pointsModuleData = response.rows;\n      });\n    },\n\n    // 得分详情数据\n    async getScoreDetails() {\n      try {\n        const response = await getScoreDetails(this.scoreQueryParams);\n        if (response.data) {\n          this.totalNumber = response.data.totalNumber;\n          this.pointsLoading = false;\n          // 使用查找表代替多重判断\n          const scoreLevels = [\n            { min: 90, scale: 'A', comment: '优秀' },\n            { min: 80, scale: 'B', comment: '良好' },\n            { min: 70, scale: 'C', comment: '一般' },\n            { min: 60, scale: 'D', comment: '差' },\n            { min: 50, scale: 'E', comment: '极差' },\n            { min: -Infinity, scale: 'E', comment: '极差' } // 默认情况\n          ];\n\n          const matchedLevel = scoreLevels.find(level => this.totalNumber >= level.min);\n          if (matchedLevel) {\n            this.scoringScale = matchedLevel.scale;\n            this.scoringComment = matchedLevel.comment;\n          }\n\n          this.indicator = response.data.indicator || [];\n          this.value = this.indicator.map(item => item?.value ?? null);\n        }\n      } catch (error) {\n        console.error('获取评分详情失败:', error);\n      }\n    },\n\n    // 具体得分\n    getSpeedGaugeChart() {\n      if (this.speedGaugeChart) return;\n      this.speedGaugeChart = this.$echarts.init(this.$refs.speedGaugeChart);\n      const resizeHandler = () => this.speedGaugeChart && this.speedGaugeChart.resize();\n      window.addEventListener('resize', resizeHandler);\n\n      this.speedGaugeChart.resizeHandler = resizeHandler;\n      this.speedGaugeChart.setOption( {\n        series: [\n          {\n            type: 'gauge',\n            radius: '90%',\n            center: ['50%', '55%'],\n            progress: {\n              show: true,\n              width: 10,\n              itemStyle: {\n                color: '#bd3124'\n              }\n            },\n            // 表盘外圈样式\n            axisLine: {\n              lineStyle: {\n                width: 10,\n              }\n            },\n            axisTick: {\n              show: true, // 显示小刻度\n              splitNumber: 5, // 小刻度的数量\n              length: -8, // 小刻度线长\n              lineStyle: {\n                color: '#63677a', // 小刻度颜色\n                width: 1 // 小刻度宽度\n              }\n            },\n            // 刻度样式\n            splitLine: {\n              length: 10,\n              lineStyle: {\n                width: 2,\n                color: '#63677a'\n              }\n            },\n            // 数值样式\n            axisLabel: {\n              distance: 10,\n              color: '#101010',\n              fontSize: 12\n            },\n            pointer: {\n              show: true,\n              length: '80%',\n              width: 5,\n              offsetCenter: [0, '0%'],\n              itemStyle: {\n                color: '#bd3124' // 指针颜色\n              }\n            },\n            // 锚点指针样式\n            anchor: {\n              show: false,\n              showAbove: false,\n              size: 10,\n              itemStyle: {\n                borderWidth: 5,\n                // 设置指针颜色\n                color: '#bd3124',    // 指针填充色\n                borderColor: '#bd3124'\n              }\n            },\n            title: {\n              show: false\n            },\n            detail: {\n              valueAnimation: true,\n              fontSize: 25,\n              offsetCenter: [0, '50%'],\n              formatter: function (value) {\n                return value.toFixed(0) + '分';\n              }\n\n            },\n            data: [\n              {\n                value: this.totalNumber\n              }\n            ],\n          }\n        ]\n      })\n    },\n    // 扣分详情雷达图\n    getBasicRadarChart() {\n      if (this.basicRadarChart) return;\n      this.basicRadarChart = this.$echarts.init(this.$refs.basicRadarChart);\n      const resizeHandler = () => this.basicRadarChart && this.basicRadarChart.resize();\n      window.addEventListener('resize', resizeHandler);\n\n      // 存储事件处理器以便后续移除\n      this.basicRadarChart.resizeHandler = resizeHandler;\n      this.basicRadarChart = this.$echarts.init(this.$refs.basicRadarChart);\n      window.addEventListener('resize', this.basicRadarChart.resize);\n      const self = this;\n      this.basicRadarChart.setOption({\n        /*legend: {\n          data: ['Allocated Budget', 'Actual Spending']\n        },*/\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            // 使用闭包中的 self 访问组件数据\n            const indicatorNames = self.indicator.map(item => {\n              return `<div>${item.name}: ${item.value}%</div>`;\n            }).join('');\n\n            return `<div>\n          <div style=\"font-weight: bold; margin-bottom: 5px;\">得分详情</div>\n          ${indicatorNames}        </div>`;\n          }\n        },\n        radar: {\n          center: ['50%', '52%'],\n          radius: ['25%', '70%'],\n          // shape: 'circle',\n          indicator: this.indicator,\n          name: {\n            textStyle: {\n              color: '#101010'\n            }\n          },\n          splitArea: {\n            show: false\n          },\n          // 轴线样式等\n          axisLine: {\n            show: false,\n            lineStyle: {\n              color: '#666', // 轴线颜色\n            }\n          }\n        },\n        series: [\n          {\n            name: 'Budget vs spending',\n            type: 'radar',\n            data: [\n              {\n                value: this.value,\n                name: 'Actual Spending',\n                itemStyle: {\n                  color: '#d97559'\n                }\n              }\n            ],\n          }\n        ]\n      })\n    },\n\n    // 详情\n    handleDetail(row) {\n      if (row.riskType === '外部威胁') {\n        this.$router.push({\n          path: '/service-ledger/theratManage',\n          query: {\n            type: '1',\n            referenceId: row.referenceId\n          }\n        });\n      }\n\n      if (row.riskType === '内部漏洞') {\n        let queryParams = {};\n        if (row.deductionType === '主机漏洞') {\n          queryParams = {\n            type: '1',\n            referenceId: row.referenceId\n          }\n        }\n        if (row.deductionType === 'Web漏洞') {\n          queryParams = {\n            type: '2',\n            referenceId: row.referenceId\n          }\n        }\n        if (row.deductionType === '弱口令') {\n          queryParams = {\n            type: '3',\n            referenceId: row.referenceId\n          }\n        }\n        this.$router.push({\n          path: '/service-ledger/frailty',\n          query: queryParams\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.points-overview {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  .points-overview-header {\n    width: 100%;\n    height: 57px;\n    display: flex;\n    flex-direction: column;\n    .proportion {\n      width: 100%;\n      height: 30px;\n      flex: 1;\n      margin-top: 27px;\n      display: flex;\n      align-items: center;\n      .score-info {\n        width: 50%;\n        height: 30px;\n        line-height: 30px;\n        background: #E8F4FE;\n        display: flex;\n        font-size: 12px;\n        color: #101010;\n        div {\n          span {\n            color: #bd3124;\n          }\n          &:first-child {\n            text-indent: 15px;\n          }\n        }\n      }\n\n      .grade-proportion {\n        display: flex;\n        height: 30px;\n        line-height: 30px;\n        flex: 1;\n        .grade-item {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #fff;\n          font-size: 12px;\n          flex: 1;\n          position: relative;\n\n          .grade-arrow {\n            position: absolute;\n            top: -27px; /* 定位到等级条上方 */\n            left: 50%;\n            transform: translateX(-50%);\n            width: 24px;\n            height: 24px;\n          }\n\n          &:first-child {\n            width: 200px !important; // 第一个元素固定宽度为 200px\n            flex: none; // 不参与弹性布局的自动分配\n          }\n\n          &:not(:first-child) {\n            flex: 1; // 其余元素均分剩余空间\n          }\n        }\n      }\n    }\n  }\n  .points-overview-container {\n    height: 600px;\n    display: flex;\n    flex-direction: row;\n    gap: 8px;\n    .score-details {\n      width: 50%;\n      border-radius: 4px;\n      border: 1px solid rgba(206,206,206,1);\n      .score-details-container {\n        width: 100%;\n        height: calc(100% - 30px);\n        padding: 12px;\n        display: flex;\n        flex-direction: column;\n        justify-content: space-between;\n        .chart-container {\n          width: 100%;\n          height: 48%;\n          background-color: rgba(242,244,248,0.8);\n        }\n      }\n    }\n    .points-module {\n      width: 50%;\n      border-radius: 4px;\n      border: 1px solid rgba(206,206,206,1);\n      .points-module-container {\n        height: 100%;\n        padding: 0 12px 12px;\n        .tb-div-table {\n          width: 100%;\n          height: calc(100% - 30px);\n          overflow-y: auto;\n          .tb-div-table-item {\n            display: flex;\n            height: 25px;\n            margin: 20px 10px;\n            font-size: 14px;\n            color: rgba(154,154,154,1);\n            justify-content: space-between;\n            align-items: center;\n            img {\n              width: 15px;\n              height: 15px;\n            }\n            .tb-div-table-item-div {\n              margin-left: 20px;\n              white-space: nowrap;         // 防止文本换行\n              overflow: hidden;            // 隐藏超出部分\n              text-overflow: ellipsis;     // 超出部分显示省略号\n              span {\n                cursor: pointer;\n                color: #347CAF;\n                text-decoration: underline;\n              }\n            }\n            .tb-div-table-item-div-num {\n              span {\n                color: #BD3124;\n              }\n            }\n          }\n          .tb-div-table-item:first-child {\n            margin: 10px 10px 20px;\n          }\n          .tb-div-table-item:last-child {\n            margin: 20px 10px 0;\n          }\n          .load-more {\n            width: 100%;\n            text-align: center;\n          }\n        }\n      }\n    }\n  }\n\n  .chart-title-div {\n    width: 130px;\n    height: 30px;\n    line-height: 30px;\n    border-radius: 4px;\n    color: rgba(48,114,198,1);\n    font-size: 12px;\n    text-align: right;\n    background-color: rgba(232,244,254,0.2);\n    border: 1px solid rgba(187,187,187,0.5);\n    display: flex;\n    align-items: center;\n    img {\n      width: 18px;\n      height: 18px;\n      margin: 0 10px;\n    }\n  }\n}\n\n</style>\n"]}]}