package com.ruoyi.ffsafe.scantaskapi.domain;

import com.aspose.slides.internal.oe.pr;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * 主机漏扫记录详细信息VO类
 * 包含任务名称、扫描目标、存活主机数量、弱口令数量等扩展字段
 * 
 * @date 2025-08-14
 */
@Data
public class FfsafeScantaskSummaryDetailVO extends FfsafeScantaskSummary {
    private static final long serialVersionUID = 1L;

    /** 任务名称 - 从sys_job表获取 */
    @Excel(name = "任务名称")
    private String jobName;

    /** 扫描目标 - 解析后的目标地址 */
    @Excel(name = "扫描目标")
    private String scanTarget;

    /** 原始扫描目标数据 - 存储invoke_target原始数据 */
    private String scanTargetRaw;

    /** 存活主机数量 - 统计ffsafe_hostscan_taskresult表记录数 */
    @Excel(name = "存活主机数量")
    private Integer hostNum;

    /** 弱口令数量 - 统计ffsafe_hostscan_wpresult表记录数 */
    @Excel(name = "弱口令数量")
    private Integer pwNum;

    /** 任务类型，0:探活,1:主机漏扫,2:Web漏扫 - 从sys_job表获取*/
    @Excel(name = "扫描策略", readConverterExp = "0=探活任务,1=主机漏扫,2=Web漏扫")
    private Integer jobType;
    /** 前端需要显示的ip地址，值与scanTarget同 */
    private String ipShow;

}
