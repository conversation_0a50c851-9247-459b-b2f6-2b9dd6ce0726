<!--扣分总览-->
<template>
  <div class="points-overview">
    <div class="points-overview-header">
      <div class="proportion">
        <div class="score-info">
          <div>您的安全总体得分<span style="margin: 0 5px">{{ scoringScale }}</span>；</div>
          <div>您的资产安全状况<span>{{ scaleState }}</span><span>{{scoringComment}}</span>，建议您及时关注安全。</div>
        </div>
        <div class="grade-proportion">
          <div class="grade-item" v-for="(item, index) in gradeItems" :key="index" :style="{ background: item.color }">
            <span style="color: #101010">{{ item.label }}</span>
            <!-- 添加箭头指示 -->
            <img
              v-if="item.label === scoringScale"
              class="grade-arrow"
              src="@/assets/images/overview/arrow-up-fill.png"
              alt="当前等级"
            />
          </div>
        </div>

      </div>
    </div>
    <div class="points-overview-container">
      <div class="score-details">
        <div class="chart-title-div"><img src="@/assets/images/overview/alarmRanking.png" alt=""/>得分详情</div>
        <div class="score-details-container" v-loading="scoreLoading">
          <!--得分-->
          <div class="chart-container">
            <div style="width: 100%; height: 100%" ref="speedGaugeChart"></div>
          </div>
          <!--得分详情雷达图-->
          <div class="chart-container">
            <div style="width: 100%; height: 100%" ref="basicRadarChart"></div>
          </div>
        </div>
      </div>
      <div class="points-module">
        <div class="chart-title-div"><img src="@/assets/images/overview/alarmRanking.png" alt=""/>扣分详情</div>
        <div class="points-module-container" v-loading="pointsLoading">
          <div
            class="tb-div-table"
            ref="scrollableTable"
            @scroll="handleScroll"
            v-if="pointsModuleData.length">
            <div class="tb-div-table-item" v-for="(item, index) in pointsModuleData" :key="index">
              <img src="@/assets/images/overview/fab-fa-windows.png" alt=""/>
              <div class="tb-div-table-item-div">{{ item.deductionDate || '--' }}</div>
              <div class="tb-div-table-item-div">{{ item.deductionType || '--' }}</div>
              <div class="tb-div-table-item-div">{{ item.deductionLevel || '--' }}</div>
              <div class="tb-div-table-item-div-num"><span>{{ '-'+item.deductionScore || 0 }}</span></div>
              <div class="tb-div-table-item-div"><span @click="handleDetail(item)">查看详情</span></div>
            </div>
            <div v-if="!isEmpty" class="load-more">
              加载中...
            </div>
            <div v-if="isEmpty && pointsModuleData.length" class="load-more">
              没有更多数据了
            </div>
          </div>
          <div class="tb-div-table" v-else>
            <el-empty description="暂无数据" :image-size="120"></el-empty>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {win} from "codemirror/src/util/dom";
import { listTblDeductionDetail ,getScoreDetails } from "@/api/aqsoc/deduction-detail/tblDeductionDetail";

export default {
  name: "PointsOverview",
  props: {
    deptId: {
      type: [String, Number],
      required: false,
      default: null,
    },
  },
  data() {
    return {
      scoreLoading: false,
      pointsLoading: false,
      // 等级
      gradeItems: [
        { label: 'E', color: '#DE868F' },
        { label: 'D', color: '#FCCA00' },
        { label: 'C', color: '#F4CE98' },
        { label: 'B', color: '#9ACD32' },
        { label: 'A', color: '#CCF783' }
      ],
      // 扣分详情
      pointsModuleData:[],
      totalCount: 0,
      //得分等级
      scoringScale: 'A',
      //得分评价
      scoringComment: '优秀',
      totalNumber: 100,
      indicator: [],
      value:[],
      speedGaugeChart: null,
      basicRadarChart: null,
      queryParams: {
        pageNum: 1,
        pageSize: 20
      },
      scoreQueryParams: {
        deptId: null
      },
      loadingMore: false,
      hasMoreData: true,
    }
  },
  async mounted() {
    await this.getList();
    await this.getScoreDetails();
    this.getSpeedGaugeChart();
    this.getBasicRadarChart();
  },
  created() {
    //this.getList();
  },
  beforeDestroy() {
    this.destroyCharts();
  },
  watch: {
    deptId: {
      handler(val) {
        this.queryParams.deptId = val;
        this.scoreQueryParams.deptId = val;
        this.getList();
        this.getScoreDetails();
      },
      immediate: true
    }
  },
  computed: {
    scaleState() {
      let state;
      switch (this.scoringScale) {
        case 'A':
          state = '优秀';
          break;
        case 'B':
          state = '良好';
          break;
        case 'C':
          state = '一般';
          break;
        case 'D':
          state = '差';
          break;
        case 'E':
          state = '极差';
          break;
        default:
          state = '未知';
      }
      return state;
    },
    isEmpty() {
      return this.pointsModuleData.length === this.totalCount;
    }
  },
  methods: {
    destroyCharts() {
      if (this.speedGaugeChart) {
        window.removeEventListener('resize', this.speedGaugeChart.resizeHandler);
        this.speedGaugeChart.dispose();
        this.speedGaugeChart = null;
      }
      if (this.basicRadarChart) {
        window.removeEventListener('resize', this.basicRadarChart.resizeHandler);
        this.basicRadarChart.dispose();
        this.basicRadarChart = null;
      }
    },

    // 处理滚动事件
    handleScroll() {
      const scrollableDiv = this.$refs.scrollableTable;
      // 计算是否滚动到底部（距离底部50px范围内）
      const isBottom = scrollableDiv.scrollHeight - scrollableDiv.scrollTop <= scrollableDiv.clientHeight + 50;

      if (isBottom && !this.loadingMore && this.hasMoreData) {
        this.loadMoreData();
      }
    },

    // 加载更多数据
    async loadMoreData() {
      this.loadingMore = true;

      try {
        // 增加页码
        this.queryParams.pageNum += 1;

        const response = await listTblDeductionDetail(this.queryParams);

        if (response.rows && response.rows.length > 0) {
          // 将新数据追加到现有数据
          this.pointsModuleData = [...this.pointsModuleData, ...response.rows];

          // 检查是否还有更多数据
          this.hasMoreData = response.rows.length >= this.queryParams.pageSize;
        } else {
          this.hasMoreData = false;
        }
      } catch (error) {
        console.error('加载更多数据失败:', error);
        // 回退页码
        this.queryParams.pageNum -= 1;
      } finally {
        this.loadingMore = false;
      }
    },

    async getList() {
      this.queryParams.pageNum = 1;
      this.scoreLoading = true;
      this.pointsLoading = true;
      await listTblDeductionDetail(this.queryParams).then(response => {
        this.scoreLoading = false;
        this.pointsModuleData = response.rows;
      });
    },

    // 得分详情数据
    async getScoreDetails() {
      try {
        const response = await getScoreDetails(this.scoreQueryParams);
        if (response.data) {
          this.totalNumber = response.data.totalNumber;
          this.pointsLoading = false;
          // 使用查找表代替多重判断
          const scoreLevels = [
            { min: 90, scale: 'A', comment: '优秀' },
            { min: 80, scale: 'B', comment: '良好' },
            { min: 70, scale: 'C', comment: '一般' },
            { min: 60, scale: 'D', comment: '差' },
            { min: 50, scale: 'E', comment: '极差' },
            { min: -Infinity, scale: 'E', comment: '极差' } // 默认情况
          ];

          const matchedLevel = scoreLevels.find(level => this.totalNumber >= level.min);
          if (matchedLevel) {
            this.scoringScale = matchedLevel.scale;
            this.scoringComment = matchedLevel.comment;
          }

          this.indicator = response.data.indicator || [];
          this.value = this.indicator.map(item => item?.value ?? null);
        }
      } catch (error) {
        console.error('获取评分详情失败:', error);
      }
    },

    // 具体得分
    getSpeedGaugeChart() {
      if (this.speedGaugeChart) return;
      this.speedGaugeChart = this.$echarts.init(this.$refs.speedGaugeChart);
      const resizeHandler = () => this.speedGaugeChart && this.speedGaugeChart.resize();
      window.addEventListener('resize', resizeHandler);

      this.speedGaugeChart.resizeHandler = resizeHandler;
      this.speedGaugeChart.setOption( {
        series: [
          {
            type: 'gauge',
            radius: '90%',
            center: ['50%', '55%'],
            progress: {
              show: true,
              width: 10,
              itemStyle: {
                color: '#bd3124'
              }
            },
            // 表盘外圈样式
            axisLine: {
              lineStyle: {
                width: 10,
              }
            },
            axisTick: {
              show: true, // 显示小刻度
              splitNumber: 5, // 小刻度的数量
              length: -8, // 小刻度线长
              lineStyle: {
                color: '#63677a', // 小刻度颜色
                width: 1 // 小刻度宽度
              }
            },
            // 刻度样式
            splitLine: {
              length: 10,
              lineStyle: {
                width: 2,
                color: '#63677a'
              }
            },
            // 数值样式
            axisLabel: {
              distance: 10,
              color: '#101010',
              fontSize: 12
            },
            pointer: {
              show: true,
              length: '80%',
              width: 5,
              offsetCenter: [0, '0%'],
              itemStyle: {
                color: '#bd3124' // 指针颜色
              }
            },
            // 锚点指针样式
            anchor: {
              show: false,
              showAbove: false,
              size: 10,
              itemStyle: {
                borderWidth: 5,
                // 设置指针颜色
                color: '#bd3124',    // 指针填充色
                borderColor: '#bd3124'
              }
            },
            title: {
              show: false
            },
            detail: {
              valueAnimation: true,
              fontSize: 25,
              offsetCenter: [0, '50%'],
              formatter: function (value) {
                return value.toFixed(0) + '分';
              }

            },
            data: [
              {
                value: this.totalNumber
              }
            ],
          }
        ]
      })
    },
    // 扣分详情雷达图
    getBasicRadarChart() {
      if (this.basicRadarChart) return;
      this.basicRadarChart = this.$echarts.init(this.$refs.basicRadarChart);
      const resizeHandler = () => this.basicRadarChart && this.basicRadarChart.resize();
      window.addEventListener('resize', resizeHandler);

      // 存储事件处理器以便后续移除
      this.basicRadarChart.resizeHandler = resizeHandler;
      this.basicRadarChart = this.$echarts.init(this.$refs.basicRadarChart);
      window.addEventListener('resize', this.basicRadarChart.resize);
      const self = this;
      this.basicRadarChart.setOption({
        /*legend: {
          data: ['Allocated Budget', 'Actual Spending']
        },*/
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            // 使用闭包中的 self 访问组件数据
            const indicatorNames = self.indicator.map(item => {
              return `<div>${item.name}: ${item.value}%</div>`;
            }).join('');

            return `<div>
          <div style="font-weight: bold; margin-bottom: 5px;">得分详情</div>
          ${indicatorNames}        </div>`;
          }
        },
        radar: {
          center: ['50%', '52%'],
          radius: ['25%', '70%'],
          // shape: 'circle',
          indicator: this.indicator,
          name: {
            textStyle: {
              color: '#101010'
            }
          },
          splitArea: {
            show: false
          },
          // 轴线样式等
          axisLine: {
            show: false,
            lineStyle: {
              color: '#666', // 轴线颜色
            }
          }
        },
        series: [
          {
            name: 'Budget vs spending',
            type: 'radar',
            data: [
              {
                value: this.value,
                name: 'Actual Spending',
                itemStyle: {
                  color: '#d97559'
                }
              }
            ],
          }
        ]
      })
    },

    // 详情
    handleDetail(row) {
      if (row.riskType === '外部威胁') {
        this.$router.push({
          path: '/service-ledger/theratManage',
          query: {
            type: '1',
            referenceId: row.referenceId
          }
        });
      }

      if (row.riskType === '内部漏洞') {
        let queryParams = {};
        if (row.deductionType === '主机漏洞') {
          queryParams = {
            type: '1',
            referenceId: row.referenceId
          }
        }
        if (row.deductionType === 'Web漏洞') {
          queryParams = {
            type: '2',
            referenceId: row.referenceId
          }
        }
        if (row.deductionType === '弱口令') {
          queryParams = {
            type: '3',
            referenceId: row.referenceId
          }
        }
        this.$router.push({
          path: '/service-ledger/frailty',
          query: queryParams
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.points-overview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  .points-overview-header {
    width: 100%;
    height: 57px;
    display: flex;
    flex-direction: column;
    .proportion {
      width: 100%;
      height: 30px;
      flex: 1;
      margin-top: 27px;
      display: flex;
      align-items: center;
      .score-info {
        width: 50%;
        height: 30px;
        line-height: 30px;
        background: #E8F4FE;
        display: flex;
        font-size: 12px;
        color: #101010;
        div {
          span {
            color: #bd3124;
          }
          &:first-child {
            text-indent: 15px;
          }
        }
      }

      .grade-proportion {
        display: flex;
        height: 30px;
        line-height: 30px;
        flex: 1;
        .grade-item {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 12px;
          flex: 1;
          position: relative;

          .grade-arrow {
            position: absolute;
            top: -27px; /* 定位到等级条上方 */
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 24px;
          }

          &:first-child {
            width: 200px !important; // 第一个元素固定宽度为 200px
            flex: none; // 不参与弹性布局的自动分配
          }

          &:not(:first-child) {
            flex: 1; // 其余元素均分剩余空间
          }
        }
      }
    }
  }
  .points-overview-container {
    height: 600px;
    display: flex;
    flex-direction: row;
    gap: 8px;
    .score-details {
      width: 50%;
      border-radius: 4px;
      border: 1px solid rgba(206,206,206,1);
      .score-details-container {
        width: 100%;
        height: calc(100% - 30px);
        padding: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .chart-container {
          width: 100%;
          height: 48%;
          background-color: rgba(242,244,248,0.8);
        }
      }
    }
    .points-module {
      width: 50%;
      border-radius: 4px;
      border: 1px solid rgba(206,206,206,1);
      .points-module-container {
        height: 100%;
        padding: 0 12px 12px;
        .tb-div-table {
          width: 100%;
          height: calc(100% - 30px);
          overflow-y: auto;
          .tb-div-table-item {
            display: flex;
            height: 25px;
            margin: 20px 10px;
            font-size: 14px;
            color: rgba(154,154,154,1);
            justify-content: space-between;
            align-items: center;
            img {
              width: 15px;
              height: 15px;
            }
            .tb-div-table-item-div {
              margin-left: 20px;
              white-space: nowrap;         // 防止文本换行
              overflow: hidden;            // 隐藏超出部分
              text-overflow: ellipsis;     // 超出部分显示省略号
              span {
                cursor: pointer;
                color: #347CAF;
                text-decoration: underline;
              }
            }
            .tb-div-table-item-div-num {
              span {
                color: #BD3124;
              }
            }
          }
          .tb-div-table-item:first-child {
            margin: 10px 10px 20px;
          }
          .tb-div-table-item:last-child {
            margin: 20px 10px 0;
          }
          .load-more {
            width: 100%;
            text-align: center;
          }
        }
      }
    }
  }

  .chart-title-div {
    width: 130px;
    height: 30px;
    line-height: 30px;
    border-radius: 4px;
    color: rgba(48,114,198,1);
    font-size: 12px;
    text-align: right;
    background-color: rgba(232,244,254,0.2);
    border: 1px solid rgba(187,187,187,0.5);
    display: flex;
    align-items: center;
    img {
      width: 18px;
      height: 18px;
      margin: 0 10px;
    }
  }
}

</style>
