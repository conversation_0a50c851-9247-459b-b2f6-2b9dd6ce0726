package com.ruoyi.ffsafe.api.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.monitor2.domain.NmapConfig;

import java.util.List;

/**
 * 设备接入配置Service接口
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface ITblDeviceConfigService
{
    /**
     * 查询设备接入配置
     *
     * @param id 设备接入配置主键
     * @return 设备接入配置
     */
    public TblDeviceConfig selectTblDeviceConfigById(Long id);

    /**
     * 批量查询设备接入配置
     *
     * @param ids 设备接入配置主键集合
     * @return 设备接入配置集合
     */
    public List<TblDeviceConfig> selectTblDeviceConfigByIds(Long[] ids);

    /**
     * 查询设备接入配置列表
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 设备接入配置集合
     */
    public List<TblDeviceConfig> selectTblDeviceConfigList(TblDeviceConfig tblDeviceConfig);

    /**
     * 新增设备接入配置
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 结果
     */
    public int insertTblDeviceConfig(TblDeviceConfig tblDeviceConfig);

    /**
     * 修改设备接入配置
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 结果
     */
    public int updateTblDeviceConfig(TblDeviceConfig tblDeviceConfig);

    /**
     * 删除设备接入配置信息
     *
     * @param id 设备接入配置主键
     * @return 结果
     */
    public int deleteTblDeviceConfigById(Long id);

    /**
     * 批量删除设备接入配置
     *
     * @param ids 需要删除的设备接入配置主键集合
     * @return 结果
     */
    public int deleteTblDeviceConfigByIds(Long[] ids);

    List<JSONObject> selectTargetDeviceConfig(String targets);

    TblDeviceConfig selectDeviceConfigOrDefault(Long deviceId);

    void updateLastTime(TblDeviceConfig update);

    FfsafeApiConfig getFfsafeApiConfig(TblDeviceConfig deviceConfig);

    NmapConfig getNmapConfig(TblDeviceConfig deviceConfig);
}
