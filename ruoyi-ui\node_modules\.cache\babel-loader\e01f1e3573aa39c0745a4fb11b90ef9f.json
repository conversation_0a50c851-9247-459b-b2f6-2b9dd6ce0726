{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\threat\\overview\\component\\PointsOverview.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\threat\\overview\\component\\PointsOverview.vue", "mtime": 1755762306448}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "names": ["_dom", "require", "_tblDeductionDetail", "name", "props", "deptId", "type", "String", "Number", "required", "default", "data", "scoreLoading", "pointsLoading", "gradeItems", "label", "color", "pointsModuleData", "totalCount", "scoringScale", "scoringComment", "totalNumber", "indicator", "value", "speedGaugeChart", "basicRadarChart", "queryParams", "pageNum", "pageSize", "scoreQueryParams", "loadingMore", "hasMoreData", "mounted", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "getList", "getScoreDetails", "getSpeedGaugeChart", "getBasicRadarChart", "stop", "created", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "watch", "handler", "val", "immediate", "computed", "scaleState", "state", "isEmpty", "length", "methods", "window", "removeEventListener", "resize<PERSON><PERSON>ler", "dispose", "handleScroll", "scrollableDiv", "$refs", "scrollableTable", "isBottom", "scrollHeight", "scrollTop", "clientHeight", "loadMoreData", "_this2", "_callee2", "response", "_callee2$", "_context2", "listTblDeductionDetail", "sent", "rows", "concat", "_toConsumableArray2", "t0", "console", "error", "finish", "_this3", "_callee3", "_callee3$", "_context3", "then", "_this4", "_callee4", "scoreLevels", "matchedLevel", "_callee4$", "_context4", "min", "scale", "comment", "Infinity", "find", "level", "map", "item", "_item$value", "_this5", "$echarts", "init", "resize", "addEventListener", "setOption", "series", "radius", "center", "progress", "show", "width", "itemStyle", "axisLine", "lineStyle", "axisTick", "splitNumber", "splitLine", "axisLabel", "distance", "fontSize", "pointer", "offsetCenter", "anchor", "showAbove", "size", "borderWidth", "borderColor", "title", "detail", "valueAnimation", "formatter", "toFixed", "_this6", "self", "tooltip", "trigger", "params", "indicatorNames", "join", "radar", "textStyle", "splitArea", "handleDetail", "row", "riskType", "$router", "push", "path", "query", "referenceId", "deductionType"], "sources": ["src/views/threat/overview/component/PointsOverview.vue"], "sourcesContent": ["<!--扣分总览-->\n<template>\n  <div class=\"points-overview\">\n    <div class=\"points-overview-header\">\n      <div class=\"proportion\">\n        <div class=\"score-info\">\n          <div>您的安全总体得分<span style=\"margin: 0 5px\">{{ scoringScale }}</span>；</div>\n          <div>您的资产安全状况<span>{{ scaleState }}</span><span>{{scoringComment}}</span>，建议您及时关注安全。</div>\n        </div>\n        <div class=\"grade-proportion\">\n          <div class=\"grade-item\" v-for=\"(item, index) in gradeItems\" :key=\"index\" :style=\"{ background: item.color }\">\n            <span style=\"color: #101010\">{{ item.label }}</span>\n            <!-- 添加箭头指示 -->\n            <img\n              v-if=\"item.label === scoringScale\"\n              class=\"grade-arrow\"\n              src=\"@/assets/images/overview/arrow-up-fill.png\"\n              alt=\"当前等级\"\n            />\n          </div>\n        </div>\n\n      </div>\n    </div>\n    <div class=\"points-overview-container\">\n      <div class=\"score-details\">\n        <div class=\"chart-title-div\"><img src=\"@/assets/images/overview/alarmRanking.png\" alt=\"\"/>得分详情</div>\n        <div class=\"score-details-container\" v-loading=\"scoreLoading\">\n          <!--得分-->\n          <div class=\"chart-container\">\n            <div style=\"width: 100%; height: 100%\" ref=\"speedGaugeChart\"></div>\n          </div>\n          <!--得分详情雷达图-->\n          <div class=\"chart-container\">\n            <div style=\"width: 100%; height: 100%\" ref=\"basicRadarChart\"></div>\n          </div>\n        </div>\n      </div>\n      <div class=\"points-module\">\n        <div class=\"chart-title-div\"><img src=\"@/assets/images/overview/alarmRanking.png\" alt=\"\"/>扣分详情</div>\n        <div class=\"points-module-container\" v-loading=\"pointsLoading\">\n          <div\n            class=\"tb-div-table\"\n            ref=\"scrollableTable\"\n            @scroll=\"handleScroll\"\n            v-if=\"pointsModuleData.length\">\n            <div class=\"tb-div-table-item\" v-for=\"(item, index) in pointsModuleData\" :key=\"index\">\n              <img src=\"@/assets/images/overview/fab-fa-windows.png\" alt=\"\"/>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionDate || '--' }}</div>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionType || '--' }}</div>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionLevel || '--' }}</div>\n              <div class=\"tb-div-table-item-div-num\"><span>{{ '-'+item.deductionScore || 0 }}</span></div>\n              <div class=\"tb-div-table-item-div\"><span @click=\"handleDetail(item)\">查看详情</span></div>\n            </div>\n            <div v-if=\"!isEmpty\" class=\"load-more\">\n              加载中...\n            </div>\n            <div v-if=\"isEmpty && pointsModuleData.length\" class=\"load-more\">\n              没有更多数据了\n            </div>\n          </div>\n          <div class=\"tb-div-table\" v-else>\n            <el-empty description=\"暂无数据\" :image-size=\"120\"></el-empty>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {win} from \"codemirror/src/util/dom\";\nimport { listTblDeductionDetail ,getScoreDetails } from \"@/api/aqsoc/deduction-detail/tblDeductionDetail\";\n\nexport default {\n  name: \"PointsOverview\",\n  props: {\n    deptId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n  },\n  data() {\n    return {\n      scoreLoading: false,\n      pointsLoading: false,\n      // 等级\n      gradeItems: [\n        { label: 'E', color: '#DE868F' },\n        { label: 'D', color: '#FCCA00' },\n        { label: 'C', color: '#F4CE98' },\n        { label: 'B', color: '#9ACD32' },\n        { label: 'A', color: '#CCF783' }\n      ],\n      // 扣分详情\n      pointsModuleData:[],\n      totalCount: 0,\n      //得分等级\n      scoringScale: 'A',\n      //得分评价\n      scoringComment: '优秀',\n      totalNumber: 100,\n      indicator: [],\n      value:[],\n      speedGaugeChart: null,\n      basicRadarChart: null,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20\n      },\n      scoreQueryParams: {\n        deptId: null\n      },\n      loadingMore: false,\n      hasMoreData: true,\n    }\n  },\n  async mounted() {\n    await this.getList();\n    await this.getScoreDetails();\n    this.getSpeedGaugeChart();\n    this.getBasicRadarChart();\n  },\n  created() {\n    //this.getList();\n  },\n  beforeDestroy() {\n    this.destroyCharts();\n  },\n  watch: {\n    deptId: {\n      handler(val) {\n        this.queryParams.deptId = val;\n        this.scoreQueryParams.deptId = val;\n        this.getList();\n        this.getScoreDetails();\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    scaleState() {\n      let state;\n      switch (this.scoringScale) {\n        case 'A':\n          state = '优秀';\n          break;\n        case 'B':\n          state = '良好';\n          break;\n        case 'C':\n          state = '一般';\n          break;\n        case 'D':\n          state = '差';\n          break;\n        case 'E':\n          state = '极差';\n          break;\n        default:\n          state = '未知';\n      }\n      return state;\n    },\n    isEmpty() {\n      return this.pointsModuleData.length === this.totalCount;\n    }\n  },\n  methods: {\n    destroyCharts() {\n      if (this.speedGaugeChart) {\n        window.removeEventListener('resize', this.speedGaugeChart.resizeHandler);\n        this.speedGaugeChart.dispose();\n        this.speedGaugeChart = null;\n      }\n      if (this.basicRadarChart) {\n        window.removeEventListener('resize', this.basicRadarChart.resizeHandler);\n        this.basicRadarChart.dispose();\n        this.basicRadarChart = null;\n      }\n    },\n\n    // 处理滚动事件\n    handleScroll() {\n      const scrollableDiv = this.$refs.scrollableTable;\n      // 计算是否滚动到底部（距离底部50px范围内）\n      const isBottom = scrollableDiv.scrollHeight - scrollableDiv.scrollTop <= scrollableDiv.clientHeight + 50;\n\n      if (isBottom && !this.loadingMore && this.hasMoreData) {\n        this.loadMoreData();\n      }\n    },\n\n    // 加载更多数据\n    async loadMoreData() {\n      this.loadingMore = true;\n\n      try {\n        // 增加页码\n        this.queryParams.pageNum += 1;\n\n        const response = await listTblDeductionDetail(this.queryParams);\n\n        if (response.rows && response.rows.length > 0) {\n          // 将新数据追加到现有数据\n          this.pointsModuleData = [...this.pointsModuleData, ...response.rows];\n\n          // 检查是否还有更多数据\n          this.hasMoreData = response.rows.length >= this.queryParams.pageSize;\n        } else {\n          this.hasMoreData = false;\n        }\n      } catch (error) {\n        console.error('加载更多数据失败:', error);\n        // 回退页码\n        this.queryParams.pageNum -= 1;\n      } finally {\n        this.loadingMore = false;\n      }\n    },\n\n    async getList() {\n      this.queryParams.pageNum = 1;\n      this.scoreLoading = true;\n      this.pointsLoading = true;\n      await listTblDeductionDetail(this.queryParams).then(response => {\n        this.scoreLoading = false;\n        this.pointsModuleData = response.rows;\n      });\n    },\n\n    // 得分详情数据\n    async getScoreDetails() {\n      try {\n        const response = await getScoreDetails(this.scoreQueryParams);\n        if (response.data) {\n          this.totalNumber = response.data.totalNumber;\n          this.pointsLoading = false;\n          // 使用查找表代替多重判断\n          const scoreLevels = [\n            { min: 90, scale: 'A', comment: '优秀' },\n            { min: 80, scale: 'B', comment: '良好' },\n            { min: 70, scale: 'C', comment: '一般' },\n            { min: 60, scale: 'D', comment: '差' },\n            { min: 50, scale: 'E', comment: '极差' },\n            { min: -Infinity, scale: 'E', comment: '极差' } // 默认情况\n          ];\n\n          const matchedLevel = scoreLevels.find(level => this.totalNumber >= level.min);\n          if (matchedLevel) {\n            this.scoringScale = matchedLevel.scale;\n            this.scoringComment = matchedLevel.comment;\n          }\n\n          this.indicator = response.data.indicator || [];\n          this.value = this.indicator.map(item => item?.value ?? null);\n        }\n      } catch (error) {\n        console.error('获取评分详情失败:', error);\n      }\n    },\n\n    // 具体得分\n    getSpeedGaugeChart() {\n      if (this.speedGaugeChart) return;\n      this.speedGaugeChart = this.$echarts.init(this.$refs.speedGaugeChart);\n      const resizeHandler = () => this.speedGaugeChart && this.speedGaugeChart.resize();\n      window.addEventListener('resize', resizeHandler);\n\n      this.speedGaugeChart.resizeHandler = resizeHandler;\n      this.speedGaugeChart.setOption( {\n        series: [\n          {\n            type: 'gauge',\n            radius: '90%',\n            center: ['50%', '55%'],\n            progress: {\n              show: true,\n              width: 10,\n              itemStyle: {\n                color: '#bd3124'\n              }\n            },\n            // 表盘外圈样式\n            axisLine: {\n              lineStyle: {\n                width: 10,\n              }\n            },\n            axisTick: {\n              show: true, // 显示小刻度\n              splitNumber: 5, // 小刻度的数量\n              length: -8, // 小刻度线长\n              lineStyle: {\n                color: '#63677a', // 小刻度颜色\n                width: 1 // 小刻度宽度\n              }\n            },\n            // 刻度样式\n            splitLine: {\n              length: 10,\n              lineStyle: {\n                width: 2,\n                color: '#63677a'\n              }\n            },\n            // 数值样式\n            axisLabel: {\n              distance: 10,\n              color: '#101010',\n              fontSize: 12\n            },\n            pointer: {\n              show: true,\n              length: '80%',\n              width: 5,\n              offsetCenter: [0, '0%'],\n              itemStyle: {\n                color: '#bd3124' // 指针颜色\n              }\n            },\n            // 锚点指针样式\n            anchor: {\n              show: false,\n              showAbove: false,\n              size: 10,\n              itemStyle: {\n                borderWidth: 5,\n                // 设置指针颜色\n                color: '#bd3124',    // 指针填充色\n                borderColor: '#bd3124'\n              }\n            },\n            title: {\n              show: false\n            },\n            detail: {\n              valueAnimation: true,\n              fontSize: 25,\n              offsetCenter: [0, '50%'],\n              formatter: function (value) {\n                return value.toFixed(0) + '分';\n              }\n\n            },\n            data: [\n              {\n                value: this.totalNumber\n              }\n            ],\n          }\n        ]\n      })\n    },\n    // 扣分详情雷达图\n    getBasicRadarChart() {\n      if (this.basicRadarChart) return;\n      this.basicRadarChart = this.$echarts.init(this.$refs.basicRadarChart);\n      const resizeHandler = () => this.basicRadarChart && this.basicRadarChart.resize();\n      window.addEventListener('resize', resizeHandler);\n\n      // 存储事件处理器以便后续移除\n      this.basicRadarChart.resizeHandler = resizeHandler;\n      this.basicRadarChart = this.$echarts.init(this.$refs.basicRadarChart);\n      window.addEventListener('resize', this.basicRadarChart.resize);\n      const self = this;\n      this.basicRadarChart.setOption({\n        /*legend: {\n          data: ['Allocated Budget', 'Actual Spending']\n        },*/\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            // 使用闭包中的 self 访问组件数据\n            const indicatorNames = self.indicator.map(item => {\n              return `<div>${item.name}: ${item.value}%</div>`;\n            }).join('');\n\n            return `<div>\n          <div style=\"font-weight: bold; margin-bottom: 5px;\">得分详情</div>\n          ${indicatorNames}        </div>`;\n          }\n        },\n        radar: {\n          center: ['50%', '52%'],\n          radius: ['25%', '70%'],\n          // shape: 'circle',\n          indicator: this.indicator,\n          name: {\n            textStyle: {\n              color: '#101010'\n            }\n          },\n          splitArea: {\n            show: false\n          },\n          // 轴线样式等\n          axisLine: {\n            show: false,\n            lineStyle: {\n              color: '#666', // 轴线颜色\n            }\n          }\n        },\n        series: [\n          {\n            name: 'Budget vs spending',\n            type: 'radar',\n            data: [\n              {\n                value: this.value,\n                name: 'Actual Spending',\n                itemStyle: {\n                  color: '#d97559'\n                }\n              }\n            ],\n          }\n        ]\n      })\n    },\n\n    // 详情\n    handleDetail(row) {\n      if (row.riskType === '外部威胁') {\n        this.$router.push({\n          path: '/service-ledger/theratManage',\n          query: {\n            type: '1',\n            referenceId: row.referenceId\n          }\n        });\n      }\n\n      if (row.riskType === '内部漏洞') {\n        let queryParams = {};\n        if (row.deductionType === '主机漏洞') {\n          queryParams = {\n            type: '1',\n            referenceId: row.referenceId\n          }\n        }\n        if (row.deductionType === 'Web漏洞') {\n          queryParams = {\n            type: '2',\n            referenceId: row.referenceId\n          }\n        }\n        if (row.deductionType === '弱口令') {\n          queryParams = {\n            type: '3',\n            referenceId: row.referenceId\n          }\n        }\n        this.$router.push({\n          path: '/service-ledger/frailty',\n          query: queryParams\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.points-overview {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  .points-overview-header {\n    width: 100%;\n    height: 57px;\n    display: flex;\n    flex-direction: column;\n    .proportion {\n      width: 100%;\n      height: 30px;\n      flex: 1;\n      margin-top: 27px;\n      display: flex;\n      align-items: center;\n      .score-info {\n        width: 50%;\n        height: 30px;\n        line-height: 30px;\n        background: #E8F4FE;\n        display: flex;\n        font-size: 12px;\n        color: #101010;\n        div {\n          span {\n            color: #bd3124;\n          }\n          &:first-child {\n            text-indent: 15px;\n          }\n        }\n      }\n\n      .grade-proportion {\n        display: flex;\n        height: 30px;\n        line-height: 30px;\n        flex: 1;\n        .grade-item {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #fff;\n          font-size: 12px;\n          flex: 1;\n          position: relative;\n\n          .grade-arrow {\n            position: absolute;\n            top: -27px; /* 定位到等级条上方 */\n            left: 50%;\n            transform: translateX(-50%);\n            width: 24px;\n            height: 24px;\n          }\n\n          &:first-child {\n            width: 200px !important; // 第一个元素固定宽度为 200px\n            flex: none; // 不参与弹性布局的自动分配\n          }\n\n          &:not(:first-child) {\n            flex: 1; // 其余元素均分剩余空间\n          }\n        }\n      }\n    }\n  }\n  .points-overview-container {\n    height: 600px;\n    display: flex;\n    flex-direction: row;\n    gap: 8px;\n    .score-details {\n      width: 50%;\n      border-radius: 4px;\n      border: 1px solid rgba(206,206,206,1);\n      .score-details-container {\n        width: 100%;\n        height: calc(100% - 30px);\n        padding: 12px;\n        display: flex;\n        flex-direction: column;\n        justify-content: space-between;\n        .chart-container {\n          width: 100%;\n          height: 48%;\n          background-color: rgba(242,244,248,0.8);\n        }\n      }\n    }\n    .points-module {\n      width: 50%;\n      border-radius: 4px;\n      border: 1px solid rgba(206,206,206,1);\n      .points-module-container {\n        height: 100%;\n        padding: 0 12px 12px;\n        .tb-div-table {\n          width: 100%;\n          height: calc(100% - 30px);\n          overflow-y: auto;\n          .tb-div-table-item {\n            display: flex;\n            height: 25px;\n            margin: 20px 10px;\n            font-size: 14px;\n            color: rgba(154,154,154,1);\n            justify-content: space-between;\n            align-items: center;\n            img {\n              width: 15px;\n              height: 15px;\n            }\n            .tb-div-table-item-div {\n              margin-left: 20px;\n              white-space: nowrap;         // 防止文本换行\n              overflow: hidden;            // 隐藏超出部分\n              text-overflow: ellipsis;     // 超出部分显示省略号\n              span {\n                cursor: pointer;\n                color: #347CAF;\n                text-decoration: underline;\n              }\n            }\n            .tb-div-table-item-div-num {\n              span {\n                color: #BD3124;\n              }\n            }\n          }\n          .tb-div-table-item:first-child {\n            margin: 10px 10px 20px;\n          }\n          .tb-div-table-item:last-child {\n            margin: 20px 10px 0;\n          }\n          .load-more {\n            width: 100%;\n            text-align: center;\n          }\n        }\n      }\n    }\n  }\n\n  .chart-title-div {\n    width: 130px;\n    height: 30px;\n    line-height: 30px;\n    border-radius: 4px;\n    color: rgba(48,114,198,1);\n    font-size: 12px;\n    text-align: right;\n    background-color: rgba(232,244,254,0.2);\n    border: 1px solid rgba(187,187,187,0.5);\n    display: flex;\n    align-items: center;\n    img {\n      width: 18px;\n      height: 18px;\n      margin: 0 10px;\n    }\n  }\n}\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAuEA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,aAAA;MACA;MACAC,UAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAC,gBAAA;MACAC,UAAA;MACA;MACAC,YAAA;MACA;MACAC,cAAA;MACAC,WAAA;MACAC,SAAA;MACAC,KAAA;MACAC,eAAA;MACAC,eAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,gBAAA;QACAxB,MAAA;MACA;MACAyB,WAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAxB,OAAA,mBAAAyB,oBAAA,CAAAzB,OAAA,IAAA0B,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAzB,OAAA,IAAA4B,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAU,OAAA;UAAA;YAAAH,QAAA,CAAAE,IAAA;YAAA,OACAT,KAAA,CAAAW,eAAA;UAAA;YACAX,KAAA,CAAAY,kBAAA;YACAZ,KAAA,CAAAa,kBAAA;UAAA;UAAA;YAAA,OAAAN,QAAA,CAAAO,IAAA;QAAA;MAAA,GAAAV,OAAA;IAAA;EACA;EACAW,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,aAAA;EACA;EACAC,KAAA;IACA9C,MAAA;MACA+C,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAA3B,WAAA,CAAArB,MAAA,GAAAgD,GAAA;QACA,KAAAxB,gBAAA,CAAAxB,MAAA,GAAAgD,GAAA;QACA,KAAAV,OAAA;QACA,KAAAC,eAAA;MACA;MACAU,SAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA;MACA,aAAAtC,YAAA;QACA;UACAsC,KAAA;UACA;QACA;UACAA,KAAA;UACA;QACA;UACAA,KAAA;UACA;QACA;UACAA,KAAA;UACA;QACA;UACAA,KAAA;UACA;QACA;UACAA,KAAA;MACA;MACA,OAAAA,KAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAzC,gBAAA,CAAA0C,MAAA,UAAAzC,UAAA;IACA;EACA;EACA0C,OAAA;IACAV,aAAA,WAAAA,cAAA;MACA,SAAA1B,eAAA;QACAqC,MAAA,CAAAC,mBAAA,gBAAAtC,eAAA,CAAAuC,aAAA;QACA,KAAAvC,eAAA,CAAAwC,OAAA;QACA,KAAAxC,eAAA;MACA;MACA,SAAAC,eAAA;QACAoC,MAAA,CAAAC,mBAAA,gBAAArC,eAAA,CAAAsC,aAAA;QACA,KAAAtC,eAAA,CAAAuC,OAAA;QACA,KAAAvC,eAAA;MACA;IACA;IAEA;IACAwC,YAAA,WAAAA,aAAA;MACA,IAAAC,aAAA,QAAAC,KAAA,CAAAC,eAAA;MACA;MACA,IAAAC,QAAA,GAAAH,aAAA,CAAAI,YAAA,GAAAJ,aAAA,CAAAK,SAAA,IAAAL,aAAA,CAAAM,YAAA;MAEA,IAAAH,QAAA,UAAAvC,WAAA,SAAAC,WAAA;QACA,KAAA0C,YAAA;MACA;IACA;IAEA;IACAA,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,WAAAxC,kBAAA,CAAAxB,OAAA,mBAAAyB,oBAAA,CAAAzB,OAAA,IAAA0B,IAAA,UAAAuC,SAAA;QAAA,IAAAC,QAAA;QAAA,WAAAzC,oBAAA,CAAAzB,OAAA,IAAA4B,IAAA,UAAAuC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAApC,IAAA;YAAA;cACAgC,MAAA,CAAA5C,WAAA;cAAAgD,SAAA,CAAArC,IAAA;cAGA;cACAiC,MAAA,CAAAhD,WAAA,CAAAC,OAAA;cAAAmD,SAAA,CAAApC,IAAA;cAAA,OAEA,IAAAqC,0CAAA,EAAAL,MAAA,CAAAhD,WAAA;YAAA;cAAAkD,QAAA,GAAAE,SAAA,CAAAE,IAAA;cAEA,IAAAJ,QAAA,CAAAK,IAAA,IAAAL,QAAA,CAAAK,IAAA,CAAAtB,MAAA;gBACA;gBACAe,MAAA,CAAAzD,gBAAA,MAAAiE,MAAA,KAAAC,mBAAA,CAAAzE,OAAA,EAAAgE,MAAA,CAAAzD,gBAAA,OAAAkE,mBAAA,CAAAzE,OAAA,EAAAkE,QAAA,CAAAK,IAAA;;gBAEA;gBACAP,MAAA,CAAA3C,WAAA,GAAA6C,QAAA,CAAAK,IAAA,CAAAtB,MAAA,IAAAe,MAAA,CAAAhD,WAAA,CAAAE,QAAA;cACA;gBACA8C,MAAA,CAAA3C,WAAA;cACA;cAAA+C,SAAA,CAAApC,IAAA;cAAA;YAAA;cAAAoC,SAAA,CAAArC,IAAA;cAAAqC,SAAA,CAAAM,EAAA,GAAAN,SAAA;cAEAO,OAAA,CAAAC,KAAA,cAAAR,SAAA,CAAAM,EAAA;cACA;cACAV,MAAA,CAAAhD,WAAA,CAAAC,OAAA;YAAA;cAAAmD,SAAA,CAAArC,IAAA;cAEAiC,MAAA,CAAA5C,WAAA;cAAA,OAAAgD,SAAA,CAAAS,MAAA;YAAA;YAAA;cAAA,OAAAT,SAAA,CAAA/B,IAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IAEA;IAEAhC,OAAA,WAAAA,QAAA;MAAA,IAAA6C,MAAA;MAAA,WAAAtD,kBAAA,CAAAxB,OAAA,mBAAAyB,oBAAA,CAAAzB,OAAA,IAAA0B,IAAA,UAAAqD,SAAA;QAAA,WAAAtD,oBAAA,CAAAzB,OAAA,IAAA4B,IAAA,UAAAoD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlD,IAAA,GAAAkD,SAAA,CAAAjD,IAAA;YAAA;cACA8C,MAAA,CAAA9D,WAAA,CAAAC,OAAA;cACA6D,MAAA,CAAA5E,YAAA;cACA4E,MAAA,CAAA3E,aAAA;cAAA8E,SAAA,CAAAjD,IAAA;cAAA,OACA,IAAAqC,0CAAA,EAAAS,MAAA,CAAA9D,WAAA,EAAAkE,IAAA,WAAAhB,QAAA;gBACAY,MAAA,CAAA5E,YAAA;gBACA4E,MAAA,CAAAvE,gBAAA,GAAA2D,QAAA,CAAAK,IAAA;cACA;YAAA;YAAA;cAAA,OAAAU,SAAA,CAAA5C,IAAA;UAAA;QAAA,GAAA0C,QAAA;MAAA;IACA;IAEA;IACA7C,eAAA,WAAAA,gBAAA;MAAA,IAAAiD,MAAA;MAAA,WAAA3D,kBAAA,CAAAxB,OAAA,mBAAAyB,oBAAA,CAAAzB,OAAA,IAAA0B,IAAA,UAAA0D,SAAA;QAAA,IAAAlB,QAAA,EAAAmB,WAAA,EAAAC,YAAA;QAAA,WAAA7D,oBAAA,CAAAzB,OAAA,IAAA4B,IAAA,UAAA2D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzD,IAAA,GAAAyD,SAAA,CAAAxD,IAAA;YAAA;cAAAwD,SAAA,CAAAzD,IAAA;cAAAyD,SAAA,CAAAxD,IAAA;cAAA,OAEA,IAAAE,mCAAA,EAAAiD,MAAA,CAAAhE,gBAAA;YAAA;cAAA+C,QAAA,GAAAsB,SAAA,CAAAlB,IAAA;cACA,IAAAJ,QAAA,CAAAjE,IAAA;gBACAkF,MAAA,CAAAxE,WAAA,GAAAuD,QAAA,CAAAjE,IAAA,CAAAU,WAAA;gBACAwE,MAAA,CAAAhF,aAAA;gBACA;gBACAkF,WAAA,IACA;kBAAAI,GAAA;kBAAAC,KAAA;kBAAAC,OAAA;gBAAA,GACA;kBAAAF,GAAA;kBAAAC,KAAA;kBAAAC,OAAA;gBAAA,GACA;kBAAAF,GAAA;kBAAAC,KAAA;kBAAAC,OAAA;gBAAA,GACA;kBAAAF,GAAA;kBAAAC,KAAA;kBAAAC,OAAA;gBAAA,GACA;kBAAAF,GAAA;kBAAAC,KAAA;kBAAAC,OAAA;gBAAA,GACA;kBAAAF,GAAA,GAAAG,QAAA;kBAAAF,KAAA;kBAAAC,OAAA;gBAAA;gBAAA,CACA;gBAEAL,YAAA,GAAAD,WAAA,CAAAQ,IAAA,WAAAC,KAAA;kBAAA,OAAAX,MAAA,CAAAxE,WAAA,IAAAmF,KAAA,CAAAL,GAAA;gBAAA;gBACA,IAAAH,YAAA;kBACAH,MAAA,CAAA1E,YAAA,GAAA6E,YAAA,CAAAI,KAAA;kBACAP,MAAA,CAAAzE,cAAA,GAAA4E,YAAA,CAAAK,OAAA;gBACA;gBAEAR,MAAA,CAAAvE,SAAA,GAAAsD,QAAA,CAAAjE,IAAA,CAAAW,SAAA;gBACAuE,MAAA,CAAAtE,KAAA,GAAAsE,MAAA,CAAAvE,SAAA,CAAAmF,GAAA,WAAAC,IAAA;kBAAA,IAAAC,WAAA;kBAAA,QAAAA,WAAA,GAAAD,IAAA,aAAAA,IAAA,uBAAAA,IAAA,CAAAnF,KAAA,cAAAoF,WAAA,cAAAA,WAAA;gBAAA;cACA;cAAAT,SAAA,CAAAxD,IAAA;cAAA;YAAA;cAAAwD,SAAA,CAAAzD,IAAA;cAAAyD,SAAA,CAAAd,EAAA,GAAAc,SAAA;cAEAb,OAAA,CAAAC,KAAA,cAAAY,SAAA,CAAAd,EAAA;YAAA;YAAA;cAAA,OAAAc,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAA+C,QAAA;MAAA;IAEA;IAEA;IACAjD,kBAAA,WAAAA,mBAAA;MAAA,IAAA+D,MAAA;MACA,SAAApF,eAAA;MACA,KAAAA,eAAA,QAAAqF,QAAA,CAAAC,IAAA,MAAA3C,KAAA,CAAA3C,eAAA;MACA,IAAAuC,aAAA,YAAAA,cAAA;QAAA,OAAA6C,MAAA,CAAApF,eAAA,IAAAoF,MAAA,CAAApF,eAAA,CAAAuF,MAAA;MAAA;MACAlD,MAAA,CAAAmD,gBAAA,WAAAjD,aAAA;MAEA,KAAAvC,eAAA,CAAAuC,aAAA,GAAAA,aAAA;MACA,KAAAvC,eAAA,CAAAyF,SAAA;QACAC,MAAA,GACA;UACA5G,IAAA;UACA6G,MAAA;UACAC,MAAA;UACAC,QAAA;YACAC,IAAA;YACAC,KAAA;YACAC,SAAA;cACAxG,KAAA;YACA;UACA;UACA;UACAyG,QAAA;YACAC,SAAA;cACAH,KAAA;YACA;UACA;UACAI,QAAA;YACAL,IAAA;YAAA;YACAM,WAAA;YAAA;YACAjE,MAAA;YAAA;YACA+D,SAAA;cACA1G,KAAA;cAAA;cACAuG,KAAA;YACA;UACA;UACA;UACAM,SAAA;YACAlE,MAAA;YACA+D,SAAA;cACAH,KAAA;cACAvG,KAAA;YACA;UACA;UACA;UACA8G,SAAA;YACAC,QAAA;YACA/G,KAAA;YACAgH,QAAA;UACA;UACAC,OAAA;YACAX,IAAA;YACA3D,MAAA;YACA4D,KAAA;YACAW,YAAA;YACAV,SAAA;cACAxG,KAAA;YACA;UACA;UACA;UACAmH,MAAA;YACAb,IAAA;YACAc,SAAA;YACAC,IAAA;YACAb,SAAA;cACAc,WAAA;cACA;cACAtH,KAAA;cAAA;cACAuH,WAAA;YACA;UACA;UACAC,KAAA;YACAlB,IAAA;UACA;UACAmB,MAAA;YACAC,cAAA;YACAV,QAAA;YACAE,YAAA;YACAS,SAAA,WAAAA,UAAApH,KAAA;cACA,OAAAA,KAAA,CAAAqH,OAAA;YACA;UAEA;UACAjI,IAAA,GACA;YACAY,KAAA,OAAAF;UACA;QAEA;MAEA;IACA;IACA;IACAyB,kBAAA,WAAAA,mBAAA;MAAA,IAAA+F,MAAA;MACA,SAAApH,eAAA;MACA,KAAAA,eAAA,QAAAoF,QAAA,CAAAC,IAAA,MAAA3C,KAAA,CAAA1C,eAAA;MACA,IAAAsC,aAAA,YAAAA,cAAA;QAAA,OAAA8E,MAAA,CAAApH,eAAA,IAAAoH,MAAA,CAAApH,eAAA,CAAAsF,MAAA;MAAA;MACAlD,MAAA,CAAAmD,gBAAA,WAAAjD,aAAA;;MAEA;MACA,KAAAtC,eAAA,CAAAsC,aAAA,GAAAA,aAAA;MACA,KAAAtC,eAAA,QAAAoF,QAAA,CAAAC,IAAA,MAAA3C,KAAA,CAAA1C,eAAA;MACAoC,MAAA,CAAAmD,gBAAA,gBAAAvF,eAAA,CAAAsF,MAAA;MACA,IAAA+B,IAAA;MACA,KAAArH,eAAA,CAAAwF,SAAA;QACA;AACA;AACA;QACA8B,OAAA;UACAC,OAAA;UACAL,SAAA,WAAAA,UAAAM,MAAA;YACA;YACA,IAAAC,cAAA,GAAAJ,IAAA,CAAAxH,SAAA,CAAAmF,GAAA,WAAAC,IAAA;cACA,eAAAxB,MAAA,CAAAwB,IAAA,CAAAvG,IAAA,QAAA+E,MAAA,CAAAwB,IAAA,CAAAnF,KAAA;YACA,GAAA4H,IAAA;YAEA,2HAAAjE,MAAA,CAEAgE,cAAA;UACA;QACA;QACAE,KAAA;UACAhC,MAAA;UACAD,MAAA;UACA;UACA7F,SAAA,OAAAA,SAAA;UACAnB,IAAA;YACAkJ,SAAA;cACArI,KAAA;YACA;UACA;UACAsI,SAAA;YACAhC,IAAA;UACA;UACA;UACAG,QAAA;YACAH,IAAA;YACAI,SAAA;cACA1G,KAAA;YACA;UACA;QACA;QACAkG,MAAA,GACA;UACA/G,IAAA;UACAG,IAAA;UACAK,IAAA,GACA;YACAY,KAAA,OAAAA,KAAA;YACApB,IAAA;YACAqH,SAAA;cACAxG,KAAA;YACA;UACA;QAEA;MAEA;IACA;IAEA;IACAuI,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,QAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA;YACAvJ,IAAA;YACAwJ,WAAA,EAAAN,GAAA,CAAAM;UACA;QACA;MACA;MAEA,IAAAN,GAAA,CAAAC,QAAA;QACA,IAAA/H,WAAA;QACA,IAAA8H,GAAA,CAAAO,aAAA;UACArI,WAAA;YACApB,IAAA;YACAwJ,WAAA,EAAAN,GAAA,CAAAM;UACA;QACA;QACA,IAAAN,GAAA,CAAAO,aAAA;UACArI,WAAA;YACApB,IAAA;YACAwJ,WAAA,EAAAN,GAAA,CAAAM;UACA;QACA;QACA,IAAAN,GAAA,CAAAO,aAAA;UACArI,WAAA;YACApB,IAAA;YACAwJ,WAAA,EAAAN,GAAA,CAAAM;UACA;QACA;QACA,KAAAJ,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAC,KAAA,EAAAnI;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}