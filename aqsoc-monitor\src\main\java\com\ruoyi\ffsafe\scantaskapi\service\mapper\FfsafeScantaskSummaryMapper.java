package com.ruoyi.ffsafe.scantaskapi.service.mapper;

import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummary;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryDetailVO;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScantaskSummaryQueryParam;

import java.util.List;

public interface FfsafeScantaskSummaryMapper {
    /**
     * 查询非凡扫描任务汇总
     *
     * @param id 非凡扫描任务汇总主键
     * @return 非凡扫描任务汇总
     */
    public FfsafeScantaskSummary selectFfsafeScantaskSummaryById(Long id);

    /**
     * 批量查询非凡扫描任务汇总
     *
     * @param ids 非凡扫描任务汇总主键集合
     * @return 非凡扫描任务汇总集合
     */
    public List<FfsafeScantaskSummary> selectFfsafeScantaskSummaryByIds(Long[] ids);

    /**
     * 批量查询非凡扫描任务汇总（仅查询任务汇总表，不关联报告记录）
     *
     * @param ids 非凡扫描任务汇总主键集合
     * @return 非凡扫描任务汇总集合
     */
    public List<FfsafeScantaskSummary> selectFfsafeScantaskSummaryByIdsOnly(Long[] ids);

    /**
     * 查询非凡扫描任务汇总列表
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 非凡扫描任务汇总集合
     */
    public List<FfsafeScantaskSummary> selectFfsafeScantaskSummaryList(FfsafeScantaskSummary ffsafeScantaskSummary);

    /**
     * 查询主机漏扫记录详细信息列表
     *
     * @param queryParam 查询参数
     * @return 主机漏扫记录详细信息集合
     */
    public List<FfsafeScantaskSummaryDetailVO> selectFfsafeScantaskSummaryDetailList(FfsafeScantaskSummaryQueryParam queryParam);

    /**
     * 新增非凡扫描任务汇总
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 结果
     */
    public int insertFfsafeScantaskSummary(FfsafeScantaskSummary ffsafeScantaskSummary);

    /**
     * 修改非凡扫描任务汇总
     *
     * @param ffsafeScantaskSummary 非凡扫描任务汇总
     * @return 结果
     */
    public int updateFfsafeScantaskSummary(FfsafeScantaskSummary ffsafeScantaskSummary);

    public int updateFfsafeScantaskSummaryByTaskId(FfsafeScantaskSummary ffsafeScantaskSummary);

    /**
     * 删除非凡扫描任务汇总
     *
     * @param id 非凡扫描任务汇总主键
     * @return 结果
     */
    public int deleteFfsafeScantaskSummaryById(Long id);

    /**
     * 批量删除非凡扫描任务汇总
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFfsafeScantaskSummaryByIds(Long[] ids);
}
