{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorIp.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorIp.vue", "mtime": 1755768894570}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7bGlzdEpvYiwgZ2V0Sm9iLCBkZWxKb2IsIGFkZEpvYiwgdXBkYXRlSm9iLCBydW5Kb2IsIGNoYW5nZUpvYlN0YXR1c30gZnJvbSAiQC9hcGkvc2FmZS9tb25pdG9yIjsKaW1wb3J0IFF1ZXN0UmVzdWx0RGV0YWlscyBmcm9tICcuLi8uLi9zYWZlL3NlcnZlci9xdWVzdFJlc3VsdERldGFpbHMnCmltcG9ydCBMZWFrU2NhbkRpYWxvZyBmcm9tICcuLi8uLi9zYWZlL3NlcnZlci9jb21wb25lbnRzL0xlYWtTY2FuRGlhbG9nJwppbXBvcnQgRmZKb2JUYXNrcyBmcm9tICcuL2ZmSm9iVGFza3MnCmltcG9ydCBKb2JMb2cgZnJvbSAnLi4vLi4vbW9uaXRvci9qb2IvbG9nJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJKb2IiLAogIGNvbXBvbmVudHM6IHsgSm9iTG9nLCBGZkpvYlRhc2tzLCBMZWFrU2NhbkRpYWxvZywgUXVlc3RSZXN1bHREZXRhaWxzIH0sCiAgZGljdHM6IFsnc3lzX2pvYl9ncm91cCcsICdzeXNfam9iX3N0YXR1cyddLAogIHByb3BzOiB7CiAgICB0b1BhcmFtczogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6ICgpID0+IHt9CiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgam9iVHlwZTogdW5kZWZpbmVkLAogICAgICAvLyDmmK/lkKbmmL7npLpDcm9u6KGo6L6+5byP5by55Ye65bGCCiAgICAgIG9wZW5Dcm9uOiBmYWxzZSwKICAgICAgLy8g5bGV56S65pyA6L+R5LiA5qyh6L+Q6KGM57uT5p6cCiAgICAgIG9wZW5TZWxlY3Q6IGZhbHNlLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5Lu75YqhSUQKICAgICAgam9iSWQ6ICcnLAogICAgICBsb2dKb2JJZDogbnVsbCwKICAgICAgdG90YWxTY2FuOiAwLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlrprml7bku7vliqHooajmoLzmlbDmja4KICAgICAgam9iTGlzdDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIGVkaXRUaXRsZTogJycsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5piv5ZCm5pi+56S66K+m57uG5by55Ye65bGCCiAgICAgIG9wZW5WaWV3OiBmYWxzZSwKICAgICAgb3BlbkxvZ1ZpZXc6IGZhbHNlLAogICAgICBzY2FuU3RyYXRlZ3lWaXNpYmxlOiAgZmFsc2UsCiAgICAgIGVkaXRGb3JtOiB7fSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgam9iVHlwZTogMSwKICAgICAgICBqb2JOYW1lOiB1bmRlZmluZWQsCiAgICAgICAgam9iR3JvdXA6ICdBU1NFVF9TQ0FOJywKICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZAogICAgICB9LAogICAgICBpbnZva2VEaXNhYmxlOiBmYWxzZSwKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8vIOWRqOacn+i9rOaNouaWh+WtlwogICAgICBjcm9uVGV4dDogJycsCiAgICAgIHJvd3M6IFtdLAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgam9iTmFtZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Lu75Yqh5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifQogICAgICAgIF0sCiAgICAgICAgaW52b2tlSXA6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJq+aPj0lQ5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifQogICAgICAgIF0sCiAgICAgICAgY3JvbkV4cHJlc3Npb246IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogImNyb27miafooYzooajovr7lvI/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9CiAgICAgICAgXQogICAgICB9LAogICAgICBnZXRMaXN0SW50ZXJ2YWw6IG51bGwsCiAgICAgIHNlbGVjdGVkSWRzOiBbXQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICB0b1BhcmFtczogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmKG5ld1ZhbCAmJiBuZXdWYWwuaWQpewogICAgICAgICAgdGhpcy5oYW5kbGVKb2JMb2coewogICAgICAgICAgICBqb2JJZDogbmV3VmFsLmlkCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB0aGlzLmdldExpc3RJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgdGhpcy5sb29wR2V0TGlzdCgpCiAgICB9LCAxMDAwMCkKICB9LAogIGRlc3Ryb3llZCgpIHsKICAgIGlmKHRoaXMuZ2V0TGlzdEludGVydmFsKXsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmdldExpc3RJbnRlcnZhbCk7CiAgICB9CiAgfSwKICAvLyB3YXRjaDogewogIC8vICAgJ2RpY3QudHlwZS5zeXNfam9iX2dyb3VwJzogewogIC8vICAgICBoYW5kbGVyKG5ld1ZhbCkgewogIC8vICAgICAgIGlmIChuZXdWYWwubGVuZ3RoID4gMCkgewogIC8vICAgICAgICAgbGV0IHRtcCA9IG5ld1ZhbC5maWx0ZXIocyA9PiBzLmxhYmVsID09PSAn6LWE5Lqn5omr5o+PJykKICAvLyAgICAgICAgIHRoaXMuZm9ybS5qb2JHcm91cCA9IHRtcC5sZW5ndGggPiAwID8gdG1wWzBdLnZhbHVlIDogdW5kZWZpbmVkCiAgLy8gICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmpvYkdyb3VwID0gdGhpcy5mb3JtLmpvYkdyb3VwCiAgLy8gICAgICAgfQogIC8vICAgICB9LAogIC8vICAgICBkZWVwOiB0cnVlCiAgLy8gICB9CiAgLy8gfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5a6a5pe25Lu75Yqh5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0Sm9iKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHJlc3BvbnNlLnJvd3MuZm9yRWFjaChzID0+IHsKICAgICAgICAgIGlmIChzLmludm9rZVRhcmdldCkgewogICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBzLmludm9rZVRhcmdldAogICAgICAgICAgICBjb25zdCBzdGFydCA9IHRhcmdldC5pbmRleE9mKCdcJyxcJycpICsgMwogICAgICAgICAgICBjb25zdCBlbmQgPSB0YXJnZXQubGVuZ3RoIC0gMgogICAgICAgICAgICBjb25zdCBpcHMgPSB0YXJnZXQuc3Vic3RyaW5nKHN0YXJ0LCBlbmQpCiAgICAgICAgICAgIGNvbnN0IGlwc3MgPSBpcHMuc3BsaXQoJ3wnKQogICAgICAgICAgICBpZiAoaXBzcy5sZW5ndGggPiAxKSB7CiAgICAgICAgICAgICAgcy5pcFNob3cgPSBpcHNzWzFdLnJlcGxhY2VBbGwoJzsnLCAnICAnKQogICAgICAgICAgICAgIHMuaXBPdmVyID0gaXBzc1sxXS5yZXBsYWNlQWxsKCc7JywgJzxicj4nKQogICAgICAgICAgICB9CiAgICAgICAgICAgIGlmKHMucGVyaW9kID09PSAwKXsKICAgICAgICAgICAgICBzLnN0YXR1cyA9IHMuY3VycmVudFN0YXR1cyA9PT0gMSA/ICcwJyA6ICcxJwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICB0aGlzLmpvYkxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgbG9vcEdldExpc3QoKSB7CiAgICAgIGxpc3RKb2IodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgcmVzcG9uc2Uucm93cy5mb3JFYWNoKHMgPT4gewogICAgICAgICAgaWYgKHMuaW52b2tlVGFyZ2V0KSB7CiAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IHMuaW52b2tlVGFyZ2V0CiAgICAgICAgICAgIGNvbnN0IHN0YXJ0ID0gdGFyZ2V0LmluZGV4T2YoJ1wnLFwnJykgKyAzCiAgICAgICAgICAgIGNvbnN0IGVuZCA9IHRhcmdldC5sZW5ndGggLSAyCiAgICAgICAgICAgIGNvbnN0IGlwcyA9IHRhcmdldC5zdWJzdHJpbmcoc3RhcnQsIGVuZCkKICAgICAgICAgICAgY29uc3QgaXBzcyA9IGlwcy5zcGxpdCgnfCcpCiAgICAgICAgICAgIGlmIChpcHNzLmxlbmd0aCA+IDEpIHsKICAgICAgICAgICAgICBzLmlwU2hvdyA9IGlwc3NbMV0ucmVwbGFjZUFsbCgnOycsICcgJykKICAgICAgICAgICAgICBzLmlwT3ZlciA9IGlwc3NbMV0ucmVwbGFjZUFsbCgnOycsICc8YnI+JykKICAgICAgICAgICAgfQogICAgICAgICAgICBpZihzLnBlcmlvZCA9PT0gMCl7CiAgICAgICAgICAgICAgcy5zdGF0dXMgPSBzLmN1cnJlbnRTdGF0dXMgPT09IDEgPyAnMCcgOiAnMScKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgY29uc3QgbmV3Sm9iTGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICBjb25zdCBzZWxlY3RlZElkcyA9IFsuLi50aGlzLnNlbGVjdGVkSWRzXQogICAgICAgIHRoaXMuam9iTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIGNvbnN0IHJvd3NUb1NlbGVjdCA9IG5ld0pvYkxpc3QuZmlsdGVyKHJvdyA9PgogICAgICAgICAgICBzZWxlY3RlZElkcy5pbmNsdWRlcyhyb3cuam9iSWQpCiAgICAgICAgICApOwogICAgICAgICAgdGhpcy4kcmVmcy5tdWx0aXBsZVRhYmxlLmNsZWFyU2VsZWN0aW9uKCk7CiAgICAgICAgICByb3dzVG9TZWxlY3QuZm9yRWFjaChyb3cgPT4gewogICAgICAgICAgICB0aGlzLiRyZWZzLm11bHRpcGxlVGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdywgdHJ1ZSk7CiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVNjYW4oKSB7CiAgICAgIHRoaXMuaW52b2tlRGlzYWJsZSA9IGZhbHNlCiAgICAgIHRoaXMudGl0bGUgPSAn5re75Yqg5Lu75YqhJzsKICAgICAgdGhpcy5lZGl0VGl0bGUgPSAnSVDmvI/mtJ7miavmj48nCiAgICAgIHRoaXMuZWRpdEZvcm0gPSB7fQogICAgICB0aGlzLmVkaXRGb3JtLmpvYlR5cGUgPSAxOwogICAgICB0aGlzLmVkaXRGb3JtLndlYWtQdyA9ICcxJzsKICAgICAgdGhpcy5lZGl0Rm9ybS5zdGF0dXMgPSAnMCc7CiAgICAgIHRoaXMuZWRpdEZvcm0uY3JvbkV4cHJlc3Npb249ICcqICogKiAqICogPyc7CiAgICAgIHRoaXMuZWRpdEZvcm0ucGVyaW9kPSAwOwogICAgICB0aGlzLmVkaXRGb3JtLmNyb25UcmFuc2Zlcj0gJ+eri+WNs+aJp+ihjCc7CiAgICAgIHRoaXMuc2NhblN0cmF0ZWd5VmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLy8g5Lu75Yqh57uE5ZCN5a2X5YW457+76K+RCiAgICBqb2JHcm91cEZvcm1hdChyb3csIGNvbHVtbikgewogICAgICByZXR1cm4gdGhpcy5zZWxlY3REaWN0TGFiZWwodGhpcy5kaWN0LnR5cGUuc3lzX2pvYl9ncm91cCwgcm93LmpvYkdyb3VwKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMuaW52b2tlRGlzYWJsZSA9IGZhbHNlCiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvKiog56Gu5a6a5ZCO5Zue5Lyg5YC8ICovCiAgICBjcm9uVGFiRmlsbCh2YWwpIHsKICAgICAgdGhpcy5mb3JtLmNyb25FeHByZXNzaW9uID0gdmFsLmNyb25UZXh0CiAgICAgIHRoaXMuZm9ybS5wZXJpb2QgPSB2YWwucGVyaW9kCiAgICAgIHRoaXMuZm9ybS5jcm9uVHJhbnNmZXIgPSB2YWwuY3JvblRyYW5zZmVyCiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGpvYklkOiB1bmRlZmluZWQsCiAgICAgICAgam9iTmFtZTogdW5kZWZpbmVkLAogICAgICAgIGludm9rZVRhcmdldDogdW5kZWZpbmVkLAogICAgICAgIGNyb25FeHByZXNzaW9uOiB1bmRlZmluZWQsCiAgICAgICAgbWlzZmlyZVBvbGljeTogMCwKICAgICAgICBjb25jdXJyZW50OiAxLAogICAgICAgIHBlcmlvZDogMCwKICAgICAgICBqb2JUeXBlOiAxLAogICAgICAgIHN0YXR1czogIjAiCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKCiAgICBnZXRSb3dLZXkocm93KSB7CiAgICAgIHJldHVybiByb3cuam9iSWQ7CiAgICB9LAoKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmpvYklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgICAgdGhpcy5yb3dzID0gc2VsZWN0aW9uOwogICAgICB0aGlzLnNlbGVjdGVkSWRzID0gWy4uLnRoaXMuaWRzXQogICAgfSwKICAgIC8vIOabtOWkmuaTjeS9nOinpuWPkQogICAgaGFuZGxlQ29tbWFuZChjb21tYW5kLCByb3cpIHsKICAgICAgc3dpdGNoIChjb21tYW5kKSB7CiAgICAgICAgY2FzZSAibGFzdFJlc3VsdCI6CiAgICAgICAgICB0aGlzLmhhbmRsZURldGFpbChyb3cpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiaGFuZGxlUnVuIjoKICAgICAgICAgIHRoaXMuaGFuZGxlUnVuKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJoYW5kbGVWaWV3IjoKICAgICAgICAgIHRoaXMuaGFuZGxlVmlldyhyb3cpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiaGFuZGxlSm9iTG9nIjoKICAgICAgICAgIHRoaXMuaGFuZGxlSm9iTG9nKHJvdyk7CiAgICAgICAgICBicmVhazsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCiAgICAvLyDku7vliqHnirbmgIHkv67mlLkKICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAnIiInICsgcm93LmpvYk5hbWUgKyAnIuS7u+WKoeWQl++8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBjaGFuZ2VKb2JTdGF0dXMocm93LmpvYklkLCByb3cuc3RhdHVzKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIjsKICAgICAgfSkuZmluYWxseSgoKSA9PiB7CiAgICAgICAgdmFyIF90aGlzID0gdGhpcwogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgX3RoaXMuZ2V0TGlzdCgpOwogICAgICAgIH0sIDEwMDApOwogICAgICB9KTsKICAgIH0sCiAgICAvKiDmnIDov5HmiafooYznu5PmnpwgKi8KICAgIGhhbmRsZURldGFpbChyb3cpIHsKICAgICAgdGhpcy5qb2JJZCA9IHJvdy5qb2JJZAogICAgfSwKICAgIC8qIOeri+WNs+aJp+ihjOS4gOasoSAqLwogICAgaGFuZGxlUnVuKHJvdykgewogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTopoHnq4vljbPmiafooYzkuIDmrKEiJyArIHJvdy5qb2JOYW1lICsgJyLku7vliqHlkJfvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gcnVuSm9iKHJvdy5qb2JJZCwgcm93LmpvYkdyb3VwKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5omn6KGM5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgfSkuZmluYWxseSgoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDku7vliqHor6bnu4bkv6Hmga8gKi8KICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIHRoaXMub3BlblZpZXcgPSB0cnVlOwogICAgICB0aGlzLmpvYlR5cGUgPSAyOwogICAgICB0aGlzLmpvYklkID0gcm93LmpvYklkOwogICAgICB0aGlzLmVkaXRGb3JtID0gey4uLnJvd30KICAgIH0sCiAgICBlZGl0Tm93KGpvYklkKSB7CiAgICAgIGxldCBmaWx0ZXIgPSB0aGlzLmpvYkxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5qb2JJZCA9PSBqb2JJZCkKICAgICAgaWYgKGZpbHRlci5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmnKrmib7liLDku7vliqHmlbDmja7vvIEnKQogICAgICAgIHJldHVybgogICAgICB9IGVsc2UgewogICAgICAgIGlmIChmaWx0ZXJbMF0uY3VycmVudFN0YXR1cyA9PT0gMSkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5b2T5YmN5Lu75Yqh54q25oCB5Li65q2j5Zyo5omr5o+P5Lit77yM6K+35Yu/5pu05pS577yBJykKICAgICAgICAgIHJldHVybgogICAgICAgIH0KICAgICAgICB0aGlzLm9wZW5WaWV3ID0gZmFsc2UKICAgICAgICB0aGlzLmhhbmRsZVVwZGF0ZShmaWx0ZXJbMF0pCiAgICAgIH0KICAgIH0sCiAgICBleGVjdXRlTm93KGpvYklkKSB7CiAgICAgIGxldCBmaWx0ZXIgPSB0aGlzLmpvYkxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5qb2JJZCA9PSBqb2JJZCkKICAgICAgaWYgKGZpbHRlci5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmnKrmib7liLDku7vliqHmlbDmja7vvIEnKQogICAgICAgIHJldHVybgogICAgICB9IGVsc2UgewogICAgICAgIGlmIChmaWx0ZXJbMF0uc3RhdHVzID09PSAnMScgfHwgZmlsdGVyWzBdLmN1cnJlbnRTdGF0dXMgPT09IDEpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+W9k+WJjeS7u+WKoeeKtuaAgeS4uuaaguWBnOaIluato+WcqOaJq+aPj+S4re+8jOivt+WLv+aJp+ihjO+8gScpCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CiAgICAgICAgdGhpcy5vcGVuVmlldyA9IGZhbHNlCiAgICAgICAgdGhpcy5oYW5kbGVSdW4oZmlsdGVyWzBdKQogICAgICB9CgogICAgfSwKICAgIC8qKiDku7vliqHml6Xlv5fliJfooajmn6Xor6IgKi8KICAgIGhhbmRsZUpvYkxvZyhyb3cpIHsKICAgICAgdGhpcy5sb2dKb2JJZCA9IHJvdy5qb2JJZCB8fCAwOwogICAgICB0aGlzLm9wZW5Mb2dWaWV3ID0gdHJ1ZQogICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCh7cGF0aDogJy9tb25pdG9yL2pvYi1sb2cvaW5kZXgnLCBxdWVyeToge2pvYklkOiBqb2JJZH19KQogICAgfSwKICAgIGhhbmRsZVNlbGVjdCgpIHsKICAgICAgdGhpcy5vcGVuU2VsZWN0ID0gdHJ1ZQogICAgfSwKICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFkZCh2YWwpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBzd2l0Y2ggKHZhbCkgewogICAgICAgIGNhc2UgMDoKICAgICAgICAgIHRoaXMuZWRpdFRpdGxlID0gJ+i1hOS6p+aJq+aPj+ebkeaOpycKICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAxOgogICAgICAgICAgdGhpcy5lZGl0VGl0bGUgPSAn5Z+656GA5pyN5Yqh5ryP5rSe5omr5o+PJwogICAgICAgICAgYnJlYWsKICAgICAgICBjYXNlIDI6CiAgICAgICAgICB0aGlzLmVkaXRUaXRsZSA9ICdXZWLmvI/mtJ7miavmj48nCiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgMzoKICAgICAgICAgIHRoaXMuZWRpdFRpdGxlID0gJ+S4u+acuui1hOS6p+aOoua1iycKICAgICAgICAgIGJyZWFrCiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIGJyZWFrCiAgICAgIH0KICAgICAgdGhpcy5vcGVuU2VsZWN0ID0gZmFsc2UKICAgICAgdGhpcy5mb3JtLmpvYlR5cGUgPSB2YWwKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDku7vliqEiOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgc3dpdGNoIChyb3cuam9iVHlwZSkgewogICAgICAgIGNhc2UgMDoKICAgICAgICAgIHRoaXMuZWRpdFRpdGxlID0gJ+i1hOS6p+aJq+aPj+ebkeaOpycKICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAxOgogICAgICAgICAgdGhpcy5lZGl0VGl0bGUgPSAn5Z+656GA5pyN5Yqh5ryP5rSe5omr5o+PJwogICAgICAgICAgYnJlYWsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgYnJlYWsKICAgICAgfQogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIGNvbnN0IGpvYklkID0gcm93LmpvYklkIHx8IHRoaXMuaWRzOwogICAgICBnZXRKb2Ioam9iSWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnN0IHRhcmdldCA9IHJlc3BvbnNlLmRhdGEuaW52b2tlVGFyZ2V0CiAgICAgICAgY29uc3Qgc3RhcnQgPSB0YXJnZXQuaW5kZXhPZignXCcsXCcnKSArIDMKICAgICAgICBjb25zdCBlbmQgPSB0YXJnZXQubGVuZ3RoIC0gMgogICAgICAgIGNvbnN0IGlwcyA9IHRhcmdldC5zdWJzdHJpbmcoc3RhcnQsIGVuZCkKICAgICAgICBjb25zdCBpcHNzID0gaXBzLnNwbGl0KCd8JykKICAgICAgICBpZiAoaXBzcy5sZW5ndGggPiAxKSB7CiAgICAgICAgICByZXNwb25zZS5kYXRhLmludm9rZUlwID0gaXBzc1sxXS5yZXBsYWNlQWxsKCc7JywgJ1xuJykKICAgICAgICAgIHJlc3BvbnNlLmRhdGEud2Vha1B3ID0gaXBzc1s0XQogICAgICAgIH0KICAgICAgICB0aGlzLmVkaXRGb3JtPSByZXNwb25zZS5kYXRhOwogICAgICAgIHRoaXMuc2NhblN0cmF0ZWd5VmlzaWJsZSA9IHRydWU7CiAgICAgICAgdGhpcy5pbnZva2VEaXNhYmxlID0gdHJ1ZQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Lu75YqhIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgLy8gaXDmraPliJkKICAgICAgICAgIGNvbnN0IGlwUmVnID0gL14oMVxkezJ9fDJbMC00XVxkfDI1WzAtNV18WzEtOV1cZHxbMS05XSlcLigoMVxkezJ9fDJbMC00XVxkfDI1WzAtNV18WzEtOV1cZHxcZClcLil7Mn0oMVxkezJ9fDJbMC00XVxkfDI1WzAtNV18WzEtOV1cZHxcZCkkLwogICAgICAgICAgLy8gaXDmrrXmraPliJkKICAgICAgICAgIGNvbnN0IGlwU2VnbWVudFJlZyA9IC9eKFxkezEsMn18MVxkXGR8MlswLTRdXGR8MjVbMC01XSlcLihcZHsxLDJ9fDFcZFxkfDJbMC00XVxkfDI1WzAtNV0pXC4oXGR7MSwyfXwxXGRcZHwyWzAtNF1cZHwyNVswLTVdKVwuKFswLTldfFxkezEsMn18MVxkezEsMn18MlswLTRdXGR8MjVbMC01XSktKFsxLTldfFxkezEsMn18MVxkezEsMn18MlswLTRdXGR8MjVbMC01XSkkLwogICAgICAgICAgLy8g5bim5a2Q572R5o6p56CB5q2j5YiZCiAgICAgICAgICAvLyBjb25zdCBpcE1hc2tSZWcgPSAvXihcZHsxLDJ9fDFcZFxkfDJbMC00XVxkfDI1WzAtNV0pXC4oXGR7MSwyfXwxXGRcZHwyWzAtNF1cZHwyNVswLTVdKVwuKFxkezEsMn18MVxkXGR8MlswLTRdXGR8MjVbMC01XSlcLihcZHsxLDJ9fDFcZFxkfDJbMC00XVxkfDI1WzAtNV0pXC8oXGR7MSwyfSkkLwoKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaW52b2tlSXApIHsKICAgICAgICAgICAgbGV0IGlwcyA9IHRoaXMuZm9ybS5pbnZva2VJcC5zcGxpdCgvXG4vZyk7CiAgICAgICAgICAgIC8vIOiwg+eUqOebruagh+Wtl+espuS4sgogICAgICAgICAgICB0aGlzLmZvcm0uaW52b2tlSXAgPSBpcHMuam9pbignICcpCiAgICAgICAgICB9CiAgICAgICAgICAvKioKICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGlwcy5sZW5ndGg7IGkrKykgewogICAgICAgICAgIGlmICghKGlwUmVnLnRlc3QoaXBzW2ldKSB8fCBpcFNlZ21lbnRSZWcudGVzdChpcHNbaV0pKSkgewogICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+i+k+WFpeato+ehrueahGlw5Zyw5Z2A5oiW5Zyw5Z2A5q6177yM5qOA5p+l5piv5ZCm5pyJ56m65qC85ZKM54m55q6K5a2X56ym77yBJykKICAgICAgICAgICByZXR1cm4KICAgICAgICAgICB9CiAgICAgICAgICAgfSAqLwoKCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmpvYlR5cGUgPT09IDApIHsKICAgICAgICAgICAgdGhpcy5mb3JtLmludm9rZVRhcmdldCA9ICdqa1NlcnZlci5zY2FuKFwnJHtqb2JJZH1cJyxcJycgKyB0aGlzLmZvcm0uaW52b2tlSXAgKyAnXCcpJwogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uam9iVHlwZSA9PT0gMSkgewogICAgICAgICAgICB0aGlzLmZvcm0uaW52b2tlVGFyZ2V0ID0gJ0Jhc2VTZXJ2ZXJWdWxuU2Nhbi5zY2FuKFwnJHtqb2JJZH1cJyxcJycgKyB0aGlzLmZvcm0uaW52b2tlSXAgKyAnXCcpJwogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uam9iVHlwZSA9PT0gMikgewogICAgICAgICAgICB0aGlzLmZvcm0uaW52b2tlVGFyZ2V0ID0gJ1dlYlNlcnZlclZ1bG5TY2FuLnNjYW4oXCcke2pvYklkfVwnLFwnJyArIHRoaXMuZm9ybS5pbnZva2VJcCArICdcJyknCiAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZm9ybS5qb2JUeXBlID09PSAzKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5pbnZva2VUYXJnZXQgPSAnY2xvdWRXYWxrZXJTY2FuLnNjYW4oXCcke2pvYklkfVwnKScKICAgICAgICAgIH0KCiAgICAgICAgICAvLyDku7vliqHliIbnu4QKICAgICAgICAgIHRoaXMuZm9ybS5qb2JHcm91cCA9ICdBU1NFVF9TQ0FOJwoKICAgICAgICAgIGlmICh0aGlzLmZvcm0uam9iSWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIHVwZGF0ZUpvYih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuaW52b2tlRGlzYWJsZSA9IGZhbHNlCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBhZGRKb2IodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmludm9rZURpc2FibGUgPSBmYWxzZQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICBsZXQgcm93cyA9IFsuLi50aGlzLnJvd3NdOwogICAgICByb3dzID0gcm93cy5maWx0ZXIoaXRlbSA9PiBpdGVtLmN1cnJlbnRTdGF0dXMgPT09IDEpOwogICAgICBpZiAocm93cy5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6YCJ5oup5Lit5pyJ5omr5o+P5Lit5Lu75Yqh77yM5peg5rOV5om56YeP5Yig6ZmkJyk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIGNvbnN0IGpvYklkcyA9IHJvdy5qb2JJZCB8fCB0aGlzLmlkczsKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5a6a5pe25Lu75Yqh57yW5Y+35Li644CQJyArIGpvYklkcyArICfjgJHnmoTmlbDmja7pobnvvJ8nKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsSm9iKGpvYklkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnbW9uaXRvci9zY2hlZHVsZS9leHBvcnQnLCB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcwogICAgICB9LCBgam9iXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["monitorIp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "monitorIp.vue", "sourceRoot": "src/views/frailty/monitor", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务名称\" prop=\"jobName\">\n                <el-input\n                  v-model=\"queryParams.jobName\"\n                  placeholder=\"请输入任务名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"扫描目标\" prop=\"invokeTarget\">\n                <el-input\n                  v-model=\"queryParams.invokeTarget\"\n                  placeholder=\"扫描目标\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务状态\" prop=\"status\">\n                <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.sys_job_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <!--<el-col :span=\"6\">-->\n              <!--<el-form-item label=\"执行策略\" prop=\"jobType\">-->\n                <!--<el-select v-model=\"queryParams.jobType\" placeholder=\"请选择执行策略\" clearable>-->\n                  <!--<el-option label=\"资产扫描监控\" :value=\"0\"/>-->\n                  <!--<el-option label=\"基础服务漏洞扫描\" :value=\"1\"/>-->\n                  <!--<el-option label=\"基础Web漏洞扫描\" :value=\"2\"/>-->\n                  <!--<el-option label=\"主机资产探测\" :value=\"3\"/>-->\n                <!--</el-select>-->\n              <!--</el-form-item>-->\n            <!--</el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">主机漏扫任务列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleScan\"\n                  v-hasPermi=\"['monitor:ipschedule:add']\"\n                >新增\n                </el-button>\n              </el-col>\n<!--              <el-col :span=\"1.5\">-->\n<!--                <el-button-->\n<!--                  class=\"btn1\"-->\n<!--                  size=\"small\"-->\n<!--                  :disabled=\"single\"-->\n<!--                  @click=\"handleUpdate\"-->\n<!--                  v-hasPermi=\"['monitor:schedule:edit']\"-->\n<!--                >修改-->\n<!--                </el-button>-->\n<!--              </el-col>-->\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"handleDelete\"\n                  v-hasPermi=\"['monitor:ipschedule:remove']\"\n                >批量删除\n                </el-button>\n              </el-col>\n              <!--            <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>-->\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          :data=\"jobList\"\n          ref=\"multipleTable\"\n          :row-key=\"getRowKey\"\n          @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <!--<el-table-column label=\"任务编号\" width=\"100\"  prop=\"jobId\"/>-->\n          <el-table-column label=\"任务名称\"  prop=\"jobName\" :show-overflow-tooltip=\"true\"/>\n          <!--<el-table-column label=\"执行策略\"  prop=\"jobGroup\">-->\n            <!--<template slot-scope=\"scope\">-->\n              <!--<span v-if=\"scope.row.jobType === 0\">资产扫描监控</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 1\">基础服务漏洞扫描</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 2\">基础Web漏洞扫描</span>-->\n              <!--<span v-else-if=\"scope.row.jobType === 3\">主机资产探测</span>-->\n            <!--</template>-->\n          <!--</el-table-column>-->\n          <el-table-column label=\"扫描目标\"  prop=\"ipShow\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <el-tooltip v-if=\"scope.row.jobType !== 3\" class=\"item\" placement=\"top\">\n                <div v-html=\"scope.row.ipOver\" slot=\"content\"></div>\n                <div class=\"oneLine\">\n                  {{ scope.row.ipShow }}\n                </div>\n              </el-tooltip>\n              <span v-else>-</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"执行周期\"  prop=\"cronTransfer\"/>\n          <el-table-column label=\"最近一次执行时间\"  prop=\"lastRunTime\"/>\n          <el-table-column label=\"最近扫描状态\"  prop=\"jobGroup\">\n            <template slot-scope=\"scope\">\n              <el-tag type=\"danger\" v-if=\"scope.row.currentStatus === 0\">未扫描</el-tag>\n              <el-tag v-else-if=\"scope.row.currentStatus === 1\">扫描中</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.currentStatus === 2\">已扫描</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"任务状态\" >\n            <template slot-scope=\"scope\">\n              <el-switch\n                v-model=\"scope.row.status\"\n                active-value=\"0\"\n                inactive-value=\"1\"\n                @change=\"handleStatusChange(scope.row)\"\n              ></el-switch>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"220\" fixed=\"right\" :show-overflow-tooltip=\"false\" class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleView(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:query']\"\n              >详情\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.currentStatus === 1\"\n                @click=\"handleUpdate(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:edit']\"\n              >编辑\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :class=\"{'table-delBtn':scope.row.currentStatus !== 1}\"\n                :disabled=\"scope.row.currentStatus === 1\"\n                @click=\"handleDelete(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:remove']\"\n              >删除\n              </el-button>\n              <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\"\n                           v-hasPermi=\"['monitor:ipschedule:query']\">\n            <span class=\"el-dropdown-link\">\n              <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\n            </span>\n                <el-dropdown-menu slot=\"dropdown\">\n                  <el-dropdown-item :disabled=\"scope.row.status === '1' || scope.row.currentStatus === 1\"\n                                    command=\"handleRun\" icon=\"el-icon-caret-right\">执行一次\n                  </el-dropdown-item>\n                  <!--<el-dropdown-item command=\"handleView\" icon=\"el-icon-view\">任务详细</el-dropdown-item>-->\n                  <el-dropdown-item command=\"handleJobLog\" icon=\"el-icon-s-operation\">调度日志</el-dropdown-item>\n                </el-dropdown-menu>\n              </el-dropdown>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <LeakScanDialog\n      :title=\"title\"\n      :edit-form=\"editForm\"\n      :edit-title=\"editTitle\"\n      :is-disable.sync=\"invokeDisable\"\n      @getList=\"getList\"\n      :scan-strategy-visible.sync=\"scanStrategyVisible\"/>\n\n\n\n    <!-- 任务日志详细 -->\n    <el-dialog title=\"任务详细\" v-if=\"openView\" :visible.sync=\"openView\" v-dialog-drag width=\"1200px\" append-to-body>\n      <ff-job-tasks  v-if=\"openView\" :jobId=\"jobId\" :job-type=\"jobType\" :job-row=\"editForm\" />\n    </el-dialog>\n    <!-- 调度日志 -->\n    <el-dialog title=\"调度日志\" v-if=\"openLogView\" :visible.sync=\"openLogView\" v-dialog-drag width=\"1200px\" append-to-body>\n      <job-log v-if=\"openLogView\" :jobId=\"logJobId\"></job-log>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"openLogView = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus} from \"@/api/safe/monitor\";\nimport QuestResultDetails from '../../safe/server/questResultDetails'\nimport LeakScanDialog from '../../safe/server/components/LeakScanDialog'\nimport FfJobTasks from './ffJobTasks'\nimport JobLog from '../../monitor/job/log'\n\nexport default {\n  name: \"Job\",\n  components: { JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },\n  dicts: ['sys_job_group', 'sys_job_status'],\n  props: {\n    toParams: {\n      type: Object,\n      default: () => {}\n    }\n  },\n  data() {\n    return {\n      jobType: undefined,\n      // 是否显示Cron表达式弹出层\n      openCron: false,\n      // 展示最近一次运行结果\n      openSelect: false,\n      // 遮罩层\n      loading: true,\n      // 任务ID\n      jobId: '',\n      logJobId: null,\n      totalScan: 0,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 定时任务表格数据\n      jobList: [],\n      // 弹出层标题\n      title: \"\",\n      editTitle: '',\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详细弹出层\n      openView: false,\n      openLogView: false,\n      scanStrategyVisible:  false,\n      editForm: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        jobType: 1,\n        jobName: undefined,\n        jobGroup: 'ASSET_SCAN',\n        status: undefined\n      },\n      invokeDisable: false,\n      isDisabled: false,\n      // 周期转换文字\n      cronText: '',\n      rows: [],\n      form: {},\n      // 表单校验\n      rules: {\n        jobName: [\n          {required: true, message: \"任务名称不能为空\", trigger: \"blur\"}\n        ],\n        invokeIp: [\n          {required: true, message: \"扫描IP不能为空\", trigger: \"blur\"}\n        ],\n        cronExpression: [\n          {required: true, message: \"cron执行表达式不能为空\", trigger: \"blur\"}\n        ]\n      },\n      getListInterval: null,\n      selectedIds: []\n    };\n  },\n  watch: {\n    toParams: {\n      handler(newVal) {\n        if(newVal && newVal.id){\n          this.handleJobLog({\n            jobId: newVal.id\n          });\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getList()\n    this.getListInterval = setInterval(() => {\n      this.loopGetList()\n    }, 10000)\n  },\n  destroyed() {\n    if(this.getListInterval){\n      clearInterval(this.getListInterval);\n    }\n  },\n  // watch: {\n  //   'dict.type.sys_job_group': {\n  //     handler(newVal) {\n  //       if (newVal.length > 0) {\n  //         let tmp = newVal.filter(s => s.label === '资产扫描')\n  //         this.form.jobGroup = tmp.length > 0 ? tmp[0].value : undefined\n  //         this.queryParams.jobGroup = this.form.jobGroup\n  //       }\n  //     },\n  //     deep: true\n  //   }\n  // },\n  methods: {\n    /** 查询定时任务列表 */\n    getList() {\n      this.loading = true;\n      listJob(this.queryParams).then(response => {\n        response.rows.forEach(s => {\n          if (s.invokeTarget) {\n            const target = s.invokeTarget\n            const start = target.indexOf('\\',\\'') + 3\n            const end = target.length - 2\n            const ips = target.substring(start, end)\n            const ipss = ips.split('|')\n            if (ipss.length > 1) {\n              s.ipShow = ipss[1].replaceAll(';', '  ')\n              s.ipOver = ipss[1].replaceAll(';', '<br>')\n            }\n            if(s.period === 0){\n              s.status = s.currentStatus === 1 ? '0' : '1'\n            }\n          }\n        })\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    loopGetList() {\n      listJob(this.queryParams).then(response => {\n        response.rows.forEach(s => {\n          if (s.invokeTarget) {\n            const target = s.invokeTarget\n            const start = target.indexOf('\\',\\'') + 3\n            const end = target.length - 2\n            const ips = target.substring(start, end)\n            const ipss = ips.split('|')\n            if (ipss.length > 1) {\n              s.ipShow = ipss[1].replaceAll(';', ' ')\n              s.ipOver = ipss[1].replaceAll(';', '<br>')\n            }\n            if(s.period === 0){\n              s.status = s.currentStatus === 1 ? '0' : '1'\n            }\n          }\n        })\n        const newJobList = response.rows\n        const selectedIds = [...this.selectedIds]\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.$nextTick(() => {\n          const rowsToSelect = newJobList.filter(row =>\n            selectedIds.includes(row.jobId)\n          );\n          this.$refs.multipleTable.clearSelection();\n          rowsToSelect.forEach(row => {\n            this.$refs.multipleTable.toggleRowSelection(row, true);\n          })\n        })\n      });\n    },\n    handleScan() {\n      this.invokeDisable = false\n      this.title = '添加任务';\n      this.editTitle = 'IP漏洞扫描'\n      this.editForm = {}\n      this.editForm.jobType = 1;\n      this.editForm.weakPw = '1';\n      this.editForm.status = '0';\n      this.editForm.cronExpression= '* * * * * ?';\n      this.editForm.period= 0;\n      this.editForm.cronTransfer= '立即执行';\n      this.scanStrategyVisible = true;\n    },\n    // 任务组名字典翻译\n    jobGroupFormat(row, column) {\n      return this.selectDictLabel(this.dict.type.sys_job_group, row.jobGroup);\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.invokeDisable = false\n      this.reset();\n    },\n    /** 确定后回传值 */\n    cronTabFill(val) {\n      this.form.cronExpression = val.cronText\n      this.form.period = val.period\n      this.form.cronTransfer = val.cronTransfer\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        jobId: undefined,\n        jobName: undefined,\n        invokeTarget: undefined,\n        cronExpression: undefined,\n        misfirePolicy: 0,\n        concurrent: 1,\n        period: 0,\n        jobType: 1,\n        status: \"0\"\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n\n    getRowKey(row) {\n      return row.jobId;\n    },\n\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.jobId);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n      this.rows = selection;\n      this.selectedIds = [...this.ids]\n    },\n    // 更多操作触发\n    handleCommand(command, row) {\n      switch (command) {\n        case \"lastResult\":\n          this.handleDetail(row);\n          break;\n        case \"handleRun\":\n          this.handleRun(row);\n          break;\n        case \"handleView\":\n          this.handleView(row);\n          break;\n        case \"handleJobLog\":\n          this.handleJobLog(row);\n          break;\n        default:\n          break;\n      }\n    },\n    // 任务状态修改\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.jobName + '\"任务吗？').then(function () {\n        return changeJobStatus(row.jobId, row.status);\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\");\n      }).catch(function () {\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\n      }).finally(() => {\n        var _this = this\n        setTimeout(() => {\n          _this.getList();\n        }, 1000);\n      });\n    },\n    /* 最近执行结果 */\n    handleDetail(row) {\n      this.jobId = row.jobId\n    },\n    /* 立即执行一次 */\n    handleRun(row) {\n      this.$modal.confirm('确认要立即执行一次\"' + row.jobName + '\"任务吗？').then(function () {\n        return runJob(row.jobId, row.jobGroup);\n      }).then(() => {\n        this.$modal.msgSuccess(\"执行成功\");\n      }).catch(() => {\n      }).finally(() => {\n        this.getList();\n      });\n    },\n    /** 任务详细信息 */\n    handleView(row) {\n      this.openView = true;\n      this.jobType = 2;\n      this.jobId = row.jobId;\n      this.editForm = {...row}\n    },\n    editNow(jobId) {\n      let filter = this.jobList.filter(item => item.jobId == jobId)\n      if (filter.length === 0) {\n        this.$message.error('未找到任务数据！')\n        return\n      } else {\n        if (filter[0].currentStatus === 1) {\n          this.$message.error('当前任务状态为正在扫描中，请勿更改！')\n          return\n        }\n        this.openView = false\n        this.handleUpdate(filter[0])\n      }\n    },\n    executeNow(jobId) {\n      let filter = this.jobList.filter(item => item.jobId == jobId)\n      if (filter.length === 0) {\n        this.$message.error('未找到任务数据！')\n        return\n      } else {\n        if (filter[0].status === '1' || filter[0].currentStatus === 1) {\n          this.$message.error('当前任务状态为暂停或正在扫描中，请勿执行！')\n          return\n        }\n        this.openView = false\n        this.handleRun(filter[0])\n      }\n\n    },\n    /** 任务日志列表查询 */\n    handleJobLog(row) {\n      this.logJobId = row.jobId || 0;\n      this.openLogView = true\n      // this.$router.push({path: '/monitor/job-log/index', query: {jobId: jobId}})\n    },\n    handleSelect() {\n      this.openSelect = true\n    },\n    /** 新增按钮操作 */\n    handleAdd(val) {\n      this.reset();\n      switch (val) {\n        case 0:\n          this.editTitle = '资产扫描监控'\n          break\n        case 1:\n          this.editTitle = '基础服务漏洞扫描'\n          break\n        case 2:\n          this.editTitle = 'Web漏洞扫描'\n          break\n        case 3:\n          this.editTitle = '主机资产探测'\n          break\n        default:\n          break\n      }\n      this.openSelect = false\n      this.form.jobType = val\n      this.open = true;\n      this.title = \"添加任务\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      switch (row.jobType) {\n        case 0:\n          this.editTitle = '资产扫描监控'\n          break\n        case 1:\n          this.editTitle = '基础服务漏洞扫描'\n          break\n        default:\n          break\n      }\n      this.reset();\n      const jobId = row.jobId || this.ids;\n      getJob(jobId).then(response => {\n        const target = response.data.invokeTarget\n        const start = target.indexOf('\\',\\'') + 3\n        const end = target.length - 2\n        const ips = target.substring(start, end)\n        const ipss = ips.split('|')\n        if (ipss.length > 1) {\n          response.data.invokeIp = ipss[1].replaceAll(';', '\\n')\n          response.data.weakPw = ipss[4]\n        }\n        this.editForm= response.data;\n        this.scanStrategyVisible = true;\n        this.invokeDisable = true\n        this.title = \"修改任务\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm: function () {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // ip正则\n          const ipReg = /^(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|[1-9])\\.((1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\.){2}(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)$/\n          // ip段正则\n          const ipSegmentReg = /^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.([0-9]|\\d{1,2}|1\\d{1,2}|2[0-4]\\d|25[0-5])-([1-9]|\\d{1,2}|1\\d{1,2}|2[0-4]\\d|25[0-5])$/\n          // 带子网掩码正则\n          // const ipMaskReg = /^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\/(\\d{1,2})$/\n\n          if (this.form.invokeIp) {\n            let ips = this.form.invokeIp.split(/\\n/g);\n            // 调用目标字符串\n            this.form.invokeIp = ips.join(' ')\n          }\n          /**\n           for (let i = 0; i < ips.length; i++) {\n           if (!(ipReg.test(ips[i]) || ipSegmentReg.test(ips[i]))) {\n           this.$message.error('请输入正确的ip地址或地址段，检查是否有空格和特殊字符！')\n           return\n           }\n           } */\n\n\n          if (this.form.jobType === 0) {\n            this.form.invokeTarget = 'jkServer.scan(\\'${jobId}\\',\\'' + this.form.invokeIp + '\\')'\n          } else if (this.form.jobType === 1) {\n            this.form.invokeTarget = 'BaseServerVulnScan.scan(\\'${jobId}\\',\\'' + this.form.invokeIp + '\\')'\n          } else if (this.form.jobType === 2) {\n            this.form.invokeTarget = 'WebServerVulnScan.scan(\\'${jobId}\\',\\'' + this.form.invokeIp + '\\')'\n          } else if (this.form.jobType === 3) {\n            this.form.invokeTarget = 'cloudWalkerScan.scan(\\'${jobId}\\')'\n          }\n\n          // 任务分组\n          this.form.jobGroup = 'ASSET_SCAN'\n\n          if (this.form.jobId != undefined) {\n            updateJob(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.invokeDisable = false\n              this.getList();\n            })\n          } else {\n            addJob(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.invokeDisable = false\n              this.getList();\n            })\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      let rows = [...this.rows];\n      rows = rows.filter(item => item.currentStatus === 1);\n      if (rows.length > 0) {\n        this.$message.error('选择中有扫描中任务，无法批量删除');\n        return false;\n      }\n      const jobIds = row.jobId || this.ids;\n      this.$modal.confirm('是否确认删除定时任务编号为【' + jobIds + '】的数据项？').then(function () {\n        return delJob(jobIds);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {\n      });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download('monitor/schedule/export', {\n        ...this.queryParams\n      }, `job_${new Date().getTime()}.xlsx`)\n    }\n  }\n};\n</script>\n<style src=\"../../../assets/styles/assetIndex.scss\" scoped lang=\"scss\"/>\n<style scoped lang=\"scss\">\n.policyCol {\n  min-width: 330px;\n  margin-top: 10px;\n}\n\n.policyDesc {\n  display: flex;\n  height: 80px;\n}\n\n.policyTxt {\n  margin-left: 10px;\n  line-height: 20px;\n}\n\n.policyTitle {\n  height: 40px;\n  line-height: 40px;\n}\n\n.oneLine {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n</style>\n"]}]}