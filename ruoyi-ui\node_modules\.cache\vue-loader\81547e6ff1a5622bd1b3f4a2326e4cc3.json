{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\threat\\overview\\component\\PointsOverview.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\threat\\overview\\component\\PointsOverview.vue", "mtime": 1755762306448}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PointsOverview.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PointsOverview.vue", "sourceRoot": "src/views/threat/overview/component", "sourcesContent": ["<!--扣分总览-->\n<template>\n  <div class=\"points-overview\">\n    <div class=\"points-overview-header\">\n      <div class=\"proportion\">\n        <div class=\"score-info\">\n          <div>您的安全总体得分<span style=\"margin: 0 5px\">{{ scoringScale }}</span>；</div>\n          <div>您的资产安全状况<span>{{ scaleState }}</span><span>{{scoringComment}}</span>，建议您及时关注安全。</div>\n        </div>\n        <div class=\"grade-proportion\">\n          <div class=\"grade-item\" v-for=\"(item, index) in gradeItems\" :key=\"index\" :style=\"{ background: item.color }\">\n            <span style=\"color: #101010\">{{ item.label }}</span>\n            <!-- 添加箭头指示 -->\n            <img\n              v-if=\"item.label === scoringScale\"\n              class=\"grade-arrow\"\n              src=\"@/assets/images/overview/arrow-up-fill.png\"\n              alt=\"当前等级\"\n            />\n          </div>\n        </div>\n\n      </div>\n    </div>\n    <div class=\"points-overview-container\">\n      <div class=\"score-details\">\n        <div class=\"chart-title-div\"><img src=\"@/assets/images/overview/alarmRanking.png\" alt=\"\"/>得分详情</div>\n        <div class=\"score-details-container\" v-loading=\"scoreLoading\">\n          <!--得分-->\n          <div class=\"chart-container\">\n            <div style=\"width: 100%; height: 100%\" ref=\"speedGaugeChart\"></div>\n          </div>\n          <!--得分详情雷达图-->\n          <div class=\"chart-container\">\n            <div style=\"width: 100%; height: 100%\" ref=\"basicRadarChart\"></div>\n          </div>\n        </div>\n      </div>\n      <div class=\"points-module\">\n        <div class=\"chart-title-div\"><img src=\"@/assets/images/overview/alarmRanking.png\" alt=\"\"/>扣分详情</div>\n        <div class=\"points-module-container\" v-loading=\"pointsLoading\">\n          <div\n            class=\"tb-div-table\"\n            ref=\"scrollableTable\"\n            @scroll=\"handleScroll\"\n            v-if=\"pointsModuleData.length\">\n            <div class=\"tb-div-table-item\" v-for=\"(item, index) in pointsModuleData\" :key=\"index\">\n              <img src=\"@/assets/images/overview/fab-fa-windows.png\" alt=\"\"/>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionDate || '--' }}</div>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionType || '--' }}</div>\n              <div class=\"tb-div-table-item-div\">{{ item.deductionLevel || '--' }}</div>\n              <div class=\"tb-div-table-item-div-num\"><span>{{ '-'+item.deductionScore || 0 }}</span></div>\n              <div class=\"tb-div-table-item-div\"><span @click=\"handleDetail(item)\">查看详情</span></div>\n            </div>\n            <div v-if=\"!isEmpty\" class=\"load-more\">\n              加载中...\n            </div>\n            <div v-if=\"isEmpty && pointsModuleData.length\" class=\"load-more\">\n              没有更多数据了\n            </div>\n          </div>\n          <div class=\"tb-div-table\" v-else>\n            <el-empty description=\"暂无数据\" :image-size=\"120\"></el-empty>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {win} from \"codemirror/src/util/dom\";\nimport { listTblDeductionDetail ,getScoreDetails } from \"@/api/aqsoc/deduction-detail/tblDeductionDetail\";\n\nexport default {\n  name: \"PointsOverview\",\n  props: {\n    deptId: {\n      type: [String, Number],\n      required: false,\n      default: null,\n    },\n  },\n  data() {\n    return {\n      scoreLoading: false,\n      pointsLoading: false,\n      // 等级\n      gradeItems: [\n        { label: 'E', color: '#DE868F' },\n        { label: 'D', color: '#FCCA00' },\n        { label: 'C', color: '#F4CE98' },\n        { label: 'B', color: '#9ACD32' },\n        { label: 'A', color: '#CCF783' }\n      ],\n      // 扣分详情\n      pointsModuleData:[],\n      totalCount: 0,\n      //得分等级\n      scoringScale: 'A',\n      //得分评价\n      scoringComment: '优秀',\n      totalNumber: 100,\n      indicator: [],\n      value:[],\n      speedGaugeChart: null,\n      basicRadarChart: null,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20\n      },\n      scoreQueryParams: {\n        deptId: null\n      },\n      loadingMore: false,\n      hasMoreData: true,\n    }\n  },\n  async mounted() {\n    await this.getList();\n    await this.getScoreDetails();\n    this.getSpeedGaugeChart();\n    this.getBasicRadarChart();\n  },\n  created() {\n    //this.getList();\n  },\n  beforeDestroy() {\n    this.destroyCharts();\n  },\n  watch: {\n    deptId: {\n      handler(val) {\n        this.queryParams.deptId = val;\n        this.scoreQueryParams.deptId = val;\n        this.getList();\n        this.getScoreDetails();\n      },\n      immediate: true\n    }\n  },\n  computed: {\n    scaleState() {\n      let state;\n      switch (this.scoringScale) {\n        case 'A':\n          state = '优秀';\n          break;\n        case 'B':\n          state = '良好';\n          break;\n        case 'C':\n          state = '一般';\n          break;\n        case 'D':\n          state = '差';\n          break;\n        case 'E':\n          state = '极差';\n          break;\n        default:\n          state = '未知';\n      }\n      return state;\n    },\n    isEmpty() {\n      return this.pointsModuleData.length === this.totalCount;\n    }\n  },\n  methods: {\n    destroyCharts() {\n      if (this.speedGaugeChart) {\n        window.removeEventListener('resize', this.speedGaugeChart.resizeHandler);\n        this.speedGaugeChart.dispose();\n        this.speedGaugeChart = null;\n      }\n      if (this.basicRadarChart) {\n        window.removeEventListener('resize', this.basicRadarChart.resizeHandler);\n        this.basicRadarChart.dispose();\n        this.basicRadarChart = null;\n      }\n    },\n\n    // 处理滚动事件\n    handleScroll() {\n      const scrollableDiv = this.$refs.scrollableTable;\n      // 计算是否滚动到底部（距离底部50px范围内）\n      const isBottom = scrollableDiv.scrollHeight - scrollableDiv.scrollTop <= scrollableDiv.clientHeight + 50;\n\n      if (isBottom && !this.loadingMore && this.hasMoreData) {\n        this.loadMoreData();\n      }\n    },\n\n    // 加载更多数据\n    async loadMoreData() {\n      this.loadingMore = true;\n\n      try {\n        // 增加页码\n        this.queryParams.pageNum += 1;\n\n        const response = await listTblDeductionDetail(this.queryParams);\n\n        if (response.rows && response.rows.length > 0) {\n          // 将新数据追加到现有数据\n          this.pointsModuleData = [...this.pointsModuleData, ...response.rows];\n\n          // 检查是否还有更多数据\n          this.hasMoreData = response.rows.length >= this.queryParams.pageSize;\n        } else {\n          this.hasMoreData = false;\n        }\n      } catch (error) {\n        console.error('加载更多数据失败:', error);\n        // 回退页码\n        this.queryParams.pageNum -= 1;\n      } finally {\n        this.loadingMore = false;\n      }\n    },\n\n    async getList() {\n      this.queryParams.pageNum = 1;\n      this.scoreLoading = true;\n      this.pointsLoading = true;\n      await listTblDeductionDetail(this.queryParams).then(response => {\n        this.scoreLoading = false;\n        this.pointsModuleData = response.rows;\n      });\n    },\n\n    // 得分详情数据\n    async getScoreDetails() {\n      try {\n        const response = await getScoreDetails(this.scoreQueryParams);\n        if (response.data) {\n          this.totalNumber = response.data.totalNumber;\n          this.pointsLoading = false;\n          // 使用查找表代替多重判断\n          const scoreLevels = [\n            { min: 90, scale: 'A', comment: '优秀' },\n            { min: 80, scale: 'B', comment: '良好' },\n            { min: 70, scale: 'C', comment: '一般' },\n            { min: 60, scale: 'D', comment: '差' },\n            { min: 50, scale: 'E', comment: '极差' },\n            { min: -Infinity, scale: 'E', comment: '极差' } // 默认情况\n          ];\n\n          const matchedLevel = scoreLevels.find(level => this.totalNumber >= level.min);\n          if (matchedLevel) {\n            this.scoringScale = matchedLevel.scale;\n            this.scoringComment = matchedLevel.comment;\n          }\n\n          this.indicator = response.data.indicator || [];\n          this.value = this.indicator.map(item => item?.value ?? null);\n        }\n      } catch (error) {\n        console.error('获取评分详情失败:', error);\n      }\n    },\n\n    // 具体得分\n    getSpeedGaugeChart() {\n      if (this.speedGaugeChart) return;\n      this.speedGaugeChart = this.$echarts.init(this.$refs.speedGaugeChart);\n      const resizeHandler = () => this.speedGaugeChart && this.speedGaugeChart.resize();\n      window.addEventListener('resize', resizeHandler);\n\n      this.speedGaugeChart.resizeHandler = resizeHandler;\n      this.speedGaugeChart.setOption( {\n        series: [\n          {\n            type: 'gauge',\n            radius: '90%',\n            center: ['50%', '55%'],\n            progress: {\n              show: true,\n              width: 10,\n              itemStyle: {\n                color: '#bd3124'\n              }\n            },\n            // 表盘外圈样式\n            axisLine: {\n              lineStyle: {\n                width: 10,\n              }\n            },\n            axisTick: {\n              show: true, // 显示小刻度\n              splitNumber: 5, // 小刻度的数量\n              length: -8, // 小刻度线长\n              lineStyle: {\n                color: '#63677a', // 小刻度颜色\n                width: 1 // 小刻度宽度\n              }\n            },\n            // 刻度样式\n            splitLine: {\n              length: 10,\n              lineStyle: {\n                width: 2,\n                color: '#63677a'\n              }\n            },\n            // 数值样式\n            axisLabel: {\n              distance: 10,\n              color: '#101010',\n              fontSize: 12\n            },\n            pointer: {\n              show: true,\n              length: '80%',\n              width: 5,\n              offsetCenter: [0, '0%'],\n              itemStyle: {\n                color: '#bd3124' // 指针颜色\n              }\n            },\n            // 锚点指针样式\n            anchor: {\n              show: false,\n              showAbove: false,\n              size: 10,\n              itemStyle: {\n                borderWidth: 5,\n                // 设置指针颜色\n                color: '#bd3124',    // 指针填充色\n                borderColor: '#bd3124'\n              }\n            },\n            title: {\n              show: false\n            },\n            detail: {\n              valueAnimation: true,\n              fontSize: 25,\n              offsetCenter: [0, '50%'],\n              formatter: function (value) {\n                return value.toFixed(0) + '分';\n              }\n\n            },\n            data: [\n              {\n                value: this.totalNumber\n              }\n            ],\n          }\n        ]\n      })\n    },\n    // 扣分详情雷达图\n    getBasicRadarChart() {\n      if (this.basicRadarChart) return;\n      this.basicRadarChart = this.$echarts.init(this.$refs.basicRadarChart);\n      const resizeHandler = () => this.basicRadarChart && this.basicRadarChart.resize();\n      window.addEventListener('resize', resizeHandler);\n\n      // 存储事件处理器以便后续移除\n      this.basicRadarChart.resizeHandler = resizeHandler;\n      this.basicRadarChart = this.$echarts.init(this.$refs.basicRadarChart);\n      window.addEventListener('resize', this.basicRadarChart.resize);\n      const self = this;\n      this.basicRadarChart.setOption({\n        /*legend: {\n          data: ['Allocated Budget', 'Actual Spending']\n        },*/\n        tooltip: {\n          trigger: 'item',\n          formatter: function (params) {\n            // 使用闭包中的 self 访问组件数据\n            const indicatorNames = self.indicator.map(item => {\n              return `<div>${item.name}: ${item.value}%</div>`;\n            }).join('');\n\n            return `<div>\n          <div style=\"font-weight: bold; margin-bottom: 5px;\">得分详情</div>\n          ${indicatorNames}        </div>`;\n          }\n        },\n        radar: {\n          center: ['50%', '52%'],\n          radius: ['25%', '70%'],\n          // shape: 'circle',\n          indicator: this.indicator,\n          name: {\n            textStyle: {\n              color: '#101010'\n            }\n          },\n          splitArea: {\n            show: false\n          },\n          // 轴线样式等\n          axisLine: {\n            show: false,\n            lineStyle: {\n              color: '#666', // 轴线颜色\n            }\n          }\n        },\n        series: [\n          {\n            name: 'Budget vs spending',\n            type: 'radar',\n            data: [\n              {\n                value: this.value,\n                name: 'Actual Spending',\n                itemStyle: {\n                  color: '#d97559'\n                }\n              }\n            ],\n          }\n        ]\n      })\n    },\n\n    // 详情\n    handleDetail(row) {\n      if (row.riskType === '外部威胁') {\n        this.$router.push({\n          path: '/service-ledger/theratManage',\n          query: {\n            type: '1',\n            referenceId: row.referenceId\n          }\n        });\n      }\n\n      if (row.riskType === '内部漏洞') {\n        let queryParams = {};\n        if (row.deductionType === '主机漏洞') {\n          queryParams = {\n            type: '1',\n            referenceId: row.referenceId\n          }\n        }\n        if (row.deductionType === 'Web漏洞') {\n          queryParams = {\n            type: '2',\n            referenceId: row.referenceId\n          }\n        }\n        if (row.deductionType === '弱口令') {\n          queryParams = {\n            type: '3',\n            referenceId: row.referenceId\n          }\n        }\n        this.$router.push({\n          path: '/service-ledger/frailty',\n          query: queryParams\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.points-overview {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  .points-overview-header {\n    width: 100%;\n    height: 57px;\n    display: flex;\n    flex-direction: column;\n    .proportion {\n      width: 100%;\n      height: 30px;\n      flex: 1;\n      margin-top: 27px;\n      display: flex;\n      align-items: center;\n      .score-info {\n        width: 50%;\n        height: 30px;\n        line-height: 30px;\n        background: #E8F4FE;\n        display: flex;\n        font-size: 12px;\n        color: #101010;\n        div {\n          span {\n            color: #bd3124;\n          }\n          &:first-child {\n            text-indent: 15px;\n          }\n        }\n      }\n\n      .grade-proportion {\n        display: flex;\n        height: 30px;\n        line-height: 30px;\n        flex: 1;\n        .grade-item {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #fff;\n          font-size: 12px;\n          flex: 1;\n          position: relative;\n\n          .grade-arrow {\n            position: absolute;\n            top: -27px; /* 定位到等级条上方 */\n            left: 50%;\n            transform: translateX(-50%);\n            width: 24px;\n            height: 24px;\n          }\n\n          &:first-child {\n            width: 200px !important; // 第一个元素固定宽度为 200px\n            flex: none; // 不参与弹性布局的自动分配\n          }\n\n          &:not(:first-child) {\n            flex: 1; // 其余元素均分剩余空间\n          }\n        }\n      }\n    }\n  }\n  .points-overview-container {\n    height: 600px;\n    display: flex;\n    flex-direction: row;\n    gap: 8px;\n    .score-details {\n      width: 50%;\n      border-radius: 4px;\n      border: 1px solid rgba(206,206,206,1);\n      .score-details-container {\n        width: 100%;\n        height: calc(100% - 30px);\n        padding: 12px;\n        display: flex;\n        flex-direction: column;\n        justify-content: space-between;\n        .chart-container {\n          width: 100%;\n          height: 48%;\n          background-color: rgba(242,244,248,0.8);\n        }\n      }\n    }\n    .points-module {\n      width: 50%;\n      border-radius: 4px;\n      border: 1px solid rgba(206,206,206,1);\n      .points-module-container {\n        height: 100%;\n        padding: 0 12px 12px;\n        .tb-div-table {\n          width: 100%;\n          height: calc(100% - 30px);\n          overflow-y: auto;\n          .tb-div-table-item {\n            display: flex;\n            height: 25px;\n            margin: 20px 10px;\n            font-size: 14px;\n            color: rgba(154,154,154,1);\n            justify-content: space-between;\n            align-items: center;\n            img {\n              width: 15px;\n              height: 15px;\n            }\n            .tb-div-table-item-div {\n              margin-left: 20px;\n              white-space: nowrap;         // 防止文本换行\n              overflow: hidden;            // 隐藏超出部分\n              text-overflow: ellipsis;     // 超出部分显示省略号\n              span {\n                cursor: pointer;\n                color: #347CAF;\n                text-decoration: underline;\n              }\n            }\n            .tb-div-table-item-div-num {\n              span {\n                color: #BD3124;\n              }\n            }\n          }\n          .tb-div-table-item:first-child {\n            margin: 10px 10px 20px;\n          }\n          .tb-div-table-item:last-child {\n            margin: 20px 10px 0;\n          }\n          .load-more {\n            width: 100%;\n            text-align: center;\n          }\n        }\n      }\n    }\n  }\n\n  .chart-title-div {\n    width: 130px;\n    height: 30px;\n    line-height: 30px;\n    border-radius: 4px;\n    color: rgba(48,114,198,1);\n    font-size: 12px;\n    text-align: right;\n    background-color: rgba(232,244,254,0.2);\n    border: 1px solid rgba(187,187,187,0.5);\n    display: flex;\n    align-items: center;\n    img {\n      width: 18px;\n      height: 18px;\n      margin: 0 10px;\n    }\n  }\n}\n\n</style>\n"]}]}