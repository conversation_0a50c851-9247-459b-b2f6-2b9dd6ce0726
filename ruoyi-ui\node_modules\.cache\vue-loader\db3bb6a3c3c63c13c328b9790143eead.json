{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\apiAlarmList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\apiAlarmList.vue", "mtime": 1755679994095}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Rmxvd1Jpc2tBc3NldHMsIGhhbmRsZUFsYXJtLCBiYXRjaEhhbmRsZUFsYXJtcywgZGVsRmxvd1Jpc2tBc3NldHMgfSBmcm9tICdAL2FwaS9mZnNhZmUvZmxvd1Jpc2tBc3NldHMnDQppbXBvcnQge2xpc3REZXZpY2VDb25maWd9IGZyb20gIkAvYXBpL2Zmc2FmZS9kZXZpY2VDb25maWciOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdBcGlBbGFybUxpc3QnLA0KICBkaWN0czogWydmbG93X3Jpc2tfdHlwZSddLA0KICBwcm9wczogew0KICAgIHByb3BzQWN0aXZlTmFtZTogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIHByb3BzUXVlcnlQYXJhbXM6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6IG51bGwNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRldmljZUNvbmZpZ0xpc3Q6IFtdLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiBmYWxzZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyBBUEnlkYrorabliJfooagNCiAgICAgIGFwaUFsYXJtTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrlpITnva7lvLnlh7rlsYINCiAgICAgIGRpc3Bvc2VPcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuaJuemHj+WkhOe9ruW8ueWHuuWxgg0KICAgICAgYmF0Y2hEaXNwb3NlT3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLror6bmg4XlvLnlh7rlsYINCiAgICAgIGRldGFpbE9wZW46IGZhbHNlLA0KICAgICAgLy8g6K+m5oOF5pWw5o2uDQogICAgICBkZXRhaWxEYXRhOiB7fSwNCiAgICAgIC8vIOWkhOe9ruihqOWNlQ0KICAgICAgZGlzcG9zZUZvcm06IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIGhhbmRsZVN0YXRlOiB1bmRlZmluZWQsDQogICAgICAgIGhhbmRsZURlc2M6ICcnDQogICAgICB9LA0KICAgICAgLy8g5aSE572u6KGo5Y2V6aqM6K+B6KeE5YiZDQogICAgICBkaXNwb3NlUnVsZXM6IHsNCiAgICAgICAgaGFuZGxlU3RhdGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5aSE572u54q25oCBJywgdHJpZ2dlcjogJ2NoYW5nZScgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgLy8g5om56YeP5aSE572u6KGo5Y2VDQogICAgICBiYXRjaERpc3Bvc2VGb3JtOiB7DQogICAgICAgIGV2ZW50SWRzOiBbXSwNCiAgICAgICAgaGFuZGxlU3RhdGU6ICcnLA0KICAgICAgICBoYW5kbGVEZXNjOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOaXpeacn+iMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICByaXNrQXNzZXRzOiB1bmRlZmluZWQsDQogICAgICAgIHJpc2tUeXBlOiB1bmRlZmluZWQsDQogICAgICAgIGhhbmRsZVN0YXRlOiAwLA0KICAgICAgICBwYXJhbXM6IHsNCiAgICAgICAgICBiZWdpblRpbWU6IHVuZGVmaW5lZCwNCiAgICAgICAgICBlbmRUaW1lOiB1bmRlZmluZWQNCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIC8vIOWkhOe9rueKtuaAgeWtl+WFuA0KICAgICAgaGFuZGxlU3RhdGVPcHRpb25zOiBbDQogICAgICAgIHsgdmFsdWU6IDAsIGxhYmVsOiAn5pyq5aSE572uJyB9LA0KICAgICAgICB7IHZhbHVlOiAxLCBsYWJlbDogJ+W3suWkhOe9ricgfSwNCiAgICAgICAgeyB2YWx1ZTogMiwgbGFiZWw6ICflv73nlaUnIH0sDQogICAgICAgIHsgdmFsdWU6IDMsIGxhYmVsOiAn5aSE572u5LitJyB9DQogICAgICBdLA0KICAgICAgaGFuZGxlU3RhdGVPcHRpb246IFsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn5bey5aSE572uJywNCiAgICAgICAgICB2YWx1ZTogMQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICflv73nlaUnLA0KICAgICAgICAgIHZhbHVlOiAyDQogICAgICAgIH0NCiAgICAgIF0NCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgcHJvcHNBY3RpdmVOYW1lOiB7DQogICAgICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgICAgICBpZiAobmV3VmFsID09PSAnYXBpQWxhcm0nKSB7DQogICAgICAgICAgLy8g56Gu5L+d5pe26Ze05Y+C5pWw5bey6K6+572u77yM6YG/5YWN5peg5pe26Ze05Y+C5pWw55qE5p+l6K+iDQogICAgICAgICAgaWYgKCF0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5iZWdpblRpbWUgfHwgIXRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmVuZFRpbWUpIHsNCiAgICAgICAgICAgIHRoaXMuc2V0RGVmYXVsdERhdGVSYW5nZSgpDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIH0NCiAgICAgIH0sDQogICAgICBpbW1lZGlhdGU6IHRydWUNCiAgICB9LA0KICAgIHByb3BzUXVlcnlQYXJhbXM6IHsNCiAgICAgIGhhbmRsZXIobmV3VmFsKSB7DQogICAgICAgIGlmIChuZXdWYWwpIHsNCiAgICAgICAgICAvLyDkv53nlZnlt7Lorr7nva7nmoTml7bpl7TojIPlm7Tlj4LmlbDvvIzpgb/lhY3ooqvnqbrnmoRwYXJhbXPopobnm5YNCiAgICAgICAgICBjb25zdCBvcmlnaW5hbEJlZ2luVGltZSA9IHRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmJlZ2luVGltZQ0KICAgICAgICAgIGNvbnN0IG9yaWdpbmFsRW5kVGltZSA9IHRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmVuZFRpbWUNCg0KICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMsIC4uLm5ld1ZhbCB9DQoNCiAgICAgICAgICAvLyDlpoLmnpzmlrDnmoTmn6Xor6Llj4LmlbDmsqHmnInml7bpl7TojIPlm7TvvIzliJnmgaLlpI3ljp/mnInnmoTml7bpl7TojIPlm7QNCiAgICAgICAgICBpZiAoIW5ld1ZhbC5wYXJhbXMgfHwgKCFuZXdWYWwucGFyYW1zLmJlZ2luVGltZSAmJiAhbmV3VmFsLnBhcmFtcy5lbmRUaW1lKSkgew0KICAgICAgICAgICAgaWYgKG9yaWdpbmFsQmVnaW5UaW1lICYmIG9yaWdpbmFsRW5kVGltZSkgew0KICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5iZWdpblRpbWUgPSBvcmlnaW5hbEJlZ2luVGltZQ0KICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5lbmRUaW1lID0gb3JpZ2luYWxFbmRUaW1lDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5Y+q5pyJ5b2T5YmN5qCH562+5pivYXBpQWxhcm3ml7bmiY3op6blj5Hmn6Xor6LvvIzpgb/lhY3ph43lpI3mn6Xor6INCiAgICAgICAgICBpZiAodGhpcy5wcm9wc0FjdGl2ZU5hbWUgPT09ICdhcGlBbGFybScpIHsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9LA0KICAgICAgZGVlcDogdHJ1ZQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyDorr7nva7pu5jorqTmn6Xor6Lml6XmnJ/ojIPlm7TkuLrmnIDov5E35aSpDQogICAgdGhpcy5zZXREZWZhdWx0RGF0ZVJhbmdlKCkNCiAgICAvLyDkuI3lnKhjcmVhdGVk5Lit6LCD55SoZ2V0TGlzdO+8jOeUsXdhdGNo5aSE55CGDQogICAgdGhpcy5nZXREZXZpY2VDb25maWdMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXREZXZpY2VDb25maWdMaXN0KCl7DQogICAgICBsaXN0RGV2aWNlQ29uZmlnKHtxdWVyeUFsbERhdGE6IHRydWV9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuZGV2aWNlQ29uZmlnTGlzdCA9IHJlcy5yb3dzOw0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDorr7nva7pu5jorqTml6XmnJ/ojIPlm7QgKi8NCiAgICBzZXREZWZhdWx0RGF0ZVJhbmdlKCkgew0KICAgICAgY29uc3QgZW5kID0gbmV3IERhdGUoKQ0KICAgICAgY29uc3Qgc3RhcnQgPSBuZXcgRGF0ZSgpDQogICAgICBzdGFydC5zZXRUaW1lKHN0YXJ0LmdldFRpbWUoKSAtIDM2MDAgKiAxMDAwICogMjQgKiA3KQ0KDQogICAgICAvLyDnoa7kv53lvIDlp4vml7bpl7TkuLogMDA6MDA6MDDvvIznu5PmnZ/ml7bpl7TkuLogMjM6NTk6NTkNCiAgICAgIHN0YXJ0LnNldEhvdXJzKDAsIDAsIDAsIDApDQogICAgICBlbmQuc2V0SG91cnMoMjMsIDU5LCA1OSwgOTk5KQ0KDQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFsNCiAgICAgICAgdGhpcy5wYXJzZVRpbWUoc3RhcnQsICd7eX0te219LXtkfSB7aH06e2l9OntzfScpLA0KICAgICAgICB0aGlzLnBhcnNlVGltZShlbmQsICd7eX0te219LXtkfSB7aH06e2l9OntzfScpDQogICAgICBdDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5iZWdpblRpbWUgPSB0aGlzLmRhdGVSYW5nZVswXQ0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuZW5kVGltZSA9IHRoaXMuZGF0ZVJhbmdlWzFdDQogICAgfSwNCiAgICAvKiog5p+l6K+iQVBJ5ZGK6K2m5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCg0KICAgICAgLy8g6YCa55+l54i257uE5Lu25ZCM5q2l5p+l6K+i5p2h5Lu25ZKM5oyJ6ZKu6YCJ5Lit54q25oCBDQogICAgICB0aGlzLiRlbWl0KCdxdWVyeS1jaGFuZ2UnLCB7DQogICAgICAgIHJpc2tUeXBlOiB0aGlzLnF1ZXJ5UGFyYW1zLnJpc2tUeXBlLA0KICAgICAgICBkZXZpY2VDb25maWdJZDogdGhpcy5xdWVyeVBhcmFtcy5kZXZpY2VDb25maWdJZA0KICAgICAgfSkNCg0KICAgICAgLy8g5ZCM5q2l6K+35rGC57G75Z6L57uf6K6h5pWw5o2uDQogICAgICB0aGlzLiRlbWl0KCdnZXRMaXN0JywgeyAuLi50aGlzLnF1ZXJ5UGFyYW1zIH0pDQogICAgICBsaXN0Rmxvd1Jpc2tBc3NldHModGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuYXBpQWxhcm1MaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDml6XmnJ/ojIPlm7Tlj5HnlJ/lj5jljJYNCiAgICBoYW5kbGVEYXRlUmFuZ2VDaGFuZ2UodmFsKSB7DQogICAgICBpZiAodmFsICYmIHZhbC5sZW5ndGggPT09IDIpIHsNCiAgICAgICAgLy8g56Gu5L+d5byA5aeL5pe26Ze05Li6IDAwOjAwOjAw77yM57uT5p2f5pe26Ze05Li6IDIzOjU5OjU5DQogICAgICAgIGNvbnN0IHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHZhbFswXSkNCiAgICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKHZhbFsxXSkNCg0KICAgICAgICBzdGFydERhdGUuc2V0SG91cnMoMCwgMCwgMCwgMCkNCiAgICAgICAgZW5kRGF0ZS5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpDQoNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuYmVnaW5UaW1lID0gdGhpcy5wYXJzZVRpbWUoc3RhcnREYXRlLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKQ0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmFtcy5lbmRUaW1lID0gdGhpcy5wYXJzZVRpbWUoZW5kRGF0ZSwgJ3t5fS17bX0te2R9IHtofTp7aX06e3N9JykNCg0KICAgICAgICAvLyDmm7TmlrBkYXRlUmFuZ2XmmL7npLrlgLwNCiAgICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbDQogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuYmVnaW5UaW1lLA0KICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmVuZFRpbWUNCiAgICAgICAgXQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuYmVnaW5UaW1lID0gdW5kZWZpbmVkDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmVuZFRpbWUgPSB1bmRlZmluZWQNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0KCkNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgaGFuZGxlU3RhdGU6ICcnLA0KICAgICAgICBoYW5kbGVEZXNjOiAnJw0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oJ2Zvcm0nKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgLy8g5aaC5p6c5pe26Ze06IyD5Zu05Li656m677yM6K6+572u6buY6K6k5pe26Ze06IyD5Zu0DQogICAgICBpZiAoIXRoaXMucXVlcnlQYXJhbXMucGFyYW1zLmJlZ2luVGltZSB8fCAhdGhpcy5xdWVyeVBhcmFtcy5wYXJhbXMuZW5kVGltZSkgew0KICAgICAgICB0aGlzLnNldERlZmF1bHREYXRlUmFuZ2UoKQ0KICAgICAgfQ0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KDQogICAgICAvLyDpgJrnn6XniLbnu4Tku7blkIzmraXmn6Xor6LmnaHku7blkozmjInpkq7pgInkuK3nirbmgIENCiAgICAgIHRoaXMuJGVtaXQoJ3F1ZXJ5LWNoYW5nZScsIHsNCiAgICAgICAgcmlza1R5cGU6IHRoaXMucXVlcnlQYXJhbXMucmlza1R5cGUsDQogICAgICAgIGRldmljZUNvbmZpZ0lkOiB0aGlzLnF1ZXJ5UGFyYW1zLmRldmljZUNvbmZpZ0lkDQogICAgICB9KQ0KDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdDQogICAgICB0aGlzLnJlc2V0Rm9ybSgncXVlcnlGb3JtJykNCiAgICAgIC8vIOaJi+WKqOmHjee9ruaJgOacieafpeivouWtl+autQ0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5yaXNrQXNzZXRzID0gdW5kZWZpbmVkDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnJpc2tUeXBlID0gdW5kZWZpbmVkDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmhhbmRsZVN0YXRlID0gMA0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kZXZpY2VDb25maWdJZCA9IHVuZGVmaW5lZA0KICAgICAgdGhpcy5zZXREZWZhdWx0RGF0ZVJhbmdlKCkNCiAgICAgIC8vIOmAmuefpeeItue7hOS7tumHjee9ruaMiemSrumAieS4reeKtuaAgQ0KICAgICAgdGhpcy4kZW1pdCgncmVzZXQtYnV0dG9uJykNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5aSE572u5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGlzcG9zZShyb3cpIHsNCiAgICAgIHRoaXMuZGlzcG9zZUZvcm0gPSB7DQogICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgIGhhbmRsZVN0YXRlOiByb3cuaGFuZGxlU3RhdGUgPT09IDIgPyByb3cuaGFuZGxlU3RhdGUgOiB1bmRlZmluZWQsDQogICAgICAgIGhhbmRsZURlc2M6IHJvdy5oYW5kbGVTdGF0ZSA9PT0gMiA/IChyb3cuaGFuZGxlRGVzYyB8fCAnJykgOiAnJw0KICAgICAgfQ0KICAgICAgdGhpcy5kaXNwb3NlT3BlbiA9IHRydWUNCiAgICAgIC8vIOa4hemZpOihqOWNlemqjOivgeeKtuaAgQ0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBpZiAodGhpcy4kcmVmcy5kaXNwb3NlRm9ybSkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuZGlzcG9zZUZvcm0uY2xlYXJWYWxpZGF0ZSgpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5aSE572uICovDQogICAgc3VibWl0RGlzcG9zZSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbJ2Rpc3Bvc2VGb3JtJ10udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBoYW5kbGVBbGFybSh7DQogICAgICAgICAgICBpZDogdGhpcy5kaXNwb3NlRm9ybS5pZCwNCiAgICAgICAgICAgIGhhbmRsZVN0YXRlOiB0aGlzLmRpc3Bvc2VGb3JtLmhhbmRsZVN0YXRlLA0KICAgICAgICAgICAgaGFuZGxlRGVzYzogdGhpcy5kaXNwb3NlRm9ybS5oYW5kbGVEZXNjDQogICAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCflpITnva7miJDlip8nKQ0KICAgICAgICAgICAgdGhpcy5kaXNwb3NlT3BlbiA9IGZhbHNlDQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5om56YeP5aSE572u5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQmF0Y2hEaXNwb3NlKCkgew0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign6K+36Iez5bCR6YCJ5oup5LiA5p2h6K6w5b2VJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmJhdGNoRGlzcG9zZUZvcm0gPSB7DQogICAgICAgIGV2ZW50SWRzOiB0aGlzLmlkcywNCiAgICAgICAgaGFuZGxlU3RhdGU6IDEsDQogICAgICAgIGhhbmRsZURlc2M6ICcnDQogICAgICB9DQogICAgICB0aGlzLmJhdGNoRGlzcG9zZU9wZW4gPSB0cnVlDQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5om56YeP5aSE572uICovDQogICAgc3VibWl0QmF0Y2hEaXNwb3NlKCkgew0KICAgICAgdGhpcy4kcmVmc1snYmF0Y2hEaXNwb3NlRm9ybSddLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgY29uc3QgZXZlbnRJZHMgPSB0aGlzLmJhdGNoRGlzcG9zZUZvcm0uZXZlbnRJZHMNCiAgICAgICAgICBjb25zdCBoYW5kbGVTdGF0ZSA9IHRoaXMuYmF0Y2hEaXNwb3NlRm9ybS5oYW5kbGVTdGF0ZQ0KICAgICAgICAgIGNvbnN0IGhhbmRsZURlc2MgPSB0aGlzLmJhdGNoRGlzcG9zZUZvcm0uaGFuZGxlRGVzYw0KDQogICAgICAgICAgYmF0Y2hIYW5kbGVBbGFybXMoew0KICAgICAgICAgICAgZXZlbnRJZHM6IGV2ZW50SWRzLA0KICAgICAgICAgICAgaGFuZGxlU3RhdGU6IGhhbmRsZVN0YXRlLA0KICAgICAgICAgICAgaGFuZGxlRGVzYzogaGFuZGxlRGVzYw0KICAgICAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5om56YeP5aSE572u5oiQ5YqfJykNCiAgICAgICAgICAgIHRoaXMuYmF0Y2hEaXNwb3NlT3BlbiA9IGZhbHNlDQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgNCiAgICAgICAgJ2Zmc2FmZS9mbG93Umlza0Fzc2V0cy9leHBvcnQnLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgICB9LA0KICAgICAgICAnQVBJ5ZGK6K2m5pWw5o2uLnhsc3gnDQogICAgICApDQogICAgfSwNCiAgICAvKiog6K+m5oOF5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGV0YWlsKHJvdykgew0KICAgICAgdGhpcy5kZXRhaWxEYXRhID0geyAuLi5yb3cgfQ0KICAgICAgdGhpcy5kZXRhaWxPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOivpUFQSeWRiuitpuiusOW9le+8nycpLnRoZW4oKCkgPT4gew0KICAgICAgICByZXR1cm4gdGhpcy5kZWxldGVGbG93Umlza0Fzc2V0cyhyb3cuaWQpDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQ0KICAgIH0sDQogICAgLyoqIOWIoOmZpEFQSeWRiuitpuiusOW9lSAqLw0KICAgIGRlbGV0ZUZsb3dSaXNrQXNzZXRzKGlkKSB7DQogICAgICByZXR1cm4gZGVsRmxvd1Jpc2tBc3NldHMoaWQpDQogICAgfSwNCiAgICAvKiog6I635Y+W6aOO6Zmp57G75Yir5qCH562+ICovDQogICAgZ2V0Umlza1R5cGVMYWJlbChyaXNrVHlwZSkgew0KICAgICAgY29uc3QgZGljdCA9IHRoaXMuZGljdC50eXBlLmZsb3dfcmlza190eXBlLmZpbmQoZCA9PiBkLnZhbHVlID09PSByaXNrVHlwZSkNCiAgICAgIHJldHVybiBkaWN0ID8gZGljdC5sYWJlbCA6IHJpc2tUeXBlDQogICAgfSwNCiAgICAvKiog6I635Y+W5aSE572u54q25oCB5qCH562+ICovDQogICAgZ2V0SGFuZGxlU3RhdGVMYWJlbChoYW5kbGVTdGF0ZSkgew0KICAgICAgY29uc3Qgb3B0aW9uID0gdGhpcy5oYW5kbGVTdGF0ZU9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0udmFsdWUgPT09IGhhbmRsZVN0YXRlKQ0KICAgICAgcmV0dXJuIG9wdGlvbiA/IG9wdGlvbi5sYWJlbCA6ICfmnKrnn6UnDQogICAgfSwNCiAgICBoYW5kbGVTdGF0ZUZvcm1hdHRlcihyb3csIGNvbHVtbiwgY2VsbFZhbHVlLCBpbmRleCkgew0KICAgICAgbGV0IG5hbWUgPSAn5pyq5aSE572uJw0KICAgICAgY29uc3QgbWF0Y2ggPSB0aGlzLmhhbmRsZVN0YXRlT3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS52YWx1ZSA9PT0gY2VsbFZhbHVlKQ0KICAgICAgaWYgKG1hdGNoKSB7DQogICAgICAgIG5hbWUgPSBtYXRjaC5sYWJlbA0KICAgICAgfQ0KICAgICAgcmV0dXJuIG5hbWUNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["apiAlarmList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyQA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "apiAlarmList.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\r\n  <div class=\"custom-container\">\r\n    <div class=\"custom-content-container-right\">\r\n      <div class=\"custom-content-search-box\">\r\n        <el-form\r\n          ref=\"queryForm\"\r\n          :model=\"queryParams\"\r\n          size=\"small\"\r\n          label-position=\"right\"\r\n          label-width=\"70px\"\r\n          :inline=\"true\"\r\n        >\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"告警时间\" prop=\"beginTime\">\r\n                <el-date-picker\r\n                  v-model=\"dateRange\"\r\n                  type=\"datetimerange\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"开始日期\"\r\n                  end-placeholder=\"结束日期\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  @change=\"handleDateRangeChange\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"风险资产\" prop=\"riskAssets\">\r\n                <el-input\r\n                  v-model=\"queryParams.riskAssets\"\r\n                  placeholder=\"请输入风险资产\"\r\n                  clearable\r\n                  @keyup.enter.native=\"handleQuery\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"风险类别\" prop=\"riskType\">\r\n                <el-select v-model=\"queryParams.riskType\" placeholder=\"请选择风险类别\" clearable>\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.flow_risk_type\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item class=\"custom-search-btn\">\r\n                <el-button\r\n                  class=\"btn1\"\r\n                  size=\"small\"\r\n                  @click=\"handleQuery\"\r\n                >查询</el-button>\r\n                <el-button\r\n                  class=\"btn2\"\r\n                  size=\"small\"\r\n                  @click=\"resetQuery\"\r\n                >重置</el-button>\r\n                <el-button\r\n                  v-if=\"!showSearch\"\r\n                  class=\"btn2\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-arrow-down\"\r\n                  @click=\"showSearch = true\"\r\n                >展开</el-button>\r\n                <el-button\r\n                  v-else\r\n                  class=\"btn2\"\r\n                  size=\"small\"\r\n                  icon=\"el-icon-arrow-up\"\r\n                  @click=\"showSearch = false\"\r\n                >收起</el-button>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row v-if=\"showSearch\" :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"处置状态\" prop=\"handleState\">\r\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\" clearable>\r\n                  <el-option\r\n                    v-for=\"dict in handleStateOptions\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"所属探针\">\r\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\r\n                  <el-option\r\n                    v-for=\"item in deviceConfigList\"\r\n                    :key=\"item.id\"\r\n                    :label=\"item.deviceName\"\r\n                    :value=\"item.id\">\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n      </div>\r\n      <div class=\"custom-content-container\">\r\n        <div class=\"common-header\">\r\n          <div><span class=\"common-head-title\">告警列表</span></div>\r\n          <div class=\"common-head-right\">\r\n            <el-row :gutter=\"10\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  v-hasPermi=\"['ffsafe:flowRiskAssets:export']\"\r\n                  class=\"btn1\"\r\n                  size=\"small\"\r\n                  @click=\"handleExport\"\r\n                >导出</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </div>\r\n        </div>\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          height=\"100%\"\r\n          :data=\"apiAlarmList\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column label=\"告警更新时间\" align=\"left\" prop=\"updateTime\" width=\"160\" sortable>\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"风险资产\" align=\"left\" prop=\"riskAssets\" />\r\n          <el-table-column label=\"风险类别\" align=\"left\" prop=\"riskType\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag :options=\"dict.type.flow_risk_type\" :value=\"scope.row.riskType\" />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"风险信息\" align=\"left\" prop=\"riskInfo\" />\r\n          <el-table-column label=\"处置状态\" align=\"center\" prop=\"handleState\" width=\"100\" :formatter=\"handleStateFormatter\" />\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            class-name=\"small-padding fixed-width\"\r\n            width=\"200\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <!--   <el-button\r\n                v-hasPermi=\"['ffsafe:flowRiskAssets:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleDetail(scope.row)\"\r\n              >详情</el-button> -->\r\n              <el-button\r\n                v-hasPermi=\"['ffsafe:flowRiskAssets:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                class=\"JNPF-table-delBtn\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除</el-button>\r\n              <el-button\r\n                v-if=\"scope.row.handleState !== 1\"\r\n                v-hasPermi=\"['ffsafe:flowRiskAssets:handle']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleDispose(scope.row)\"\r\n              >处置</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 处置对话框 -->\r\n    <el-dialog title=\"快速处置\" :visible.sync=\"disposeOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"disposeForm\" :model=\"disposeForm\" :rules=\"disposeRules\" label-width=\"80px\">\r\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\r\n          <el-select v-model=\"disposeForm.handleState\" placeholder=\"请选择处置状态\" clearable>\r\n            <el-option\r\n              v-for=\"dict in handleStateOption\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"处置备注\" prop=\"handleDesc\">\r\n          <el-input v-model=\"disposeForm.handleDesc\" type=\"textarea\" placeholder=\"请输入处置备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitDispose\">确 定</el-button>\r\n        <el-button @click=\"disposeOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量处置对话框 -->\r\n    <el-dialog title=\"批量处置\" :visible.sync=\"batchDisposeOpen\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"batchDisposeForm\" :model=\"batchDisposeForm\" label-width=\"80px\">\r\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\r\n          <el-select v-model=\"batchDisposeForm.handleState\" placeholder=\"请选择处置状态\">\r\n            <el-option\r\n              v-for=\"dict in handleStateOptions\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"处置备注\" prop=\"handleDesc\">\r\n          <el-input v-model=\"batchDisposeForm.handleDesc\" type=\"textarea\" placeholder=\"请输入处置备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitBatchDispose\">确 定</el-button>\r\n        <el-button @click=\"batchDisposeOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog title=\"API告警详情\" :visible.sync=\"detailOpen\" width=\"600px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"告警更新时间\">\r\n          {{ parseTime(detailData.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"风险资产\">\r\n          {{ detailData.riskAssets }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"风险类别\">\r\n          {{ getRiskTypeLabel(detailData.riskType) }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"引擎名称\">\r\n          {{ detailData.engineName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"处置状态\">\r\n          {{ getHandleStateLabel(detailData.handleState) }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"处置人\">\r\n          {{ detailData.disposerName || '' }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"风险信息\" :span=\"2\">\r\n          {{ detailData.riskInfo }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item v-if=\"detailData.handleDesc\" label=\"处置描述\" :span=\"2\">\r\n          {{ detailData.handleDesc }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listFlowRiskAssets, handleAlarm, batchHandleAlarms, delFlowRiskAssets } from '@/api/ffsafe/flowRiskAssets'\r\nimport {listDeviceConfig} from \"@/api/ffsafe/deviceConfig\";\r\n\r\nexport default {\r\n  name: 'ApiAlarmList',\r\n  dicts: ['flow_risk_type'],\r\n  props: {\r\n    propsActiveName: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    propsQueryParams: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      deviceConfigList: [],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // API告警列表\r\n      apiAlarmList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示处置弹出层\r\n      disposeOpen: false,\r\n      // 是否显示批量处置弹出层\r\n      batchDisposeOpen: false,\r\n      // 是否显示详情弹出层\r\n      detailOpen: false,\r\n      // 详情数据\r\n      detailData: {},\r\n      // 处置表单\r\n      disposeForm: {\r\n        id: null,\r\n        handleState: undefined,\r\n        handleDesc: ''\r\n      },\r\n      // 处置表单验证规则\r\n      disposeRules: {\r\n        handleState: [\r\n          { required: true, message: '请选择处置状态', trigger: 'change' }\r\n        ]\r\n      },\r\n      // 批量处置表单\r\n      batchDisposeForm: {\r\n        eventIds: [],\r\n        handleState: '',\r\n        handleDesc: ''\r\n      },\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        riskAssets: undefined,\r\n        riskType: undefined,\r\n        handleState: 0,\r\n        params: {\r\n          beginTime: undefined,\r\n          endTime: undefined\r\n        }\r\n      },\r\n      // 处置状态字典\r\n      handleStateOptions: [\r\n        { value: 0, label: '未处置' },\r\n        { value: 1, label: '已处置' },\r\n        { value: 2, label: '忽略' },\r\n        { value: 3, label: '处置中' }\r\n      ],\r\n      handleStateOption: [\r\n        {\r\n          label: '已处置',\r\n          value: 1\r\n        },\r\n        {\r\n          label: '忽略',\r\n          value: 2\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  watch: {\r\n    propsActiveName: {\r\n      handler(newVal) {\r\n        if (newVal === 'apiAlarm') {\r\n          // 确保时间参数已设置，避免无时间参数的查询\r\n          if (!this.queryParams.params.beginTime || !this.queryParams.params.endTime) {\r\n            this.setDefaultDateRange()\r\n          }\r\n          this.getList()\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    propsQueryParams: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          // 保留已设置的时间范围参数，避免被空的params覆盖\r\n          const originalBeginTime = this.queryParams.params.beginTime\r\n          const originalEndTime = this.queryParams.params.endTime\r\n\r\n          this.queryParams = { ...this.queryParams, ...newVal }\r\n\r\n          // 如果新的查询参数没有时间范围，则恢复原有的时间范围\r\n          if (!newVal.params || (!newVal.params.beginTime && !newVal.params.endTime)) {\r\n            if (originalBeginTime && originalEndTime) {\r\n              this.queryParams.params.beginTime = originalBeginTime\r\n              this.queryParams.params.endTime = originalEndTime\r\n            }\r\n          }\r\n\r\n          // 只有当前标签是apiAlarm时才触发查询，避免重复查询\r\n          if (this.propsActiveName === 'apiAlarm') {\r\n            this.getList()\r\n          }\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    // 设置默认查询日期范围为最近7天\r\n    this.setDefaultDateRange()\r\n    // 不在created中调用getList，由watch处理\r\n    this.getDeviceConfigList();\r\n  },\r\n  methods: {\r\n    getDeviceConfigList(){\r\n      listDeviceConfig({queryAllData: true}).then(res => {\r\n        this.deviceConfigList = res.rows;\r\n      })\r\n    },\r\n    /** 设置默认日期范围 */\r\n    setDefaultDateRange() {\r\n      const end = new Date()\r\n      const start = new Date()\r\n      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\r\n\r\n      // 确保开始时间为 00:00:00，结束时间为 23:59:59\r\n      start.setHours(0, 0, 0, 0)\r\n      end.setHours(23, 59, 59, 999)\r\n\r\n      this.dateRange = [\r\n        this.parseTime(start, '{y}-{m}-{d} {h}:{i}:{s}'),\r\n        this.parseTime(end, '{y}-{m}-{d} {h}:{i}:{s}')\r\n      ]\r\n      this.queryParams.params.beginTime = this.dateRange[0]\r\n      this.queryParams.params.endTime = this.dateRange[1]\r\n    },\r\n    /** 查询API告警列表 */\r\n    getList() {\r\n      this.loading = true\r\n\r\n      // 通知父组件同步查询条件和按钮选中状态\r\n      this.$emit('query-change', {\r\n        riskType: this.queryParams.riskType,\r\n        deviceConfigId: this.queryParams.deviceConfigId\r\n      })\r\n\r\n      // 同步请求类型统计数据\r\n      this.$emit('getList', { ...this.queryParams })\r\n      listFlowRiskAssets(this.queryParams).then(response => {\r\n        this.apiAlarmList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 日期范围发生变化\r\n    handleDateRangeChange(val) {\r\n      if (val && val.length === 2) {\r\n        // 确保开始时间为 00:00:00，结束时间为 23:59:59\r\n        const startDate = new Date(val[0])\r\n        const endDate = new Date(val[1])\r\n\r\n        startDate.setHours(0, 0, 0, 0)\r\n        endDate.setHours(23, 59, 59, 999)\r\n\r\n        this.queryParams.params.beginTime = this.parseTime(startDate, '{y}-{m}-{d} {h}:{i}:{s}')\r\n        this.queryParams.params.endTime = this.parseTime(endDate, '{y}-{m}-{d} {h}:{i}:{s}')\r\n\r\n        // 更新dateRange显示值\r\n        this.dateRange = [\r\n          this.queryParams.params.beginTime,\r\n          this.queryParams.params.endTime\r\n        ]\r\n      } else {\r\n        this.queryParams.params.beginTime = undefined\r\n        this.queryParams.params.endTime = undefined\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        handleState: '',\r\n        handleDesc: ''\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      // 如果时间范围为空，设置默认时间范围\r\n      if (!this.queryParams.params.beginTime || !this.queryParams.params.endTime) {\r\n        this.setDefaultDateRange()\r\n      }\r\n      this.queryParams.pageNum = 1\r\n\r\n      // 通知父组件同步查询条件和按钮选中状态\r\n      this.$emit('query-change', {\r\n        riskType: this.queryParams.riskType,\r\n        deviceConfigId: this.queryParams.deviceConfigId\r\n      })\r\n\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.resetForm('queryForm')\r\n      // 手动重置所有查询字段\r\n      this.queryParams.riskAssets = undefined\r\n      this.queryParams.riskType = undefined\r\n      this.queryParams.handleState = 0\r\n      this.queryParams.deviceConfigId = undefined\r\n      this.setDefaultDateRange()\r\n      // 通知父组件重置按钮选中状态\r\n      this.$emit('reset-button')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 处置按钮操作 */\r\n    handleDispose(row) {\r\n      this.disposeForm = {\r\n        id: row.id,\r\n        handleState: row.handleState === 2 ? row.handleState : undefined,\r\n        handleDesc: row.handleState === 2 ? (row.handleDesc || '') : ''\r\n      }\r\n      this.disposeOpen = true\r\n      // 清除表单验证状态\r\n      this.$nextTick(() => {\r\n        if (this.$refs.disposeForm) {\r\n          this.$refs.disposeForm.clearValidate()\r\n        }\r\n      })\r\n    },\r\n    /** 提交处置 */\r\n    submitDispose() {\r\n      this.$refs['disposeForm'].validate(valid => {\r\n        if (valid) {\r\n          handleAlarm({\r\n            id: this.disposeForm.id,\r\n            handleState: this.disposeForm.handleState,\r\n            handleDesc: this.disposeForm.handleDesc\r\n          }).then(response => {\r\n            this.$modal.msgSuccess('处置成功')\r\n            this.disposeOpen = false\r\n            this.getList()\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 批量处置按钮操作 */\r\n    handleBatchDispose() {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError('请至少选择一条记录')\r\n        return\r\n      }\r\n      this.batchDisposeForm = {\r\n        eventIds: this.ids,\r\n        handleState: 1,\r\n        handleDesc: ''\r\n      }\r\n      this.batchDisposeOpen = true\r\n    },\r\n    /** 提交批量处置 */\r\n    submitBatchDispose() {\r\n      this.$refs['batchDisposeForm'].validate(valid => {\r\n        if (valid) {\r\n          const eventIds = this.batchDisposeForm.eventIds\r\n          const handleState = this.batchDisposeForm.handleState\r\n          const handleDesc = this.batchDisposeForm.handleDesc\r\n\r\n          batchHandleAlarms({\r\n            eventIds: eventIds,\r\n            handleState: handleState,\r\n            handleDesc: handleDesc\r\n          }).then(response => {\r\n            this.$modal.msgSuccess('批量处置成功')\r\n            this.batchDisposeOpen = false\r\n            this.getList()\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        'ffsafe/flowRiskAssets/export',\r\n        {\r\n          ...this.queryParams\r\n        },\r\n        'API告警数据.xlsx'\r\n      )\r\n    },\r\n    /** 详情按钮操作 */\r\n    handleDetail(row) {\r\n      this.detailData = { ...row }\r\n      this.detailOpen = true\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除该API告警记录？').then(() => {\r\n        return this.deleteFlowRiskAssets(row.id)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    /** 删除API告警记录 */\r\n    deleteFlowRiskAssets(id) {\r\n      return delFlowRiskAssets(id)\r\n    },\r\n    /** 获取风险类别标签 */\r\n    getRiskTypeLabel(riskType) {\r\n      const dict = this.dict.type.flow_risk_type.find(d => d.value === riskType)\r\n      return dict ? dict.label : riskType\r\n    },\r\n    /** 获取处置状态标签 */\r\n    getHandleStateLabel(handleState) {\r\n      const option = this.handleStateOptions.find(item => item.value === handleState)\r\n      return option ? option.label : '未知'\r\n    },\r\n    handleStateFormatter(row, column, cellValue, index) {\r\n      let name = '未处置'\r\n      const match = this.handleStateOptions.find(item => item.value === cellValue)\r\n      if (match) {\r\n        name = match.label\r\n      }\r\n      return name\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n</style>\r\n"]}]}