package com.ruoyi.ffsafe.scantaskapi.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
public class FfsafeHostscanVulnResult extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    private Long id;

    /** 任务id */
    @Excel(name = "任务id")
    private Integer taskId;

    /** ip地址 */
    @Excel(name = "ip地址")
    private String hostIp;

    /** 端口号 */
    @Excel(name = "端口号")
    private Integer hostPort;

    /** 版本信息 */
    @Excel(name = "版本信息")
    private String versionInfo;

    /** 漏洞id */
    @Excel(name = "漏洞id")
    private Integer vulnId;

    /** 漏洞名称 */
    @Excel(name = "漏洞名称")
    private String vulnName;

    /** 风险级别: 1:低危,2:中危,3:高危 */
    @Excel(name = "风险级别: 1:低危,2:中危,3:高危, 4:严重")
    private Integer riskLevel;

    /** cve编号 */
    @Excel(name = "cve编号")
    private String cveNumber;

    /** CNNVD编号 */
    @Excel(name = "CNNVD编号")
    private String cnnvdNumber;

    /** 漏洞类型 */
    @Excel(name = "漏洞类型")
    private String vulnType;

    /** 漏洞发布日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "漏洞发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date publishDate;

    /** 漏洞介绍 */
    @Excel(name = "漏洞介绍")
    private String vulnInfo;

    /** 漏洞修复建议 */
    @Excel(name = "漏洞修复建议")
    private String vulnSolve;

    /** 利用证明(不存在为空， 存在base64编码) */
    @Excel(name = "利用证明(不存在为空， 存在base64编码)")
    private String evidence;

    /** cpe列表 */
    @Excel(name = "cpe列表")
    private String cpe;

    /** 汇总记录ID */
    @Excel(name = "汇总记录ID")
    private Long summaryId;

}
