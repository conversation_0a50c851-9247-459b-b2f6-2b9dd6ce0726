package com.ruoyi.monitor2.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.monitor2.domain.MonitorBssHostResult;
import com.ruoyi.monitor2.mapper.MonitorBssHostResultMapper;
import com.ruoyi.monitor2.service.IMonitorBssHostResultService;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MonitorBssHostResultServiceImpl implements IMonitorBssHostResultService {
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    private MonitorBssHostResultMapper monitorBssHostResultMapper;

    /**
     * 查询基础扫描主机结果
     *
     * @param id 基础扫描主机结果主键
     * @return 基础扫描主机结果
     */
    @Override
    public MonitorBssHostResult selectMonitorBssHostResultById(Long id)
    {
        return monitorBssHostResultMapper.selectMonitorBssHostResultById(id);
    }

    /**
     * 批量查询基础扫描主机结果
     *
     * @param ids 基础扫描主机结果主键集合
     * @return 基础扫描主机结果集合
     */
    @Override
    public List<MonitorBssHostResult> selectMonitorBssHostResultByIds(Long[] ids)
    {
        return monitorBssHostResultMapper.selectMonitorBssHostResultByIds(ids);
    }

    /**
     * 查询基础扫描主机结果列表
     *
     * @param monitorBssHostResult 基础扫描主机结果
     * @return 基础扫描主机结果
     */
    @Override
    public List<MonitorBssHostResult> selectMonitorBssHostResultList(MonitorBssHostResult monitorBssHostResult)
    {
        return monitorBssHostResultMapper.selectMonitorBssHostResultList(monitorBssHostResult);
    }

    /**
     * 新增基础扫描主机结果
     *
     * @param monitorBssHostResult 基础扫描主机结果
     * @return 结果
     */
    @Override
    public int insertMonitorBssHostResult(MonitorBssHostResult monitorBssHostResult)
    {
        monitorBssHostResult.setCreateTime(DateUtils.getNowDate());
        return monitorBssHostResultMapper.insertMonitorBssHostResult(monitorBssHostResult);
    }

    @Override
    public int insertMonitorBssHostResultList(List<MonitorBssHostResult> monitorBssHostResultList)
    {
        int ret = 0;
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
        try {
            MonitorBssHostResultMapper bssHostResultMapper = sqlSession.getMapper(MonitorBssHostResultMapper.class);
            AtomicInteger count = new AtomicInteger(0);
            monitorBssHostResultList.forEach(bssHostResult -> {
                bssHostResultMapper.insertMonitorBssHostResult(bssHostResult);
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            });
            // 提交数据
            sqlSession.commit();
            //sqlSession.rollback();
            ret = monitorBssHostResultList.size();
        } catch (Exception e) {
            sqlSession.rollback();
            throw e;
        } finally {
            sqlSession.close();
        }
        return ret;
    }

    /**
     * 修改基础扫描主机结果
     *
     * @param monitorBssHostResult 基础扫描主机结果
     * @return 结果
     */
    @Override
    public int updateMonitorBssHostResult(MonitorBssHostResult monitorBssHostResult)
    {
        monitorBssHostResult.setUpdateTime(DateUtils.getNowDate());
        return monitorBssHostResultMapper.updateMonitorBssHostResult(monitorBssHostResult);
    }

    /**
     * 删除基础扫描主机结果信息
     *
     * @param id 基础扫描主机结果主键
     * @return 结果
     */
    @Override
    public int deleteMonitorBssHostResultById(Long id)
    {
        return monitorBssHostResultMapper.deleteMonitorBssHostResultById(id);
    }

    /**
     * 批量删除基础扫描主机结果
     *
     * @param ids 需要删除的基础扫描主机结果主键
     * @return 结果
     */
    @Override
    public int deleteMonitorBssHostResultByIds(Long[] ids)
    {
        return monitorBssHostResultMapper.deleteMonitorBssHostResultByIds(ids);
    }

    /**
     * 查找漏洞扫描主机信息列表
     * @param monitorBssHostResult
     * @return
     */
    @Override
    public List<MonitorBssHostResult> findServerPage(MonitorBssHostResult monitorBssHostResult) {
        return monitorBssHostResultMapper.findServerPage(monitorBssHostResult);
    }
}
