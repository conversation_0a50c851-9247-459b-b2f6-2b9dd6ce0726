<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeScanReportTaskRelationMapper">

    <resultMap type="FfsafeScanReportTaskRelation" id="FfsafeScanReportTaskRelationResult">
        <result property="id"    column="id"    />
        <result property="scanReportRecordId"    column="scan_report_record_id"    />
        <result property="taskSummaryId"    column="task_summary_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectFfsafeScanReportTaskRelationVo">
        select id, scan_report_record_id, task_summary_id, create_time from ffsafe_scan_report_task_relation
    </sql>

    <select id="selectFfsafeScanReportTaskRelationList" parameterType="FfsafeScanReportTaskRelation" resultMap="FfsafeScanReportTaskRelationResult">
        <include refid="selectFfsafeScanReportTaskRelationVo"/>
        <where>
            <if test="scanReportRecordId != null "> and scan_report_record_id = #{scanReportRecordId}</if>
            <if test="taskSummaryId != null "> and task_summary_id = #{taskSummaryId}</if>
        </where>
    </select>

    <select id="selectFfsafeScanReportTaskRelationById" parameterType="Long" resultMap="FfsafeScanReportTaskRelationResult">
        <include refid="selectFfsafeScanReportTaskRelationVo"/>
        where id = #{id}
    </select>

    <select id="selectTaskSummaryIdsByReportRecordId" parameterType="Long" resultType="Integer">
        select task_summary_id from ffsafe_scan_report_task_relation
        where scan_report_record_id = #{reportRecordId}
        order by task_summary_id
    </select>

    <select id="selectReportRecordIdsByTaskSummaryIds" resultType="Long">
        select distinct scan_report_record_id from ffsafe_scan_report_task_relation
        where task_summary_id in
        <foreach item="taskSummaryId" collection="taskSummaryIds" open="(" separator="," close=")">
            #{taskSummaryId}
        </foreach>
        order by scan_report_record_id
    </select>

    <select id="selectByReportRecordId" parameterType="Long" resultMap="FfsafeScanReportTaskRelationResult">
        <include refid="selectFfsafeScanReportTaskRelationVo"/>
        where scan_report_record_id = #{reportRecordId}
        order by task_summary_id
    </select>

    <select id="selectByTaskSummaryIds" resultMap="FfsafeScanReportTaskRelationResult">
        <include refid="selectFfsafeScanReportTaskRelationVo"/>
        where task_summary_id in
        <foreach item="taskSummaryId" collection="taskSummaryIds" open="(" separator="," close=")">
            #{taskSummaryId}
        </foreach>
        order by scan_report_record_id, task_summary_id
    </select>

    <select id="checkExistingReports" resultType="Integer">
        select distinct rel.task_summary_id
        from ffsafe_scan_report_task_relation rel
        inner join ffsafe_scan_report_record r on rel.scan_report_record_id = r.id
        where rel.task_summary_id in
        <foreach item="taskSummaryId" collection="taskSummaryIds" open="(" separator="," close=")">
            #{taskSummaryId}
        </foreach>
        and r.generate_source = 1
    </select>

    <insert id="insertFfsafeScanReportTaskRelation" parameterType="FfsafeScanReportTaskRelation" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_scan_report_task_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scanReportRecordId != null">scan_report_record_id,</if>
            <if test="taskSummaryId != null">task_summary_id,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scanReportRecordId != null">#{scanReportRecordId},</if>
            <if test="taskSummaryId != null">#{taskSummaryId},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_scan_report_task_relation (scan_report_record_id, task_summary_id, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.scanReportRecordId}, #{item.taskSummaryId}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateFfsafeScanReportTaskRelation" parameterType="FfsafeScanReportTaskRelation">
        update ffsafe_scan_report_task_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="scanReportRecordId != null">scan_report_record_id = #{scanReportRecordId},</if>
            <if test="taskSummaryId != null">task_summary_id = #{taskSummaryId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeScanReportTaskRelationById" parameterType="Long">
        delete from ffsafe_scan_report_task_relation where id = #{id}
    </delete>

    <delete id="deleteFfsafeScanReportTaskRelationByIds" parameterType="String">
        delete from ffsafe_scan_report_task_relation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByReportRecordId" parameterType="Long">
        delete from ffsafe_scan_report_task_relation where scan_report_record_id = #{reportRecordId}
    </delete>

    <delete id="deleteByTaskSummaryId" parameterType="Integer">
        delete from ffsafe_scan_report_task_relation where task_summary_id = #{taskSummaryId}
    </delete>

</mapper>
