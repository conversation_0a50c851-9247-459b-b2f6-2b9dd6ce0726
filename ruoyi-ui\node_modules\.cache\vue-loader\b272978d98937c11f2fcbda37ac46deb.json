{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\threat\\overview\\component\\PointsOverview.vue?vue&type=template&id=18ad52a9&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\threat\\overview\\component\\PointsOverview.vue", "mtime": 1755762306448}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}