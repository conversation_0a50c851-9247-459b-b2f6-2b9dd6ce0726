#!/bin/bash

# 测试脚本：验证镜像计数修复

echo "=== 测试镜像计数修复 ==="

# 创建测试文件（没有换行符结尾）
echo -n "aqsoc-server:2.5.1" > test_no_newline.txt

# 创建测试文件（有换行符结尾）
echo "aqsoc-server:2.5.1" > test_with_newline.txt

REGISTRY="192.168.200.12:5000"
TEMP_LIST=$(mktemp)

echo "1. 测试原始方法（有bug的版本）："

# 处理没有换行符的文件
sed "s#^#$REGISTRY/#" test_no_newline.txt > $TEMP_LIST

echo "生成的临时文件内容："
cat $TEMP_LIST
echo ""

# 原始计数方法（有bug）
total_images_old=0
while read image; do
    total_images_old=$((total_images_old + 1))
done < $TEMP_LIST

echo "原始方法计数结果: $total_images_old 个镜像"

echo ""
echo "2. 测试修复方法："

# 修复的计数方法
total_images_new=0
while read image || [[ -n $image ]]; do
    [[ -n $image ]] && total_images_new=$((total_images_new + 1))
done < $TEMP_LIST

echo "修复方法计数结果: $total_images_new 个镜像"

echo ""
echo "3. 测试有换行符的文件："

# 处理有换行符的文件
sed "s#^#$REGISTRY/#" test_with_newline.txt > $TEMP_LIST

total_images_with_newline=0
while read image || [[ -n $image ]]; do
    [[ -n $image ]] && total_images_with_newline=$((total_images_with_newline + 1))
done < $TEMP_LIST

echo "有换行符文件计数结果: $total_images_with_newline 个镜像"

# 清理
rm -f test_no_newline.txt test_with_newline.txt $TEMP_LIST

echo ""
echo "=== 测试完成 ==="
echo "总结："
echo "- 原始方法（有bug）: $total_images_old 个镜像"
echo "- 修复方法: $total_images_new 个镜像"
echo "- 有换行符文件: $total_images_with_newline 个镜像"

if [ $total_images_new -eq 1 ] && [ $total_images_with_newline -eq 1 ]; then
    echo "✅ 修复成功！"
else
    echo "❌ 修复失败！"
fi
