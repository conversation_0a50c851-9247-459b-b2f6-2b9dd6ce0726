package com.ruoyi.monitor2.task;

import com.alibaba.fastjson2.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.CollectionUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.monitor2.domain.MonitorPort;
import com.ruoyi.safe.domain.MonitorAsset;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

public class NmapTool {
    private static Logger log = LoggerFactory.getLogger(NmapTool.class);

    //获取CPU核数
    private static int CPU_NUMS = Runtime.getRuntime().availableProcessors();

    /**
     * 线程池核心池的大小
     */
    private static int CORE_POOL_SIZE = 5;

    /**
     * 线程池的最大线程数
     */
    private static int MAXMUM_POOL_SIZE = CPU_NUMS * 10;
    //阻塞队列容量
    private static int QUEUE_CAPACITY = 20;

    //活跃时间
    private static int KEPP_ALIVE_TIME_SECOND = 300;

    public static ExecutorService THREAD_POOL = null;

    private static final String NMAP_SCAN_DIRECT = "nmap -sV -O --osscan-guess -p ";

    private static final String NMAP_SCAN_OFFLINE_DIRECT = "nmap -Pn -O -p ";

    private static final HashMap<String, Process> PROCESS_MAP = new HashMap<>();

    private static RedisCache redisCache = SpringUtils.getBean(RedisCache.class);

    private static final String DEFAULT_PORT = "7,9,13,21-23,25-26,37,53,79-81,88,106,110-111,113,119,135,139,143-144,179,199,389,427,443-445,465,513-515,543-544,548,554,587,631,646,873,888,990,993,995,1025-1029,1080,1110,1433,1443,1720,1723,1755,1900,2000-2001,2049,2121,2181,2717,3000,3128,3306,3389,3986,4899,5000,5009,5051,5060,5101,5190,5357,5432,5631,5666,5800,5900,6000-6001,6646,7000-7005,7070,8000,8008-8009,8080-8081,8443,8888,9100,9999-10000,11211,32768,49152-49157 ";

    static {
        //建立10个核心线程，线程请求个数超过20，则进入队列等待
        THREAD_POOL = new ThreadPoolExecutor(CORE_POOL_SIZE, MAXMUM_POOL_SIZE, KEPP_ALIVE_TIME_SECOND, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<Runnable>(QUEUE_CAPACITY), new ThreadFactoryBuilder().setNameFormat("PROS-%d").build());
    }

    /***
     *
     * 获取操做系统类型
     * @return 1: window 2: 非window
     */
    public static String getOsType() {
        String osName = System.getProperty("os.name");
        String os_type = "";
        if (osName == null) {
            os_type = "";
        }
        if (osName.toLowerCase().indexOf("win") != -1) {
            os_type = Constants.ONE;
        } else {
            os_type = "2";
        }
        return os_type;
    }

    public static String getCmdPrex() {
        String tp = getOsType();
        if (tp.equals(Constants.ONE)) {
            return "cmd /c";
        }
        return "sh -c"; // /bin/sh -c
    }

    /**
     * 通过nmap工具ping一台服务器，返回结果：是否成功
     *
     * @param osType linux 下是 /bin/sh -c; windows 下是 cmd /c
     * @param ip
     * @return
     */
    public static boolean pingIp(String ip, String osType) {
        try {
            Runtime runtime = Runtime.getRuntime();
            Process process = null;
            if (osType.equals(Constants.ONE)) {
                process = runtime.exec(new String[]{"cmd", "/c", "nmap -sP " + ip});
            } else {
                process = runtime.exec(new String[]{"/bin/sh", "-c", "nmap -sP " + ip});
            }
            InputStream inputStream = process.getInputStream(); // 标准输出流
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "GBK");

            AtomicInteger atomicInteger = new AtomicInteger(0);
            THREAD_POOL.execute(() -> {
                try {
                    BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                    String line;
                    while ((line = bufferedReader.readLine()) != null) {
                        //System.out.println(line); // 输出每行结果
                        if (line.indexOf("Host is up") > -1) {
                            atomicInteger.incrementAndGet();
                            return;
                        }
                    }
                } catch (Exception e) {
                    log.error("pingIp线程运行失败:" + ip + "," + e.getMessage());
                }
            });
            process.waitFor();
            return atomicInteger.incrementAndGet() > 0;
        } catch (Exception ex) {
            log.error("hymap scanIp error:" + ip + "," + ex.getMessage());
        }
        return false;
    }

    public static List<MonitorPort> scanPort(String ip, String osType) {
        List<MonitorPort> rtn = new ArrayList<>();
        try {
            Runtime runtime = Runtime.getRuntime();
            Process process = null;
            if (osType.equals(Constants.ONE)) {
                process = runtime.exec(new String[]{"cmd", "/c", "nmap -sS " + ip});
            } else {
                process = runtime.exec(new String[]{"/bin/sh", "-c", "nmap -sS " + ip});
            }
            InputStream inputStream = process.getInputStream(); // 标准输出流
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "GBK");

            THREAD_POOL.execute(() -> {
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                String line;
                try {
                    while ((line = bufferedReader.readLine()) != null) {
                        //System.out.println(line); // 输出每行结果
                        if (line.indexOf("open") > -1) {
                            MonitorPort tmp = new MonitorPort();
                            line = line.replaceAll("\\s+", " ");
                            String[] abc = line.split("\\s");
                            String[] a = abc[0].split("/");
                            tmp.setIp(ip);
                            tmp.setPort(Long.valueOf(a[0]));
                            tmp.setProtocol(a[1]);
                            tmp.setState(abc[1]);
                            tmp.setAppver(abc.length > 2 ? abc[2] : "");
                            rtn.add(tmp);
                        }
                    }
                } catch (Exception e) {
                    log.error("扫描服务器端口线程运行失败:" + ip + "," + e.getMessage());
                }
            });
            process.waitFor();
        } catch (Exception ex) {
            log.error("hymap scanPort error:" + ip + "," + ex.getMessage());
        }
        return rtn;
    }


    /**
     * 扫描服务器
     *
     * @param ipd
     * @param osType
     * @return
     */
    public static Map<String, Object> scanServer(String[] ips, String ipd, String osType, String jobId) {
        Map result = new HashMap<String, Object>();
        List<MonitorAsset> rtn = new ArrayList<MonitorAsset>();
        Process process = null;
        AtomicReference<Integer> totalScan = new AtomicReference<>();   // 扫描IP总数
        AtomicReference<Integer> alive = new AtomicReference<>();   // 存活IP数
        try {
            // 初始化扫描端口值
            String ports = redisCache.getCacheObject(CacheConstants.SYS_CONFIG_KEY + "monitor.scan");
            StringBuffer instruct = new StringBuffer(NMAP_SCAN_DIRECT);
            if (StringUtils.isEmpty(ports)) {
                instruct.append(DEFAULT_PORT);
            } else {
                instruct.append(ports).append(" ");
            }
            Runtime runtime = Runtime.getRuntime();
            if (osType.equals(Constants.ONE)) {
                process = runtime.exec(new String[]{"cmd", "/c", instruct.toString() + ipd});
            } else {
                process = runtime.exec(new String[]{"/bin/sh", "-c", instruct.toString() + ipd});
            }
            InputStream inputStream = process.getInputStream(); // 标准输出流
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "GBK");
            InputStream errorStream = process.getErrorStream();
            // 将进程对象地址存入堆内存
            PROCESS_MAP.put(CacheConstants.RUN_SCAN_JOB + jobId, process);

            // 线程池运行
            final Process finalProcess = process;
            THREAD_POOL.execute(() -> {
                try {
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(errorStream, "GBK"));
                    String line = null;
                    while ((line = bufferedReader.readLine()) != null) {
                        log.info(line);
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
            THREAD_POOL.execute(() -> {
                log.info("thread runing");
                MonitorAsset pc = new MonitorAsset();
                String ip, line;
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                try {
                    while ((line = bufferedReader.readLine()) != null) {
                        if (line.indexOf("Nmap scan report") > -1) { //得到在线主机的IP
                            if (StringUtils.isNotEmpty(pc.getIp())) {
                                log.info("hymap :" + pc.toString());
                                pc = new MonitorAsset();
                            }
                            rtn.add(pc);
                            if (line.indexOf("(") > -1) {
                                ip = line.substring(line.indexOf("(") + 1, line.indexOf(")"));
                            } else {
                                ip = line.substring(line.indexOf("for") + 4);
                            }
                            pc.setIp(ip);
                            continue;
                        }
                        if (line.indexOf("Host is up") > -1) {
                            pc.setState(Constants.ONE); //主机在线
                            continue;
                        }
                        if (line.indexOf("MAC") > -1) {
                            pc.setMac(line.substring(line.indexOf(": ") + 2, line.indexOf(" ("))); //网卡地址
                            if (line.indexOf("VMware") > 0) {
                                pc.setIsVir(1);//虚拟机
                            }
                            continue;
                        }
                        if (line.indexOf("Running") > -1) {
                            if (line.indexOf("(JUST GUESSING): ") > -1) {
                                String guess = line.substring(line.indexOf("Running") + 8);
                                String osGuess = guess.split(",\\s")[0].replace("(JUST GUESSING): ", "");
                                String os = osGuess.substring(0, osGuess.lastIndexOf(" "));
                                pc.setName(os);
                                // 指纹信息
                                String osVer = guess.replace("(JUST GUESSING): ", "");
                                if (osVer.length() > 250) {
                                    String s = osVer.substring(0, 251);
                                    pc.setOsVer(s.substring(s.lastIndexOf(", ")));
                                } else {
                                    pc.setOsVer(osVer);
                                }
                            } else {
                                String guess = line.substring(line.indexOf("Running") + 9);
                                pc.setName(guess);
                                pc.setOsVer(guess);
                            }
                            continue;
                        }
                        if (line.indexOf("OS CPE") > -1) {
                            pc.setCpe(getCPE(line, pc.getName()));
                            continue;
                        }
                        if (line.indexOf("OS details") == 0 && StringUtils.isEmpty(pc.getOsVer())) {
                            pc.setOsVer(pc.getName() + " (" + getOsVer(line, pc.getName()) + ")");
                            continue;
                        }
                        if (line.indexOf("open") > 0 && line.indexOf("/") > -1) {
                            MonitorPort port = new MonitorPort();
                            line = line.replaceAll("\\s+", " ");
                            String[] abc = line.split("\\s");
                            String[] a = abc[0].split("/");
                            port.setIp(pc.getIp());
                            port.setPort(Long.valueOf(a[0]));
                            port.setProtocol(a[1]);
                            port.setState(abc[1]);
                            port.setApp(abc.length > 2 ? abc[2] : null);
                            if (abc.length > 3) {
                                String[] appVer = Arrays.copyOfRange(abc, 3, abc.length);
                                String appVerStr = String.join(" ", appVer);
                                if (StringUtils.isNotEmpty(appVerStr) && appVerStr.indexOf("(access denied)") < 0) {
                                    port.setAppver(appVerStr);
                                }
                            }
                            pc.addPort(port);
                            continue;
                        }
                        if (line.indexOf("Nmap done:") > -1) {
                            totalScan.set(Integer.valueOf(line.substring(11, line.indexOf("IP") - 1)));
                            alive.set(Integer.valueOf(line.substring(line.indexOf("(") + 1, line.indexOf("host") - 1)));
                        }
                    }
                } catch (Exception ex) {
                    log.error("扫描服务器线程运行失败:" + ipd + "," + ex.getMessage());
                    finalProcess.destroy();
                    if (rtn.size() > 0) {
                        rtn.remove(rtn.size() - 1);
                    }
                    throw new RuntimeException("扫描服务器线程运行失败:" + ipd + "," + ex.getMessage());
                }

            });
            log.info("main waiting...");
            int code = process.waitFor();
            log.info("main move on.");
            // 重新绕过ping扫描端口
//            if (0 == code) {
//                List<String> aliveIps = rtn.stream().map(MonitorAsset::getIp).collect(Collectors.toList());
//                List<String> ipArr = Arrays.asList(ips);
//                String offlineIpd = ipArr.stream().filter(s -> !aliveIps.contains(s)).collect(Collectors.joining(" "));
//                List<MonitorAsset> reScan = scanOfflineServer(offlineIpd, osType, jobId);
//                rtn.addAll(reScan);
//                alive.set(alive.get() - reScan.size());
//            }

        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("hymap scanServer error:" + ipd + "," + ex.getMessage());
        } finally {
            stopProcess(jobId);
            result.put(Constants.SCAN_DATA, rtn);
            result.put(Constants.TOTAL_SCAN, totalScan.get());
            result.put(Constants.ALIVE_SCAN, alive.get());
            return result;
        }
    }

    /**
     * 扫描离线服务器
     *
     * @param ipd
     * @param osType
     * @return
     */
    private static List<MonitorAsset> scanOfflineServer(String ipd, String osType, String jobId) {
        List<MonitorAsset> resArr = new ArrayList<>();
        Process process = null;
        try {
            // 初始化扫描端口值
            String ports = redisCache.getCacheObject(CacheConstants.SYS_CONFIG_KEY + "monitor.scan");
            StringBuffer offlineInstr = new StringBuffer(NMAP_SCAN_OFFLINE_DIRECT);
            if (StringUtils.isEmpty(ports)) {
                offlineInstr.append(DEFAULT_PORT);
            } else {
                offlineInstr.append(ports).append(" ");
            }
            Runtime runtime = Runtime.getRuntime();
            // 获取进程
            if (osType.equals(Constants.ONE)) {
                process = runtime.exec(new String[]{"cmd", "/c", offlineInstr.toString() + ipd});
            } else {
                process = runtime.exec(new String[]{"/bin/sh", "-c", offlineInstr.toString() + ipd});
            }
            InputStream inputStream = process.getInputStream(); // 标准输出流
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "GBK");

            // 将进程对象覆盖
            PROCESS_MAP.put(CacheConstants.RUN_SCAN_JOB + jobId, process);
            // 线程池运行
            Process finalProcess = process;
            THREAD_POOL.execute(() -> {
                MonitorAsset pc = new MonitorAsset();
                String ip, line;
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                try {
                    while ((line = bufferedReader.readLine()) != null) {
                        if (line.indexOf("Nmap scan report") > -1) { //得到在线主机的IP
                            if (StringUtils.isNotEmpty(pc.getIp())) {
                                log.info("offline hymap :" + pc.toString());
                                pc = new MonitorAsset();
                            }
                            pc.setState(Constants.ONE);
                            if (Constants.ONE.equals(pc.getState())) {
                                resArr.add(pc);
                            }
                            if (line.indexOf("(") > -1) {
                                ip = line.substring(line.indexOf("(") + 1, line.indexOf(")"));
                            } else {
                                ip = line.substring(line.indexOf("for") + 4);
                            }
                            pc.setIp(ip);
                            continue;
                        }
                        if (line.indexOf("MAC") > -1) {
                            pc.setMac(line.substring(line.indexOf(": ") + 2, line.indexOf(" ("))); //网卡地址
                            if (line.indexOf("VMware") > 0) {
                                pc.setIsVir(1);//虚拟机
                            }
                            continue;
                        }
                        if (line.indexOf("Running") > -1) {
                            if (line.indexOf("(JUST GUESSING): ") > -1) {
                                String guess = line.substring(line.indexOf("Running") + 8);
                                String osGuess = guess.split(",\\s")[0].replace("(JUST GUESSING): ", "");
                                String os = osGuess.substring(0, osGuess.lastIndexOf(" "));
                                pc.setName(os);
                                // 指纹信息
                                String osVer = guess.replace("(JUST GUESSING): ", "");
                                if (osVer.length() > 250) {
                                    String s = osVer.substring(0, 251);
                                    pc.setOsVer(s.substring(s.lastIndexOf(", ")));
                                } else {
                                    pc.setOsVer(osVer);
                                }
                            } else {
                                String guess = line.substring(line.indexOf("Running") + 9);
                                pc.setName(guess);
                                pc.setOsVer(guess);
                            }
                            continue;
                        }
                        if (line.indexOf("OS CPE") > -1) {
                            pc.setCpe(getCPE(line, pc.getName()));
                            continue;
                        }
                        if (line.indexOf("OS details") == 0 && StringUtils.isEmpty(pc.getOsVer())) {
                            pc.setOsVer(pc.getName() + " (" + getOsVer(line, pc.getName()) + ")");
                            continue;
                        }
                        if (line.indexOf("open") > 0 && line.indexOf("/") > -1) {
                            pc.setState(Constants.ONE);
                            MonitorPort port = new MonitorPort();
                            line = line.replaceAll("\\s+", " ");
                            String[] abc = line.split("\\s");
                            String[] a = abc[0].split("/");
                            port.setIp(pc.getIp());
                            port.setPort(Long.valueOf(a[0]));
                            port.setProtocol(a[1]);
                            port.setState(abc[1]);
                            port.setApp(abc.length > 2 ? abc[2] : null);
                            if (abc.length > 3) {
                                String[] appVer = Arrays.copyOfRange(abc, 3, abc.length);
                                String appVerStr = String.join(" ", appVer);
                                if (StringUtils.isNotEmpty(appVerStr) && appVerStr.indexOf("(access denied)") < 0) {
                                    port.setAppver(appVerStr);
                                }
                            }
                            pc.addPort(port);
                            continue;
                        }
                    }
                } catch (Exception ex) {
                    ex.printStackTrace();
                    log.error("扫描离线服务器线程运行失败:" + ipd + "," + ex.getMessage());
                    // 停止阻塞，防止死锁
                    finalProcess.destroy();
                    if (resArr.size() > 0) {
                        resArr.remove(resArr.size() - 1);
                    }
                    throw new RuntimeException("扫描离线服务器线程运行失败:" + ipd + "," + ex.getMessage());
                }
            });
            process.waitFor();
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("hymap scanOfflineServer error:" + ipd + "," + ex.getMessage());
        } finally {
            return resArr;
        }
    }

    private static String getCPE(String line, String osName) {
        // cpe字符串
        String cpe = null;
        String cepStr = line.substring(8);
        if (StringUtils.isNotEmpty(osName)) {
            if (osName.indexOf("|") > -1) {
                String[] names = osName.split("\\s");
                // 截取版本字符串
                String[] verArr = names[names.length - 1].split("\\|");
                // 截取CPE数组
                String[] cpeArr = cepStr.split("\\s");
                String[] cpeRange = Arrays.copyOfRange(cpeArr, 0, verArr.length > cepStr.length() ? cepStr.length() : verArr.length);
                cpe = String.join(",", cpeRange);
            } else {
                cpe = cepStr;
            }
        }
        return cpe;
    }

    private static String getOsVer(String line, String osName) {
        String osVer = null;
        String verStr = line.substring(12);
        if (StringUtils.isNotEmpty(osName)) {
            if (osName.indexOf("|") > -1) {
                String[] names = osName.split("\\s");
                // 截取版本字符串
                String[] verArr = names[names.length - 1].split("\\|");
                // 截取版本详情数组
                String[] detailArr = verStr.split(", ");
                String[] detailRange = Arrays.copyOfRange(detailArr, 0, verArr.length > detailArr.length ? detailArr.length : verArr.length);
                osVer = String.join(",", detailRange);
            } else {
                osVer = verStr;
            }
        }
        return osVer;
    }

    public static void stopProcess(String jobId) {
        Process process = PROCESS_MAP.get(CacheConstants.RUN_SCAN_JOB + jobId);
        if (process != null) {
            process.destroy();
        }
        PROCESS_MAP.remove(CacheConstants.RUN_SCAN_JOB + jobId);
    }

    public static void main(String[] args) {
    }

}
