{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasks.vue?vue&type=style&index=0&id=492561f2&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasks.vue", "mtime": 1755743179262}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCjo6di1kZWVwIC5lbC1kaWFsb2dfX2JvZHkgewogIHBhZGRpbmc6IDAgMjBweCAzMHB4OwogIGhlaWdodDogODB2aDsKICBvdmVyZmxvdy15OiBhdXRvOwogIG92ZXJmbG93LXg6IGhpZGRlbjsKfQo="}, {"version": 3, "sources": ["ffJobTasks.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4NA;AACA;AACA;AACA;AACA;AACA", "file": "ffJobTasks.vue", "sourceRoot": "src/views/frailty/monitor", "sourcesContent": ["<template>\n    <div style=\"padding: 0 15px\">\n      <div style=\"margin-bottom: 10px;\">\n        <h3 style=\"font-weight: bold\">扫描任务名称：{{ jobRow.jobName}}</h3>\n      </div>\n      <el-table height=\"100%\" v-loading=\"loading\" :data=\"jobTaskList\">\n        <el-table-column\n          label=\"任务名称\"\n        >\n          <template slot-scope=\"scope\">\n            <span> {{ jobRow.jobName }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"扫描目标\"\n        >\n          <template slot-scope=\"scope\">\n            <el-tooltip v-if=\"jobRow.jobType !== 3\" class=\"item\" placement=\"top\">\n              <div v-html=\"jobRow.ipOver\" slot=\"content\"></div>\n              <div class=\"oneLine\">\n                {{ jobRow.ipShow }}\n              </div>\n            </el-tooltip>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"执行周期\"\n          prop=\"username\"\n          width=\"120\"\n        >\n          <template slot-scope=\"scope\">\n            <span> {{ jobRow.cronTransfer }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"最近扫描状态\"\n          prop=\"taskStatus\">\n          <template slot-scope=\"scope\">\n            <el-tag type=\"danger\" v-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 3\">任务异常</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 1\">扫描中</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 2\">扫描中</el-tag>\n            <el-tag type=\"success\" v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 4\">已扫描</el-tag>\n            <el-tag type=\"danger\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 3\">任务异常</el-tag>\n            <el-tag type=\"danger\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 4\">任务终止</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 0\">扫描中</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 1\">扫描中</el-tag>\n            <el-tag type=\"success\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 2\">已扫描</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"扫描进度\"\n          prop=\"finishRate\"\n          width=\"120\"\n        >\n          <template slot-scope=\"scope\">\n            <el-progress :text-inside=\"true\" :stroke-width=\"18\" :percentage=\"scope.row.finishRate\"></el-progress>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"执行时间\"\n          prop=\"endTime\"\n          width=\"160\"\n        >\n          <template slot-scope=\"scope\">\n            {{ parseTime(scope.row.endTime, \"{y}-{m}-{d} {h}:{i}:{s}\") }}\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"操作\"\n          width=\"160\"\n          fixed=\"right\"\n          class-name=\"small-padding fixed-width\"\n          :show-overflow-tooltip=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              @click=\"taskDetail(scope.row)\"\n            >详情\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 2 && scope.row.reportStatus === null\"\n              @click=\"createReport(scope.row)\"\n            >生成报告\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 4 && scope.row.reportStatus === null\"\n              @click=\"createReport(scope.row)\"\n            >生成报告\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.reportStatus !== null && scope.row.reportStatus !== 2\"\n            >报告生成中\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.reportStatus === 2\"\n              @click=\"downReport(scope.row)\"\n            >下载报告\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n      <el-dialog title=\"任务详情\" :visible.sync=\"detailDialog\" width=\"70%\" append-to-body>\n        <ff-job-tasks-detail v-if=\"detailDialog\" :form=\"taskRow\" :job-row=\"jobRow\" />\n        <!--<detail-info v-if=\"detailDialog\" :host-ip=\"hostIp\" :detail-type=\"detailType\" :dept-name=\"deptName\" :is-asset=\"isAsset\" :current-asset-data=\"currentAssetData\" />-->\n      </el-dialog>\n    </div>\n</template>\n\n<script>\n  import { listScantaskSummary, taskCreatereport, taskDownReport } from '../../../api/monitor2/wpresult'\n  import FfJobTasksDetail from './ffJobTasksDetail'\n\n  export default {\n    name: 'ffJobTasks',\n    components: { FfJobTasksDetail },\n    props: {\n      jobRow: {\n        type: Object,\n        default: {}\n      },\n      jobId: {\n        type: [String,Number],\n        required: true,\n      },\n      jobType:{\n        type:Number,\n        default:undefined,\n      }\n    },\n    data() {\n      return {\n        //下拉框所选中的扫描实例id\n        exploreJobId: '',\n        //任务详细信息\n        jobDetail:{},\n        //任务列表\n        jobTaskList: [],\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n        },\n        // 总条数\n        total: 0,\n        detailDialog: false,\n        processStatus: undefined,\n        processId: '',\n        loading: false,\n        exploreDisabled: false,\n        taskRow: [],\n        timer: null,\n        reportType: ''\n        // jobType: 'tanHuo'\n      }\n    },\n    created() {\n      this.getList()\n      var _this = this;\n      this.timer = setInterval(() => {\n        _this.getList()\n      }, 10000);\n    },\n    beforeDestroy() {\n      // 清除定时器\n      if (this.timer) {\n        clearInterval(this.timer);\n      }\n    },\n    methods: {\n      getList() {\n        this.loading = true;\n        this.queryParams.taskType = this.jobType\n        this.queryParams.jobId = this.jobId\n        listScantaskSummary(this.queryParams).then(response => {\n          this.jobTaskList = response.rows\n          this.total = response.total;\n          this.loading = false;\n        }).catch(() => {\n          this.loading = false;\n        });\n      },\n      createReport(row) {\n        taskCreatereport(row).then(response => {\n          this.getList()\n        })\n      },\n      downReport(row) {\n        taskDownReport(row).then(response => {\n          if (response.code === 200) {\n            window.open(response.msg, \"_blank\")\n          }\n        })\n      },\n      taskDetail(row) {\n        this.taskRow = []\n        this.taskRow.push(row)\n        this.detailDialog = true\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .el-dialog__body {\n  padding: 0 20px 30px;\n  height: 80vh;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n</style>\n"]}]}