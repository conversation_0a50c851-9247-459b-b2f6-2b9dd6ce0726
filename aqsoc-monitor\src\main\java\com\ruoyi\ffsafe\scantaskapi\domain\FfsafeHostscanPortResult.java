package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class FfsafeHostscanPortResult extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    private Long id;

    /** 任务id */
    @Excel(name = "任务id")
    private Integer taskId;

    /** 主机ip */
    @Excel(name = "主机ip")
    private String hostIp;

    /** 主机端口 */
    @Excel(name = "主机端口")
    private Integer hostPort;

    /** 协议 */
    @Excel(name = "协议")
    private String procotol;

    /** 服务名称 */
    @Excel(name = "服务名称")
    private String serviceName;

    /** 制造商信息 */
    @Excel(name = "制造商信息")
    private String product;

    /** 汇总记录ID */
    @Excel(name = "汇总记录ID")
    private Long summaryId;
}
