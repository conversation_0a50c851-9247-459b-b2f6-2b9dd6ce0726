{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasksDetail.vue?vue&type=template&id=0a074b3a&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasksDetail.vue", "mtime": 1755743179263}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}