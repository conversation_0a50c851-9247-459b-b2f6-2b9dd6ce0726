package com.ruoyi.monitor2.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.CollectionUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.domain.MonitorPort;
import com.ruoyi.monitor2.domain.NmapConfig;
import com.ruoyi.monitor2.domain.TblProduct;
import com.ruoyi.monitor2.domain.TblProductFinger;
import com.ruoyi.monitor2.service.IMonitorPortService;
import com.ruoyi.monitor2.service.ITblProductService;
import com.ruoyi.monitor2.util.AesUtil;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.service.ISysJobService;
import com.ruoyi.safe.domain.*;
import com.ruoyi.safe.mapper.TblAssetOverviewMapper;
import com.ruoyi.safe.mapper.TblNetworkIpMacMapper;
import com.ruoyi.safe.service.IMonitorAssetService;
import com.ruoyi.safe.service.IMonitorExploreJobService;
import com.ruoyi.safe.service.IMonitorHandlePortService;
import com.ruoyi.safe.service.IMonitorHandleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.util.matcher.IpAddressMatcher;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.util.*;
import java.util.stream.Collectors;

@Component("jkServer")
public class JkServer {
    private Logger logger = LoggerFactory.getLogger(JkServer.class);
    @Resource
    private TblAssetOverviewMapper overviewMapper;

    @Resource
    private TblNetworkIpMacMapper tblNetworkIpMacMapper;

    @Autowired
    private ITblProductService tblProductService;

    @Autowired
    private IMonitorPortService monitorPortService;

    @Autowired
    private IMonitorAssetService monitorAssetService;

    @Autowired
    private IMonitorHandleService monitorHandleService;

    @Autowired
    private IMonitorExploreJobService monitorExploreJobService;

    @Autowired
    private INetworkDomainService networkDomainService;

    @Autowired
    private ISysJobService sysJobService;

    @Autowired
    private IMonitorHandlePortService monitorHandlePortService;

    @Resource
    private ITblDeviceConfigService deviceConfigService;

    protected static List<NetworkDomain> NETWORK_DOMAINS;

//    @Autowired
//    public void initDomains(INetworkDomainService networkDomainService){
//
//    }

    /**
     * 监测服务器下线服务
     *
     */
    public Boolean offline(String ip) {
        logger.info(">>服务器下线监控开始...");
        boolean rtn = false;
        String prex = NmapTool.getCmdPrex();
        if (StringUtils.isNotEmpty(ip)) {
            rtn = NmapTool.pingIp(ip, prex);
        }
        logger.info(">>服务器下线监控结束!");
        return rtn;
    }

    private void foundAsset(MonitorAsset pc) {
        monitorAssetService.insertMonitorAsset(pc);
        List<MonitorPort> ports = pc.getPorts();
        if (CollectionUtils.isNotEmpty(ports)) {
            // 删除旧数据
            monitorPortService.deleteMonitorPortByServerPid(pc.getIp());
            ports.forEach(s -> {
                s.setServerPid(pc.getPid());
                monitorPortService.insertMonitorPort(s);
            });
        }
    }


    /**
     * 处理扫描结果
     * @param assets
     * @param ips
     * @param jobId
     */
    private void disposalResult(List<MonitorAsset> assets, String[] ips, String jobId, String jobEntityId){
        // 循环查找资产对应关系
        TblNetworkIpMac ipMac = new TblNetworkIpMac();
        for (int i = 0; i < ips.length; i++) {
            final String ip = ips[i];
            List<MonitorAsset> collect = assets.stream().filter(s -> s.getIp().equals(ip)).collect(Collectors.toList());
            // 设置查询条件IP
            ipMac.setIpv4(ip);
            List<TblNetworkIpMac> existAssets = tblNetworkIpMacMapper.selectTblNetworkIpMacList(ipMac);
            // 判断资产状态
            if (collect.size() == 0) {
                // 当未探到结果时
                if (existAssets.size() > 0) {
                    existAssets.forEach(s -> {
                        // 资产总信息
                        TblAssetOverview assetOverview = overviewMapper.selectTblAssetOverviewByAssetId(s.getAssetId());
                        // 只要资产状态信息不为离线，即更新
                        if (assetOverview != null && !Constants.ZERO.equals(assetOverview.getState())) {
                            TblAssetOverview tmp = new TblAssetOverview();
                            tmp.setAssetId(assetOverview.getAssetId());
                            tmp.setState(Constants.ZERO);
                            tmp.setUpdateTime(new Date());
                            overviewMapper.updateTblAssetOverview(tmp);
                        }
                        // 跟新最后一次扫描字段
                        s.setLastScanState(0);
                        s.setLastScanTime(new Date());
                        tblNetworkIpMacMapper.updateScheduleInfo(s);
                    });
                }
                // 插入未谈查到的ip
                MonitorAsset asset = new MonitorAsset();
                asset.setJobId(jobEntityId);
                asset.setIp(ip);
                asset.setState(Constants.ZERO);
                foundAsset(asset);
            } else {
                // 当探到结果时
                if (existAssets.size() == 0) {
                    // 当线上未录入时,插入新资产信息
                    MonitorHandle handle = new MonitorHandle();
                    BeanUtils.copyProperties(collect.get(0), handle);
                    handle.setJobId(Long.valueOf(jobId));
                    // 判断IP所属网络
                    handle.setDomainId(getDomainId(ip));
                    handle.setExceptionState(0);
                    handle.setIsDel(0);
                    monitorHandleService.insertOrUpdateMonitorHandle(handle);
                } else {
                    // 当线上有录入时
                    MonitorAsset asset = collect.get(0);
                    existAssets.forEach(s -> {
                        TblAssetOverview assetOverview = overviewMapper.selectTblAssetOverviewByAssetId(s.getAssetId());
                        // 只要资产状态信息不为在线，即更新
                        if (assetOverview!=null && !Constants.ONE.equals(assetOverview.getState())) {
                            TblAssetOverview tmp = new TblAssetOverview();
                            tmp.setAssetId(assetOverview.getAssetId());
                            tmp.setState(Constants.ONE);
                            tmp.setUpdateTime(new Date());
                            overviewMapper.updateTblAssetOverview(tmp);
                        }
                        // 判断是否为服务器设备
                        Integer isServer = overviewMapper.selectIsServer(s.getAssetId());
                        // 只有为服务器设备时才可进行指纹对比
                        if (isServer > 0 && assetOverview!=null) {
                            // 获取指纹信息
                            List<TblProduct> tblProducts = tblProductService.selectTblProductByAssetId(s.getAssetId(), Constants.ONE, null);
                            // 判断是否正常,true为正常
                            boolean isRight = checkFingerRight(tblProducts, asset);
                            if (!isRight) {
                                // 插入异常
                                MonitorHandle handle = new MonitorHandle();
                                BeanUtils.copyProperties(asset, handle);
                                handle.setJobId(Long.valueOf(jobId));
                                handle.setDomainId(s.getDomainId());
                                handle.setDeptId(assetOverview.getDeptId());
                                handle.setExceptionState(1);
                                handle.setAssetId(s.getAssetId());
                                handle.setIsDel(0);
                                monitorHandleService.insertOrUpdateMonitorHandle(handle);
                            }
                            // 处理端口
                            disposePort(s.getAssetId(), asset, s.getDomainId(), Long.valueOf(jobId), assetOverview.getDeptId());
                        }
                        // 跟新最后一次扫描字段
                        s.setLastScanState(1);
                        s.setLastScanTime(new Date());
                        tblNetworkIpMacMapper.updateScheduleInfo(s);
                    });
                }
            }

        }
    }

    /**
     *  处理端口
     * @param assetId 资产id
     * @param scanResult 扫描结果
     * @param domainId 所属网络
     * @param jobId 任务id
     * @param deptId 部门Id
     */
    private void disposePort(Long assetId, MonitorAsset scanResult, Long domainId, Long jobId, Long deptId){
        if (CollectionUtils.isEmpty(scanResult.getPorts())) {
            return;
        }
        // 处理异常
        scanResult.getPorts().forEach(s -> {
            List<TblProduct> product = tblProductService.selectTblProductByAssetId(assetId, null, s.getPort());
            if (CollectionUtils.isEmpty(product)) {
                // 发现新资产
                MonitorHandlePort handlePort = new MonitorHandlePort();
                handlePort.setAssetId(assetId);
                handlePort.setDomainId(domainId);
                handlePort.setJobId(jobId);
                handlePort.setDeptId(deptId);
                handlePort.setAppName(s.getApp());
                handlePort.setAppVer(s.getAppver());
                handlePort.setExceptionState(0);
                handlePort.setIp(scanResult.getIp());
                handlePort.setPort(s.getPort());
                handlePort.setState(s.getState());
                handlePort.setIsDel(0);
                monitorHandlePortService.insertOrUpdateHandlePort(handlePort);
            } else {
                product.forEach(p -> {
                    List<TblProductFinger> fingers = p.getFingers();
                    boolean isRight = false;
                    if(CollectionUtils.isNotEmpty(fingers)){
                        isRight = fingers.stream().anyMatch(f -> s.getApp().equals(f.getOsName()));
                    }
                    if (!(s.getApp().equals(p.getProcName()) || isRight)) {
                        // 发现异常资产
                        MonitorHandlePort handlePort = new MonitorHandlePort();
                        handlePort.setAssetId(assetId);
                        handlePort.setDomainId(domainId);
                        handlePort.setJobId(jobId);
                        handlePort.setDeptId(deptId);
                        handlePort.setDeployId(p.getDeployId());
                        handlePort.setAppName(s.getApp());
                        handlePort.setAppVer(s.getAppver());
                        handlePort.setExceptionState(1);
                        handlePort.setIp(scanResult.getIp());
                        handlePort.setPort(s.getPort());
                        handlePort.setState(s.getState());
                        handlePort.setIsDel(0);
                        monitorHandlePortService.insertOrUpdateHandlePort(handlePort);
                    }
                });
            }
        });
    }

    /**
     * 格式化IP
     */
    private String[] formatIPs(String[] ipArr) {
        List<String> result = new ArrayList<>();
        String pre;
        String suffix;
        Integer start;
        Integer end;
        for (int i = 0; i < ipArr.length; i++){
            String current = ipArr[i];
            if(current.indexOf("/24") != -1 || current.indexOf("/16") != -1){
                List<String> allIpsInCidr = getAllIpsInCidr(current);
                result.addAll(allIpsInCidr);
                continue;
            }
            if (ipArr[i].indexOf("-") > -1) {
                pre = ipArr[i].substring(0, ipArr[i].lastIndexOf(".") + 1);
                suffix = ipArr[i].substring(ipArr[i].lastIndexOf(".") + 1);
                String[] range = suffix.split("-");
                start = Integer.valueOf(range[0]);
                end = Integer.valueOf(range[1]);
                for (int j = start; j <= end; j++) {
                    result.add(pre + j);
                }
            } else {
                result.add(ipArr[i]);
            }
        }
        return (result.toArray(new String[]{}));
    }

    // 根据IP获取domainId
    protected Long getDomainId(String ip){
        this.NETWORK_DOMAINS = networkDomainService.selectNetworkDomainList(new NetworkDomain());
        for (int i = 0; i < this.NETWORK_DOMAINS.size(); i++) {
            if (StringUtils.isNotEmpty(this.NETWORK_DOMAINS.get(i).getIparea())) {
                IpAddressMatcher ipAddressMatcher = new IpAddressMatcher(this.NETWORK_DOMAINS.get(i).getIparea());
                if (ipAddressMatcher.matches(ip)) {
                    return this.NETWORK_DOMAINS.get(i).getDomainId();
                }
            }
        }
        return null;
    }

    // 指纹对比方法
    protected boolean checkFingerRight(List<TblProduct> products, MonitorAsset asset){
        if (StringUtils.isEmpty(asset.getName())) {
            return true;
        }
        // 如果没有标准软件库程序，则比对失败
        if (CollectionUtils.isEmpty(products)) {
            return false;
        }
        // 获取标准软件库程序
        TblProduct tblProduct = products.get(0);
        if (asset.getName().equals(tblProduct.getProcName())) {
            return true;
        }
        // 获取标准软件库指纹信息
        List<TblProductFinger> fingers = tblProduct.getFingers();
        if (fingers == null) {
            return false;
        }
        // 对比开始
        return fingers.stream().anyMatch(s -> asset.getName().equals(s.getOsName()));
    }

    /**
     * 扫描网络 ，发现计算机
     * nmap -sP ***********-254
     *
     * @param ipd   IP段 exp: ***********-254
     * @param jobId 所属网络
     */
    @Transactional(rollbackFor = Exception.class)
    public void scan(String jobId, String ipd) {
        // ######创建任务实例######
        SysJob sysJob = sysJobService.selectJobById(Long.valueOf(jobId));
        TblDeviceConfig deviceConfig = deviceConfigService.selectDeviceConfigOrDefault(sysJob.getDeviceConfigId());
        NmapConfig nmapConfig = deviceConfigService.getNmapConfig(deviceConfig);

        MonitorExploreJob jobEntity = new MonitorExploreJob();
        jobEntity.setJobId(Long.valueOf(jobId));
        jobEntity.setStatus(1);
        String[] ipdArr = ipd.split("\\s+");
        jobEntity.setCreateTime(new Date());
        monitorExploreJobService.insertMonitorExploreJob(jobEntity);
        // ######更新任务状态######
        SysJob job = new SysJob();
        job.setJobId(Long.valueOf(jobId));
        job.setCurrentStatus(1);
        sysJobService.updateJobImmediately(job);
        // #######开始扫描######
        String osType;
        osType = NmapTool.getOsType();
        //先扫描网段
        String[] ips = formatIPs(ipdArr);
        Map result = null;
        if(nmapConfig == null || nmapConfig.getType() == 1){
            logger.info(">>网段扫描开始...");
            result = NmapTool.scanServer(ips, ipd, osType, jobId);
            logger.info(">>网段扫描结束...");
        }else if (nmapConfig.getType() == 2){
            //远程服务
            logger.info(">>远程NMAP扫描开始...");
            JSONObject params = new JSONObject();
            params.put("ips", ips);
            params.put("ipd", ipd);
            params.put("osType", osType);
            params.put("jobId", jobId);
            JSONObject reqParam = new JSONObject();
            reqParam.put("params", AesUtil.NmapAesEncrypt(params.toJSONString()));
            String body = HttpRequest.post(StrUtil.format("http://{}:{}{}", nmapConfig.getIp(), nmapConfig.getPort(), nmapConfig.getUrl())).body(reqParam.toJSONString()).timeout(1000*60*120) //超时120分钟
                    .execute().body();
            JSONObject reqResult = JSONObject.parseObject(body);
            String reqResultData = reqResult.getString("data");
            result = JSONObject.parseObject(AesUtil.NmapAesDecrypt(reqResultData));
            logger.info(">>远程NMAP扫描结束...");
        }

        // 判断是否为中断状态
        MonitorExploreJob isInterrupt = monitorExploreJobService.selectMonitorExploreJobById(jobEntity.getId());
        if(isInterrupt == null){
            isInterrupt = jobEntity;
        }
        if (CollUtil.isNotEmpty(result) && isInterrupt.getStatus() < 2) {
            List<MonitorAsset> list = new ArrayList<>();
            if(result.get(Constants.SCAN_DATA) instanceof JSONArray){
                for (Object resultItem : ((JSONArray) result.get(Constants.SCAN_DATA))) {
                    JSONObject bean = BeanUtil.toBean(resultItem, JSONObject.class);
                    MonitorAsset monitorAsset = new MonitorAsset();
                    BeanUtil.copyProperties(bean, monitorAsset);
                    list.add(monitorAsset);
                }
            }else {
                list = (List<MonitorAsset>) result.get(Constants.SCAN_DATA);
            }
            disposalResult(list, ips, jobId, String.valueOf(jobEntity.getId()));
            // #######处理操作系统结果######
            List<String> verNameList = list.stream().filter(item -> StrUtil.isNotBlank(item.getName())).map(MonitorAsset::getName).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(verNameList)){
                List<TblProduct> productList = tblProductService.selectByProcNameList(verNameList);
                if(CollUtil.isNotEmpty(productList)){
                    verNameList.removeIf(item -> productList.stream().anyMatch(prodItem -> item.equals(prodItem.getProcName())));
                }
                if(CollUtil.isNotEmpty(verNameList)){
                    DateTime nowDate = DateUtil.date();
                    List<TblProduct> saveProductList = verNameList.stream().map(verName -> {
                        TblProduct product = new TblProduct();
                        product.setProcName(verName);
                        product.setProcType("system");
                        product.setPubTime(nowDate);
                        return product;
                    }).collect(Collectors.toList());
                    tblProductService.batchInsertBaseList(saveProductList);
                }
            }

            for (MonitorAsset pc : list) {
                pc.setJobId(String.valueOf(jobEntity.getId()));
//            pc.setDomainId(getDomainId(pc.getIp()));
                foundAsset(pc);
            }
            // ######更新任务实例状态######
            MonitorExploreJob jobEntityUpd = new MonitorExploreJob();
            jobEntityUpd.setId(jobEntity.getId());
            if (result.get(Constants.TOTAL_SCAN) != null) {
                jobEntityUpd.setTotalScan((Integer) result.get(Constants.TOTAL_SCAN));
            } else {
                jobEntityUpd.setTotalScan(formatIPs(ipdArr).length);
            }
            if (result.get(Constants.ALIVE_SCAN) != null) {
                jobEntityUpd.setAlive((Integer) result.get(Constants.ALIVE_SCAN));
            } else {
                jobEntityUpd.setAlive(list.size());
            }
            jobEntityUpd.setStatus(0);
            jobEntityUpd.setUpdateTime(new Date());
            monitorExploreJobService.updateMonitorExploreJob(jobEntityUpd);

            // ######更新任务状态######
            job.setCurrentStatus(2);
            sysJobService.updateJobImmediately(job);
        }

    }

    public static List<String> getAllIpsInCidr(String cidr) {
        List<String> ips = new ArrayList<>();
        try {
            String[] parts = cidr.split("/");
            String ipPart = parts[0];
            int prefix = Integer.parseInt(parts[1]);

            InetAddress inetAddress = InetAddress.getByName(ipPart);
            byte[] addressBytes = inetAddress.getAddress();
            int address = ByteBuffer.wrap(addressBytes).getInt();

            int mask = 0xFFFFFFFF << (32 - prefix);
            int network = address & mask;
            int broadcast = network | ~mask;

            for (int i = network + 1; i < broadcast; i++) {
                byte[] ipBytes = ByteBuffer.allocate(4).putInt(i).array();
                InetAddress ip = InetAddress.getByAddress(ipBytes);
                ips.add(ip.getHostAddress());
            }
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        return ips;
    }
}
