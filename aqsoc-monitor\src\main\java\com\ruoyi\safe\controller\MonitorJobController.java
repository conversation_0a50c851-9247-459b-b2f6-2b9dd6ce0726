package com.ruoyi.safe.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.ScheduleConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.task.NmapTool;
import com.ruoyi.quartz.domain.SysJobLog;
import com.ruoyi.quartz.mapper.SysJobMapper;
import com.ruoyi.quartz.service.ISysJobLogService;
import com.ruoyi.safe.domain.MonitorExploreJob;
import com.ruoyi.safe.service.IMonitorExploreJobService;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.job.TaskException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.quartz.domain.SysJob;
import com.ruoyi.quartz.service.ISysJobService;
import com.ruoyi.quartz.util.CronUtils;
import com.ruoyi.quartz.util.ScheduleUtils;

/**
 * 调度任务信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/schedule")
public class MonitorJobController extends BaseController
{
    @Resource
    private SysJobMapper jobMapper;

    @Resource
    private ISysJobLogService sysJobLogService;

    @Autowired
    private ISysJobService jobService;

    @Autowired
    private IMonitorExploreJobService monitorExploreJobService;

    @Resource
    private ITblDeviceConfigService deviceConfigService;

    /**
     * 查询定时任务列表
     */
    //@PreAuthorize("@ss.hasPermi('monitor:schedule:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysJob sysJob)
    {
        startPage();
        List<SysJob> list = jobService.selectJobList(sysJob);
        SysJobLog jobLog = new SysJobLog();
        list.forEach(s -> {
            jobLog.setJobGroup(s.getJobGroup());
            jobLog.setJobName(s.getJobName());
            jobLog.setInvokeTarget(s.getInvokeTarget());
            SysJobLog tmp = sysJobLogService.selectLastJobLog(jobLog);
            s.setLastRunTime(tmp == null ? null : tmp.getCreateTime());
        });
        return getDataTable(list);
    }

    @GetMapping("/listByType")
    public TableDataInfo listByType(SysJob sysJob)
    {
        startPage();
        List<SysJob> list = jobService.selectJobListByType(sysJob);
        SysJobLog jobLog = new SysJobLog();
        list.forEach(s -> {
            jobLog.setJobGroup(s.getJobGroup());
            jobLog.setJobName(s.getJobName());
            jobLog.setInvokeTarget(s.getInvokeTarget());
            SysJobLog tmp = sysJobLogService.selectLastJobLog(jobLog);
            s.setLastRunTime(tmp == null ? null : tmp.getCreateTime());
        });
        return getDataTable(list);
    }

    /**
     * 导出定时任务列表
     */
//    @PreAuthorize("@ss.hasPermi('monitor:schedule:export')")
    @Log(title = "定时任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysJob sysJob)
    {
        List<SysJob> list = jobService.selectJobList(sysJob);
        ExcelUtil<SysJob> util = new ExcelUtil<SysJob>(SysJob.class);
        util.exportExcel(response, list, "定时任务");
    }

    /**
     * 获取定时任务详细信息
     */
//    @PreAuthorize("@ss.hasPermi('monitor:schedule:query')")
    @GetMapping(value = "/{jobId}")
    public AjaxResult getInfo(@PathVariable("jobId") Long jobId)
    {
        return AjaxResult.success(jobService.selectJobById(jobId));
    }

    /**
     * 新增定时任务
     */
//    @PreAuthorize("@ss.hasPermi('monitor:schedule:add')")
    @Log(title = "定时任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysJob job) throws SchedulerException, TaskException
    {
        SysJob tmp = new SysJob();
        tmp.setJobGroup(job.getJobGroup());
        tmp.setJobName(job.getJobName());
        tmp.setJobType(job.getJobType());
        if (jobMapper.selectNameCount(tmp) > 0)
        {
            return error("新增任务'" + job.getJobName() + "'失败，已存在同名任务");
        }
        if (!CronUtils.isValid(job.getCronExpression()))
        {
            return error("新增任务'" + job.getJobName() + "'失败，Cron表达式不正确");
        }
        else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), Constants.LOOKUP_RMI))
        {
            return error("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用");
        }
        else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[] { Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS }))
        {
            return error("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
        }
        /* else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[] { Constants.HTTP, Constants.HTTPS }))
        {
            return error("新增任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
        }*/
        else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), Constants.JOB_ERROR_STR))
        {
            return error("新增任务'" + job.getJobName() + "'失败，目标字符串存在违规");
        }
        else if (!ScheduleUtils.whiteList(job.getInvokeTarget()))
        {
            return error("新增任务'" + job.getJobName() + "'失败，目标字符串不在白名单内");
        }

        if(job.getJobType() == SysJob.WEB_SCAN){
            //Web漏洞扫描
            if(job.getDeviceConfigId() == null){
                return AjaxResult.error("未选择设备");
            }
            TblDeviceConfig deviceConfig = deviceConfigService.selectTblDeviceConfigById(job.getDeviceConfigId());
            if(deviceConfig == null){
                return AjaxResult.error("未找到配置设备，选择的配置可能已经删除，请重新选择");
            }
            if(deviceConfig.getStatus() != 1){
                return AjaxResult.error("选择的设备未启用");
            }
            job.setCreateBy(getUsername());
            job.setCurrentStatus(0);
            jobMapper.insertJob(job);
            job.setInvokeTarget(job.getInvokeTarget().replace("${jobId}", String.valueOf(job.getJobId())));
            jobService.updateJob(job);
        }else {
            //其他漏洞扫描
            String targets = null;
            String splitChar = ";";
            if(job.getJobType() == SysJob.HOST_SCAN){
                splitChar = " ";
                //探活任务
                String regex = "jkServer\\.scan\\('\\$\\{jobId\\}','([^']+)'\\)";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(job.getInvokeTarget());
                if (matcher.find()) {
                    targets = matcher.group(1);
                    if(StrUtil.isNotBlank(targets)){
                        targets = targets.replace(" ",";");
                    }
                }
            }else {
                String[] fields = job.getInvokeTarget().split("\\|");
                targets = fields[1];
            }
            if(StrUtil.isBlank(targets)){
                return AjaxResult.error("未识别到任务对象");
            }
            List<JSONObject> deviceConfigs = deviceConfigService.selectTargetDeviceConfig(targets);
            if(CollUtil.isEmpty(deviceConfigs)){
                return AjaxResult.error("未找到配置设备，请确认任务对象在配置的网络区域内并且已经关联设备");
            }
            Map<Long,JSONObject> configMap = new ConcurrentHashMap<>();
            //判断deviceConfigs是否存在不同的config
            for (JSONObject curConfigData : deviceConfigs) {
                TblDeviceConfig config = curConfigData.getObject("config", TblDeviceConfig.class);
                if (config == null) {
                    return AjaxResult.error(curConfigData.getString("target") + "未找到配置设备，请确认任务对象在配置的网络区域内并且已经关联设备");
                }
                JSONObject configData = configMap.get(config.getId());
                if (configData == null) {
                    configData = new JSONObject();
                    configData.put("config", config);
                    List<String> ips = new ArrayList<>();
                    ips.add(curConfigData.getString("target"));
                    configData.put("data", ips);
                } else {
                    List<String> ips = configData.getList("data", String.class);
                    ips.add(curConfigData.getString("target"));
                    configData.put("data", ips);
                }
                configMap.put(config.getId(), configData);
            }

            job.setInvokeTarget(job.getInvokeTarget().replace(targets,"${ips}"));
            job.setCreateBy(getUsername());
            job.setCurrentStatus(0);
            int index = 1;
            for (JSONObject configData : configMap.values()) {
                TblDeviceConfig config = configData.getObject("config", TblDeviceConfig.class);
                List<String> ips = configData.getList("data", String.class);
                SysJob curJob = new SysJob();
                BeanUtil.copyProperties(job,curJob);
                if(index > 1){
                    curJob.setJobName(curJob.getJobName() + "_" + index);
                }
                curJob.setInvokeTarget(curJob.getInvokeTarget().replace("${ips}", CollUtil.join(ips, splitChar)).replace(job.getJobName(), curJob.getJobName()));
                curJob.setDeviceConfigId(config.getId());
                jobMapper.insertJob(curJob);
                curJob.setInvokeTarget(curJob.getInvokeTarget().replace("${jobId}", String.valueOf(curJob.getJobId())));
                jobService.updateJob(curJob);
                index++;
            }
        }
        return AjaxResult.success();
    }

    /**
     * 修改定时任务
     */
//    @PreAuthorize("@ss.hasPermi('monitor:schedule:edit')")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysJob job) throws SchedulerException, TaskException
    {
        // 判断名字是否存在
        SysJob tmp = new SysJob();
        tmp.setJobId(job.getJobId());
        tmp.setJobName(job.getJobName());
        tmp.setJobGroup(job.getJobGroup());
        tmp.setJobType(job.getJobType());
        if (jobMapper.selectNameCount(tmp) > 0)
        {
            return error("新增任务'" + job.getJobName() + "'失败，已存在同名任务");
        }
        tmp = jobMapper.selectJobById(job.getJobId());

        if (!CronUtils.isValid(job.getCronExpression()))
        {
            return error("修改任务'" + job.getJobName() + "'失败，Cron表达式不正确");
        }
        else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), Constants.LOOKUP_RMI))
        {
            return error("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'rmi'调用");
        }
        else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[] { Constants.LOOKUP_LDAP, Constants.LOOKUP_LDAPS }))
        {
            return error("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'ldap(s)'调用");
        }
        /*else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[] { Constants.HTTP, Constants.HTTPS }))
        {
            return error("修改任务'" + job.getJobName() + "'失败，目标字符串不允许'http(s)'调用");
        }*/
        else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), Constants.JOB_ERROR_STR))
        {
            return error("修改任务'" + job.getJobName() + "'失败，目标字符串存在违规");
        }
        else if (!ScheduleUtils.whiteList(job.getInvokeTarget()))
        {
            return error("修改任务'" + job.getJobName() + "'失败，目标字符串不在白名单内");
        }
        job.setUpdateBy(getUsername());
        job.setInvokeTarget(job.getInvokeTarget().replace("${jobId}", String.valueOf(job.getJobId())));
        // 不更新当前状态值
        job.setCurrentStatus(null);
        int rows = jobService.updateJob(job);
        // 名字修改需进行日志同步
        if (rows > 0 && !tmp.getJobName().equals(job.getJobName())) {
            SysJobLog queryLog = new SysJobLog();
            queryLog.setJobName(tmp.getJobName());
            queryLog.setJobGroup(tmp.getJobGroup());
            List<SysJobLog> sysJobLogs = sysJobLogService.selectJobLogList(queryLog);
            sysJobLogs.forEach(s -> {
                s.setJobName(job.getJobName());
                sysJobLogService.updateSysJobLog(s);
            });
        }
        return toAjax(rows);
    }

    /**
     * 定时任务状态修改
     */
    // @PreAuthorize("@ss.hasPermi('monitor:schedule:edit')")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysJob job) throws SchedulerException
    {
        SysJob newJob = jobService.selectJobById(job.getJobId());
        newJob.setStatus(job.getStatus());
        // 停止任务
        if (ScheduleConstants.Status.PAUSE.getValue().equals(job.getStatus())) {
            // 手动终止
            newJob.setCurrentStatus(2);
            // 更新所有未完成的实例
            MonitorExploreJob query = new MonitorExploreJob();
            query.setJobId(job.getJobId());
            query.setStatus(2);
            monitorExploreJobService.updateMonitorExploreJobByJobId(query);
            // 再终止nmap
            NmapTool.stopProcess(String.valueOf(job.getJobId()));
        }
        return toAjax(jobService.changeStatus(newJob));
    }

    /**
     * 定时任务状态修改
     */
//    @PreAuthorize("@ss.hasPermi('monitor:schedule:edit')")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    @PutMapping("/stopExplore")
    public AjaxResult stopExplore(@RequestBody SysJob job) throws SchedulerException
    {
        // 先更新数据状态
        SysJob newJob = jobService.selectJobById(job.getJobId());
        if (newJob.getPeriod() == 0) {
            job.setStatus(ScheduleConstants.Status.PAUSE.getValue());
        }
        job.setCurrentStatus(2);
        // 更新所有未完成的实例
        MonitorExploreJob query = new MonitorExploreJob();
        // 设置为已终止
        query.setStatus(2);
        query.setJobId(job.getJobId());
        int rows = monitorExploreJobService.updateMonitorExploreJobByJobId(query);
        if (rows == 0) {
            return AjaxResult.error("任务已经执行完成，请刷新页面后再试！");
        }
        // 再终止nmap
        NmapTool.stopProcess(String.valueOf(job.getJobId()));
        return toAjax(jobMapper.updateJob(job));
    }

    /**
     * 定时任务立即执行一次
     */
//    @PreAuthorize("@ss.hasPermi('monitor:schedule:edit')")
    @Log(title = "定时任务", businessType = BusinessType.UPDATE)
    @PutMapping("/run")
    public AjaxResult run(@RequestBody SysJob job) throws SchedulerException
    {
        boolean result = jobService.run(job);
        return result ? success() : error("任务不存在或已过期！");
    }

    /**
     * 删除定时任务
     */
//    @PreAuthorize("@ss.hasPermi('monitor:schedule:remove')")
    @Log(title = "定时任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{jobIds}")
    public AjaxResult remove(@PathVariable Long[] jobIds) throws SchedulerException, TaskException
    {
        jobService.deleteJobByIds(jobIds);
        return success();
    }
}
