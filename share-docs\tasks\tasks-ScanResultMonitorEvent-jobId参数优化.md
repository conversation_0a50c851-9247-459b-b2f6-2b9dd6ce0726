# ScanResultMonitorEvent jobId 参数优化任务计划

## 项目背景
为 aqsoc-main 项目中的 ScanResultMonitorEvent 类添加 jobId 参数，优化数据库查询精确性。

## 修改目标
1. 在 `dealFfsafeScanTaskSummary` 方法中新增 `jobId` 参数
2. 在 `dealFfsafeWebscanTaskSummary` 方法中新增 `jobId` 参数  
3. 在 `getSysJogByTaskId` 方法中新增 `jobId` 参数
4. 更新所有相关调用点，确保 `jobId` 参数正确传递

## 技术方案
采用渐进式修改方案，保持现有业务逻辑稳定，仅在关键查询点添加 jobId 参数。

## 任务清单

### 任务 1：修改 getSysJogByTaskId 方法签名和查询逻辑
- **ID**: aee7e229-a0da-4eed-b173-bbf69859db6a
- **描述**: 为核心查询方法添加 jobId 参数，提高查询精确性
- **修改点**: 
  - 方法签名：`getSysJogByTaskId(int taskId, int taskType, int jobId)`
  - 查询条件：`ffsafeScantaskSummary.setJobId(jobId)`
  - 参数验证：添加 jobId 有效性检查

### 任务 2：修改 dealFfsafeScanTaskSummary 方法签名和调用
- **ID**: 7c0dcda8-1a14-4d07-a51c-31285db797d6
- **描述**: 为主机扫描处理方法添加 jobId 参数
- **依赖**: 任务 1
- **修改点**:
  - 方法签名：`dealFfsafeScanTaskSummary(int taskId, HostScanTaskSummaryResult hostScanTaskSummaryResult, int jobId)`
  - 内部调用：`getSysJogByTaskId(taskId, HostVulnScan.HOST_SCAN, jobId)`

### 任务 3：修改 dealFfsafeWebscanTaskSummary 方法签名和调用
- **ID**: a58d7a7c-a742-4b64-ac4e-4134b72b19e4
- **描述**: 为Web扫描处理方法添加 jobId 参数
- **依赖**: 任务 1
- **修改点**:
  - 方法签名：`dealFfsafeWebscanTaskSummary(int taskId, WebScanTaskSummaryResult webScanTaskSummaryResult, int jobId)`
  - 内部调用：`getSysJogByTaskId(taskId, HostVulnScan.WEB_SCAN, jobId)`

### 任务 4：更新主机扫描任务的调用点
- **ID**: 3b9b0b69-fef9-40fe-954c-888935365bdb
- **描述**: 更新 startEvent 方法中主机扫描部分的调用
- **依赖**: 任务 2
- **修改点**:
  - 获取 jobId：`Long jobId = taskInfo.getLong("jobId")`
  - 更新调用：`dealFfsafeScanTaskSummary(taskId, hostScanTaskSummaryResult, jobId.intValue())`

### 任务 5：更新Web扫描任务的调用点
- **ID**: e3e925c7-e577-4186-950a-52430bc83bdb
- **描述**: 更新 startEvent 方法中Web扫描部分的调用
- **依赖**: 任务 3
- **修改点**:
  - 使用已有 jobId 变量
  - 更新调用：`dealFfsafeWebscanTaskSummary(webtaskId, webScanTaskSummaryResult, jobId.intValue())`

### 任务 6：代码质量检查和测试验证
- **ID**: 1bc2618c-4a91-4247-82a8-c916e1049a82
- **描述**: 全面质量检查，确保修改符合要求
- **依赖**: 任务 4, 任务 5
- **检查项**:
  - 方法签名修改正确性
  - 调用点参数传递正确性
  - 参数验证和错误处理
  - 代码格式和注释一致性
  - MyBatis 查询逻辑验证

## 关键文件
- `aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/scantaskapi/event/ScanResultMonitorEvent.java`
- `aqsoc-monitor/src/main/resources/mapper/ffsafe/FfsafeScantaskSummaryMapper.xml`

## 风险控制
1. 渐进式修改，降低引入bug风险
2. 保持现有错误处理逻辑不变
3. 确保向后兼容性
4. 重点测试查询逻辑的正确性

## 验收标准
1. 所有方法签名正确修改
2. 所有调用点正确传递 jobId 参数
3. 数据库查询更加精确
4. 现有功能不受影响
5. 代码质量符合最佳实践
