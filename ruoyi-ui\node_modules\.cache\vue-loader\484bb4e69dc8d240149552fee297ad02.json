{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\apiAlarmList.vue?vue&type=template&id=6397e3e8&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\apiAlarmList.vue", "mtime": 1755679994095}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}