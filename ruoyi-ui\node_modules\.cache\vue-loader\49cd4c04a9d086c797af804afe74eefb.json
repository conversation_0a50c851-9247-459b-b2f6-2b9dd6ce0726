{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasksDetail.vue?vue&type=template&id=0a074b3a&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasksDetail.vue", "mtime": 1755743179263}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJkaXYiLAogICAgWwogICAgICBfYygKICAgICAgICAiZWwtcm93IiwKICAgICAgICB7IHN0YXRpY0NsYXNzOiAic19kX3JvdyIgfSwKICAgICAgICBbCiAgICAgICAgICBfYygiZWwtY29sIiwgWwogICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJzX3NwYW5fdGFnIiB9LCBbX3ZtLl92KCLmvI/mtJ7nu5Pmnpznu7zov7AiKV0pLAogICAgICAgICAgXSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF9jKAogICAgICAgICJlbC1yb3ciLAogICAgICAgIHsgc3RhdGljQ2xhc3M6ICJyb3ctaXRlbSIsIGF0dHJzOiB7IGd1dHRlcjogMTUgfSB9LAogICAgICAgIFsKICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDEyIH0gfSwgWwogICAgICAgICAgICBfYygiZGl2IiwgewogICAgICAgICAgICAgIHJlZjogImhvbGVNYXAiLAogICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IGhlaWdodDogIjMwMHB4IiB9LAogICAgICAgICAgICAgIGF0dHJzOiB7IGlkOiAiaG9sZU1hcCIgfSwKICAgICAgICAgICAgfSksCiAgICAgICAgICBdKSwKICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDEyIH0gfSwgWwogICAgICAgICAgICBfYygiZGl2IiwgewogICAgICAgICAgICAgIHJlZjogInBpZU1hcCIsCiAgICAgICAgICAgICAgc3RhdGljU3R5bGU6IHsgaGVpZ2h0OiAiMzAwcHgiIH0sCiAgICAgICAgICAgICAgYXR0cnM6IHsgaWQ6ICJwaWVNYXAiIH0sCiAgICAgICAgICAgIH0pLAogICAgICAgICAgXSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF9jKAogICAgICAgICJlbC1yb3ciLAogICAgICAgIHsgYXR0cnM6IHsgZ3V0dGVyOiAxMCB9IH0sCiAgICAgICAgWwogICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogMjQgfSB9LCBbCiAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJjb2xsYXBzZS1jb250ZW50LWRpdiIgfSwKICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICBfdm0uam9iUm93LmpvYlR5cGUgPT09IDEKICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC10YWJsZSIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IHdpZHRoOiAiMTAwJSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBfdm0uZm9ybSwKICAgICAgICAgICAgICAgICAgICAgICAgICAiaGVhZGVyLWNlbGwtc3R5bGUiOiBfdm0uaGVhZGVyQ2VsbFN0eWxlLAogICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogIiIsCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiAi5Lu75Yqh5ZCN56ewIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlZFNsb3RzOiBfdm0uX3UoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJkZWZhdWx0IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmbjogZnVuY3Rpb24gKHNjb3BlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygic3BhbiIsIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIiAiICsgX3ZtLl9zKF92bS5qb2JSb3cuam9iTmFtZSkpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG51bGwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxzZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDI0MzMyMjczNAogICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiAi5a2Y5rS75Li75py6IiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlZFNsb3RzOiBfdm0uX3UoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJkZWZhdWx0IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmbjogZnVuY3Rpb24gKHNjb3BlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygic3BhbiIsIFtfdm0uX3YoX3ZtLl9zKF92bS50b3RhbCkpXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBudWxsLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZmFsc2UsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAxMzAwOTk5NDkyCiAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogImlwdjQiLCBsYWJlbDogIuW8seWPo+S7pCIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZWRTbG90czogX3ZtLl91KAogICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAiZGVmYXVsdCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm46IGZ1bmN0aW9uIChzY29wZSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCBbX3ZtLl92KF92bS5fcyhfdm0ud3BUb3RhbCkpXSksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBudWxsLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZmFsc2UsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAyMDA3MTYwMDAzCiAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogInBvY1Jpc2tOdW0iLCBsYWJlbDogIuWPr+WFpeS+tea8j+a0niIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogImhpZ2hSaXNrTnVtIiwgbGFiZWw6ICLpq5jpo47pmanmvI/mtJ4iIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJtaWRkbGVSaXNrTnVtIiwgbGFiZWw6ICLkuK3po47pmanmvI/mtJ4iIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJsb3dSaXNrTnVtIiwgbGFiZWw6ICLkvY7po47pmanmvI/mtJ4iIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICBfdm0uam9iUm93LmpvYlR5cGUgPT09IDIKICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC10YWJsZSIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IHdpZHRoOiAiMTAwJSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBfdm0uZm9ybSwKICAgICAgICAgICAgICAgICAgICAgICAgICAiaGVhZGVyLWNlbGwtc3R5bGUiOiBfdm0uaGVhZGVyQ2VsbFN0eWxlLAogICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogIiIsCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiAi5Lu75Yqh5ZCN56ewIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlZFNsb3RzOiBfdm0uX3UoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJkZWZhdWx0IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmbjogZnVuY3Rpb24gKHNjb3BlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygic3BhbiIsIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIiAiICsgX3ZtLl9zKF92bS5qb2JSb3cuam9iTmFtZSkpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG51bGwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxzZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDI0MzMyMjczNAogICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiAi572R56uZIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlZFNsb3RzOiBfdm0uX3UoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJkZWZhdWx0IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmbjogZnVuY3Rpb24gKHNjb3BlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygiZGl2IiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHNsb3Q6ICJjb250ZW50IiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvbVByb3BzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbm5lckhUTUw6IF92bS5fcyhfdm0uam9iUm93LmlwT3ZlciksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzbG90OiAiY29udGVudCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVsbCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhbHNlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgMTEzNDk1Mzg4NAogICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJoaWdoUmlza051bSIsIGxhYmVsOiAi6auY6aOO6ZmpIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAibWlkZGxlUmlza051bSIsIGxhYmVsOiAi5Lit6aOO6ZmpIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAibG93Umlza051bSIsIGxhYmVsOiAi5L2O6aOO6ZmpIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAiaW5mb1Jpc2tOdW0iLCBsYWJlbDogIuS/oeaBr+mjjumZqSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogInN0YXJ0VGltZSIsIGxhYmVsOiAi5byA5aeL5pe26Ze0IiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAiZW5kVGltZSIsIGxhYmVsOiAi57uT5p2f5pe26Ze0IiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgMQogICAgICAgICAgICApLAogICAgICAgICAgXSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF9jKAogICAgICAgICJlbC1yb3ciLAogICAgICAgIHsgc3RhdGljQ2xhc3M6ICJzX2Rfcm93IiB9LAogICAgICAgIFsKICAgICAgICAgIF9jKCJlbC1jb2wiLCBbCiAgICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogInNfc3Bhbl90YWciIH0sIFtfdm0uX3YoIuS7u+WKoeWPguaVsCIpXSksCiAgICAgICAgICBdKSwKICAgICAgICBdLAogICAgICAgIDEKICAgICAgKSwKICAgICAgX2MoCiAgICAgICAgImVsLXJvdyIsCiAgICAgICAgewogICAgICAgICAgc3RhdGljU3R5bGU6IHsKICAgICAgICAgICAgaGVpZ2h0OiAiNDBweCIsCiAgICAgICAgICAgICJsaW5lLWhlaWdodCI6ICI0MHB4IiwKICAgICAgICAgICAgIm1hcmdpbi10b3AiOiAiMjBweCIsCiAgICAgICAgICAgIGJvcmRlcjogInNvbGlkIDFweCAjZTdlM2UzNjEiLAogICAgICAgICAgfSwKICAgICAgICB9LAogICAgICAgIFsKICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDEyIH0gfSwgWwogICAgICAgICAgICBfYygiZGl2IiwgWwogICAgICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogImJfdGFnIiB9LCBbX3ZtLl92KCLku7vliqHnsbvlnosiKV0pLAogICAgICAgICAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgICAgICAgICAgPyBfYygic3BhbiIsIFtfdm0uX3YoIuWfuuehgOacjeWKoea8j+a0nuaJq+aPjyIpXSkKICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgX3ZtLmpvYlJvdy5qb2JUeXBlID09PSAyCiAgICAgICAgICAgICAgICA/IF9jKCJzcGFuIiwgW192bS5fdigi5Z+656GAV2Vi5ryP5rSe5omr5o+PIildKQogICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgXSksCiAgICAgICAgICBdKSwKICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDEyIH0gfSwgWwogICAgICAgICAgICBfYygiZGl2IiwgWwogICAgICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogImJfdGFnIiB9LCBbX3ZtLl92KCLku7vliqHnm67moIciKV0pLAogICAgICAgICAgICAgIF9jKCJzcGFuIiwgewogICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJ0cnVuY2F0ZSIsCiAgICAgICAgICAgICAgICBhdHRyczogeyBzbG90OiAiY29udGVudCIsIHRpdGxlOiBfdm0uam9iUm93LmlwU2hvdyB9LAogICAgICAgICAgICAgICAgZG9tUHJvcHM6IHsgaW5uZXJIVE1MOiBfdm0uX3MoX3ZtLmpvYlJvdy5pcFNob3cpIH0sCiAgICAgICAgICAgICAgICBzbG90OiAiY29udGVudCIsCiAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgIF0pLAogICAgICAgICAgXSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF9jKAogICAgICAgICJlbC1yb3ciLAogICAgICAgIHsKICAgICAgICAgIHN0YXRpY1N0eWxlOiB7CiAgICAgICAgICAgIGhlaWdodDogIjQwcHgiLAogICAgICAgICAgICAibGluZS1oZWlnaHQiOiAiNDBweCIsCiAgICAgICAgICAgICJtYXJnaW4tdG9wIjogIjFweCIsCiAgICAgICAgICAgIGJvcmRlcjogInNvbGlkIDFweCAjZTdlM2UzNjEiLAogICAgICAgICAgfSwKICAgICAgICB9LAogICAgICAgIFsKICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDEyIH0gfSwgWwogICAgICAgICAgICBfYygKICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJiX3RhZyIgfSwgW192bS5fdigi5Lu75Yqh54q25oCBIildKSwKICAgICAgICAgICAgICAgIF92bS5mb3JtWzBdLnRhc2tUeXBlID09PSAyICYmIF92bS5mb3JtWzBdLnRhc2tTdGF0dXMgPT09IDMKICAgICAgICAgICAgICAgICAgPyBfYygiZWwtdGFnIiwgeyBhdHRyczogeyB0eXBlOiAiZGFuZ2VyIiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi5Lu75Yqh5byC5bi4IiksCiAgICAgICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICAgICAgOiBfdm0uZm9ybVswXS50YXNrVHlwZSA9PT0gMiAmJiBfdm0uZm9ybVswXS50YXNrU3RhdHVzID09PSAxCiAgICAgICAgICAgICAgICAgID8gX2MoImVsLXRhZyIsIFtfdm0uX3YoIuaJq+aPj+S4rSIpXSkKICAgICAgICAgICAgICAgICAgOiBfdm0uZm9ybVswXS50YXNrVHlwZSA9PT0gMiAmJiBfdm0uZm9ybVswXS50YXNrU3RhdHVzID09PSAyCiAgICAgICAgICAgICAgICAgID8gX2MoImVsLXRhZyIsIFtfdm0uX3YoIuaJq+aPj+S4rSIpXSkKICAgICAgICAgICAgICAgICAgOiBfdm0uZm9ybVswXS50YXNrVHlwZSA9PT0gMiAmJiBfdm0uZm9ybVswXS50YXNrU3RhdHVzID09PSA0CiAgICAgICAgICAgICAgICAgID8gX2MoImVsLXRhZyIsIHsgYXR0cnM6IHsgdHlwZTogInN1Y2Nlc3MiIH0gfSwgWwogICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KCLlt7Lmiavmj48iKSwKICAgICAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgICAgICA6IF92bS5mb3JtWzBdLnRhc2tUeXBlID09PSAxICYmIF92bS5mb3JtWzBdLnRhc2tTdGF0dXMgPT09IDMKICAgICAgICAgICAgICAgICAgPyBfYygiZWwtdGFnIiwgeyBhdHRyczogeyB0eXBlOiAiZGFuZ2VyIiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi5Lu75Yqh5byC5bi4IiksCiAgICAgICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICAgICAgOiBfdm0uZm9ybVswXS50YXNrVHlwZSA9PT0gMSAmJiBfdm0uZm9ybVswXS50YXNrU3RhdHVzID09PSA0CiAgICAgICAgICAgICAgICAgID8gX2MoImVsLXRhZyIsIHsgYXR0cnM6IHsgdHlwZTogImRhbmdlciIgfSB9LCBbCiAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIuS7u+WKoee7iOatoiIpLAogICAgICAgICAgICAgICAgICAgIF0pCiAgICAgICAgICAgICAgICAgIDogX3ZtLmZvcm1bMF0udGFza1R5cGUgPT09IDEgJiYgX3ZtLmZvcm1bMF0udGFza1N0YXR1cyA9PT0gMAogICAgICAgICAgICAgICAgICA/IF9jKCJlbC10YWciLCBbX3ZtLl92KCLmiavmj4/kuK0iKV0pCiAgICAgICAgICAgICAgICAgIDogX3ZtLmZvcm1bMF0udGFza1R5cGUgPT09IDEgJiYgX3ZtLmZvcm1bMF0udGFza1N0YXR1cyA9PT0gMQogICAgICAgICAgICAgICAgICA/IF9jKCJlbC10YWciLCBbX3ZtLl92KCLmiavmj4/kuK0iKV0pCiAgICAgICAgICAgICAgICAgIDogX3ZtLmZvcm1bMF0udGFza1R5cGUgPT09IDEgJiYgX3ZtLmZvcm1bMF0udGFza1N0YXR1cyA9PT0gMgogICAgICAgICAgICAgICAgICA/IF9jKCJlbC10YWciLCB7IGF0dHJzOiB7IHR5cGU6ICJzdWNjZXNzIiB9IH0sIFsKICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigi5bey5omr5o+PIiksCiAgICAgICAgICAgICAgICAgICAgXSkKICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgIDEKICAgICAgICAgICAgKSwKICAgICAgICAgIF0pLAogICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogMTIgfSB9LCBbCiAgICAgICAgICAgIF9jKCJkaXYiLCBbCiAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAiYl90YWciIH0sIFtfdm0uX3YoIuW8gOWni+aXtumXtCIpXSksCiAgICAgICAgICAgICAgX2MoInNwYW4iLCBbX3ZtLl92KF92bS5fcyhfdm0uZm9ybVswXS5zdGFydFRpbWUpKV0pLAogICAgICAgICAgICBdKSwKICAgICAgICAgIF0pLAogICAgICAgIF0sCiAgICAgICAgMQogICAgICApLAogICAgICBfYygKICAgICAgICAiZWwtcm93IiwKICAgICAgICB7CiAgICAgICAgICBzdGF0aWNTdHlsZTogewogICAgICAgICAgICBoZWlnaHQ6ICI0MHB4IiwKICAgICAgICAgICAgImxpbmUtaGVpZ2h0IjogIjQwcHgiLAogICAgICAgICAgICAibWFyZ2luLXRvcCI6ICIxcHgiLAogICAgICAgICAgICBib3JkZXI6ICJzb2xpZCAxcHggI2U3ZTNlMzYxIiwKICAgICAgICAgIH0sCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICBfYygiZWwtY29sIiwgeyBhdHRyczogeyBzcGFuOiAxMiB9IH0sIFsKICAgICAgICAgICAgX2MoImRpdiIsIFsKICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJiX3RhZyIgfSwgW192bS5fdigi5o6i5rWL5Y+C5pWwIildKSwKICAgICAgICAgICAgICBfYygic3BhbiIsIFtfdm0uX3YoIiDlv6vpgJ8v5a2Y5rS75o6i5rWL5ZCv55SoL+WFqOi/nuaOpSIpXSksCiAgICAgICAgICAgIF0pLAogICAgICAgICAgXSksCiAgICAgICAgICBfYygiZWwtY29sIiwgeyBhdHRyczogeyBzcGFuOiAxMiB9IH0sIFsKICAgICAgICAgICAgX2MoImRpdiIsIFsKICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJiX3RhZyIgfSwgW192bS5fdigi57uT5p2f5pe26Ze0IildKSwKICAgICAgICAgICAgICBfYygic3BhbiIsIFtfdm0uX3YoX3ZtLl9zKF92bS5mb3JtWzBdLmVuZFRpbWUpKV0pLAogICAgICAgICAgICBdKSwKICAgICAgICAgIF0pLAogICAgICAgIF0sCiAgICAgICAgMQogICAgICApLAogICAgICBfYygKICAgICAgICAiZWwtcm93IiwKICAgICAgICB7CiAgICAgICAgICBzdGF0aWNTdHlsZTogewogICAgICAgICAgICBoZWlnaHQ6ICI0MHB4IiwKICAgICAgICAgICAgImxpbmUtaGVpZ2h0IjogIjQwcHgiLAogICAgICAgICAgICAibWFyZ2luLXRvcCI6ICIxcHgiLAogICAgICAgICAgICBib3JkZXI6ICJzb2xpZCAxcHggI2U3ZTNlMzYxIiwKICAgICAgICAgIH0sCiAgICAgICAgfSwKICAgICAgICBbCiAgICAgICAgICBfYygiZWwtY29sIiwgeyBhdHRyczogeyBzcGFuOiAxMiB9IH0sIFsKICAgICAgICAgICAgX2MoImRpdiIsIFsKICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJiX3RhZyIgfSwgW192bS5fdigi56uv5Y+j6IyD5Zu0IildKSwKICAgICAgICAgICAgICBfYygic3BhbiIsIFtfdm0uX3YoIuW4uOinhOerr+WPoyIpXSksCiAgICAgICAgICAgIF0pLAogICAgICAgICAgXSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgID8gX2MoCiAgICAgICAgICAgICJlbC1yb3ciLAogICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAic19kX3JvdyIgfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCBbCiAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJzX3NwYW5fdGFnIiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdigi5Li75py65ryP5rSe5YiX6KGoIiksCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgXSwKICAgICAgICAgICAgMQogICAgICAgICAgKQogICAgICAgIDogX3ZtLl9lKCksCiAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgID8gX2MoCiAgICAgICAgICAgICJlbC1yb3ciLAogICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7ICJtYXJnaW4tdG9wIjogIjIwcHgiIH0sIGF0dHJzOiB7IGd1dHRlcjogMTAgfSB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogMjQgfSB9LCBbCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJjb2xsYXBzZS1jb250ZW50LWRpdiIgfSwKICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgImVsLXRhYmxlIiwKICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljU3R5bGU6IHsgd2lkdGg6ICIxMDAlIiB9LAogICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IF92bS5qb2JUYXNrTGlzdCwKICAgICAgICAgICAgICAgICAgICAgICAgICAiaGVhZGVyLWNlbGwtc3R5bGUiOiBfdm0uaGVhZGVyQ2VsbFN0eWxlLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAiaW5kZXgiLCBsYWJlbDogIuW6j+WPtyIsIHdpZHRoOiAiNzAiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJpcCIsIGxhYmVsOiAiSVAiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJzeXN0ZW1OYW1lIiwgbGFiZWw6ICLmk43kvZzns7vnu58gICIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogInBvY1Jpc2tOdW0iLCBsYWJlbDogIuWPr+WFpeS+tea8j+a0niIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogImhpZ2hSaXNrTnVtIiwgbGFiZWw6ICLpq5jpo47pmakiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJtaWRkbGVSaXNrTnVtIiwgbGFiZWw6ICLkuK3po47pmakiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJsb3dSaXNrTnVtIiwgbGFiZWw6ICLkvY7po47pmakiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJwd051bSIsIGxhYmVsOiAi5byx5Y+j5Luk5pWw6YePIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAicG9ydE51bSIsIGxhYmVsOiAi5byA5pS+56uv5Y+j5pWw6YePIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICBfYygicGFnaW5hdGlvbiIsIHsKICAgICAgICAgICAgICAgICAgICAgIGRpcmVjdGl2ZXM6IFsKICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICJzaG93IiwKICAgICAgICAgICAgICAgICAgICAgICAgICByYXdOYW1lOiAidi1zaG93IiwKICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogX3ZtLnRvdGFsID4gMCwKICAgICAgICAgICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAidG90YWw+MCIsCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdG90YWw6IF92bS50b3RhbCwKICAgICAgICAgICAgICAgICAgICAgICAgcGFnZTogX3ZtLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0sCiAgICAgICAgICAgICAgICAgICAgICAgIGxpbWl0OiBfdm0ucXVlcnlQYXJhbXMucGFnZVNpemUsCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICAgICAgInVwZGF0ZTpwYWdlIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uJHNldChfdm0ucXVlcnlQYXJhbXMsICJwYWdlTnVtIiwgJGV2ZW50KQogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAidXBkYXRlOmxpbWl0IjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uJHNldChfdm0ucXVlcnlQYXJhbXMsICJwYWdlU2l6ZSIsICRldmVudCkKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgcGFnaW5hdGlvbjogX3ZtLmdldElwTGlzdCwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICkKICAgICAgICA6IF92bS5fZSgpLAogICAgICBfYygKICAgICAgICAiZWwtcm93IiwKICAgICAgICB7IHN0YXRpY0NsYXNzOiAic19kX3JvdyIgfSwKICAgICAgICBbCiAgICAgICAgICBfYygiZWwtY29sIiwgWwogICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJzX3NwYW5fdGFnIiB9LCBbX3ZtLl92KCLmvI/mtJ7liIbluIPliJfooagiKV0pLAogICAgICAgICAgXSksCiAgICAgICAgXSwKICAgICAgICAxCiAgICAgICksCiAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgID8gX2MoCiAgICAgICAgICAgICJlbC1yb3ciLAogICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7ICJtYXJnaW4tdG9wIjogIjIwcHgiIH0sIGF0dHJzOiB7IGd1dHRlcjogMTAgfSB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogMjQgfSB9LCBbCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJjb2xsYXBzZS1jb250ZW50LWRpdiIgfSwKICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgImVsLXRhYmxlIiwKICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgZGlyZWN0aXZlczogWwogICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICJsb2FkaW5nIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJhd05hbWU6ICJ2LWxvYWRpbmciLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IF92bS5sb2FkaW5nLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwcmVzc2lvbjogImxvYWRpbmciLAogICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IHdpZHRoOiAiMTAwJSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBfdm0uc2Nhbkxpc3QsCiAgICAgICAgICAgICAgICAgICAgICAgICAgImhlYWRlci1jZWxsLXN0eWxlIjogX3ZtLmhlYWRlckNlbGxTdHlsZSwKICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgdHlwZTogImV4cGFuZCIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGVkU2xvdHM6IF92bS5fdSgKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogImRlZmF1bHQiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmbjogZnVuY3Rpb24gKHByb3BzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtZGVzY3JpcHRpb25zIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2x1bW46IDEsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICIiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImxhYmVsLXN0eWxlIjogX3ZtLmxzLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJlbC1kZXNjcmlwdGlvbnMtaXRlbSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IGxhYmVsOiAi5Y+X5b2x5ZONSVAiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoX3ZtLl9zKHByb3BzLnJvdy5ob3N0SXApKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWRlc2NyaXB0aW9ucy1pdGVtIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgbGFiZWw6ICLlj5flvbHlk43nq6/lj6MiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl9zKHByb3BzLnJvdy5ob3N0UG9ydCkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWRlc2NyaXB0aW9ucy1pdGVtIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuWPl+W9seWTjeeJiOacrOS/oeaBryIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl9zKHByb3BzLnJvdy52ZXJzaW9uSW5mbykKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWRlc2NyaXB0aW9ucy1pdGVtIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogIua8j+a0nuWPkeW4g+aXtumXtCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl9zKHByb3BzLnJvdy5wdWJsaXNoRGF0ZSkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWRlc2NyaXB0aW9ucy1pdGVtIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgbGFiZWw6ICLlqIHog4HnsbvlnosiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fcyhwcm9wcy5yb3cudnVsblR5cGUpCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJlbC1kZXNjcmlwdGlvbnMtaXRlbSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IGxhYmVsOiAi5ryP5rSe566A5LuLIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiICIgKwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl9zKHByb3BzLnJvdy52dWxuSW5mbykgKwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIiAiCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJlbC1kZXNjcmlwdGlvbnMtaXRlbSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IGxhYmVsOiAi6Kej5Yaz5pa55qGIIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJzcGFuIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRvbVByb3BzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbm5lckhUTUw6IF92bS5fcygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcHMucm93LnZ1bG5Tb2x2ZQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBudWxsLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhbHNlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDMyNTc3MjM1MTUKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyB0eXBlOiAiaW5kZXgiLCBsYWJlbDogIuW6j+WPtyIsIHdpZHRoOiAiNzAiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJ2dWxuTmFtZSIsIGxhYmVsOiAi5ryP5rSe5ZCN56ewIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlZFNsb3RzOiBfdm0uX3UoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6ICJkZWZhdWx0IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmbjogZnVuY3Rpb24gKHNjb3BlKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cucmlza0xldmVsID09IDEKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAic3BhbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgY29sb3I6ICIjMTNjZTY2IiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoX3ZtLl9zKHNjb3BlLnJvdy52dWxuTmFtZSkpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlLnJvdy5yaXNrTGV2ZWwgPT0gMgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBjb2xvcjogIiNmZmJhMDAiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdihfdm0uX3Moc2NvcGUucm93LnZ1bG5OYW1lKSldCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LnJpc2tMZXZlbCA9PSAzCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgInNwYW4iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7IGNvbG9yOiAiI2ZmNDk0OSIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KF92bS5fcyhzY29wZS5yb3cudnVsbk5hbWUpKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cucmlza0xldmVsID09IDQKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAic3BhbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgY29sb3I6ICIjZmY0OTQ5IiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoX3ZtLl9zKHNjb3BlLnJvdy52dWxuTmFtZSkpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBudWxsLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZmFsc2UsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAyMjM2NjcxMzgzCiAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogInZ1bG5UeXBlIiwgbGFiZWw6ICLmvI/mtJ7nsbvlnosiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJyaXNrTGV2ZWwiLCBsYWJlbDogIumjjumZqee6p+WIqyIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZWRTbG90czogX3ZtLl91KAogICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAiZGVmYXVsdCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm46IGZ1bmN0aW9uIChzY29wZSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LnJpc2tMZXZlbCA9PSAxCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLXRhZyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgdHlwZTogInN1Y2Nlc3MiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigi5L2O6aOO6ZmpIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LnJpc2tMZXZlbCA9PSAyCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLXRhZyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgdHlwZTogIndhcm5pbmciIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigi5Lit6aOO6ZmpIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LnJpc2tMZXZlbCA9PSAzCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLXRhZyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgdHlwZTogImRhbmdlciIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLpq5jpo47pmakiKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cucmlza0xldmVsID09IDQKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtdGFnIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBhdHRyczogeyB0eXBlOiAiZGFuZ2VyIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIuS4pemHjSIpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBudWxsLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgZmFsc2UsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAyNjIyNjEwNDQ1CiAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgX2MoInBhZ2luYXRpb24iLCB7CiAgICAgICAgICAgICAgICAgICAgICBkaXJlY3RpdmVzOiBbCiAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAic2hvdyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgcmF3TmFtZTogInYtc2hvdyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IF92bS5zY2FuVG90YWwgPiAwLAogICAgICAgICAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJzY2FuVG90YWw+MCIsCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdG90YWw6IF92bS5zY2FuVG90YWwsCiAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2U6IF92bS5xdWVyeVNjYW5QYXJhbXMucGFnZU51bSwKICAgICAgICAgICAgICAgICAgICAgICAgbGltaXQ6IF92bS5xdWVyeVNjYW5QYXJhbXMucGFnZVNpemUsCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICAgICAgInVwZGF0ZTpwYWdlIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uJHNldCgKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5xdWVyeVNjYW5QYXJhbXMsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAicGFnZU51bSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAkZXZlbnQKICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICJ1cGRhdGU6bGltaXQiOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF92bS4kc2V0KAogICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLnF1ZXJ5U2NhblBhcmFtcywKICAgICAgICAgICAgICAgICAgICAgICAgICAgICJwYWdlU2l6ZSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAkZXZlbnQKICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2luYXRpb246IF92bS5nZXRTY2FuTGlzdCwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICkKICAgICAgICA6IF92bS5fZSgpLAogICAgICBfdm0uam9iUm93LmpvYlR5cGUgPT09IDIKICAgICAgICA/IF9jKAogICAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyAibWFyZ2luLXRvcCI6ICIyMHB4IiB9LCBhdHRyczogeyBndXR0ZXI6IDEwIH0gfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDI0IH0gfSwgWwogICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiY29sbGFwc2UtY29udGVudC1kaXYiIH0sCiAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC10YWJsZSIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIGRpcmVjdGl2ZXM6IFsKICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAibG9hZGluZyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICByYXdOYW1lOiAidi1sb2FkaW5nIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0ubG9hZGluZywKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJsb2FkaW5nIiwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEwMCUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogX3ZtLnNjYW5MaXN0LAogICAgICAgICAgICAgICAgICAgICAgICAgICJoZWFkZXItY2VsbC1zdHlsZSI6IF92bS5oZWFkZXJDZWxsU3R5bGUsCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJleHBhbmQiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGVkU2xvdHM6IF92bS5fdSgKICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogImRlZmF1bHQiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZuOiBmdW5jdGlvbiAocHJvcHMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJlbC1kZXNjcmlwdGlvbnMiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbHVtbjogMSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAiIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImxhYmVsLXN0eWxlIjogX3ZtLmxzLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtZGVzY3JpcHRpb25zLWl0ZW0iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IGxhYmVsOiAi5ryP5rSe5o+P6L+wIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3MocHJvcHMucm93LmRlc2NyaXB0aW9uKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWRlc2NyaXB0aW9ucy1pdGVtIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBhdHRyczogeyBsYWJlbDogIua8j+a0nuWNseWusyIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KF92bS5fcyhwcm9wcy5yb3cuaW1wYWN0KSldCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJlbC1kZXNjcmlwdGlvbnMtaXRlbSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgbGFiZWw6ICLmvI/mtJ7op6PlhrPmlrnmoYgiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fcyhwcm9wcy5yb3cucmVjb21tZW5kYXRpb24pCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtZGVzY3JpcHRpb25zLWl0ZW0iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IGxhYmVsOiAi5omr5o+P55uu5qCHIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoX3ZtLl9zKHByb3BzLnJvdy51cmwpKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLWRlc2NyaXB0aW9ucy1pdGVtIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBhdHRyczogeyBsYWJlbDogIua8j+a0nuaehOmAoOivt+axgiIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZToKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcHMucm93LmV2aWRlbmNlLmluY2x1ZGVzKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJIb3N0IgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB7IHdoaXRlU3BhY2U6ICJwcmUtd3JhcCIgfQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogeyB3aGl0ZVNwYWNlOiAibm9ybWFsIiB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZG9tUHJvcHM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5uZXJIVE1MOiBfdm0uX3MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcHMucm93LmV2aWRlbmNlCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVsbCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhbHNlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgMTc1ODE0OTIxOAogICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHR5cGU6ICJpbmRleCIsIGxhYmVsOiAi5bqP5Y+3Iiwgd2lkdGg6ICI3MCIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogInZ1bG5OYW1lIiwgbGFiZWw6ICLmvI/mtJ7lkI3np7AiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGVkU2xvdHM6IF92bS5fdSgKICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogImRlZmF1bHQiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZuOiBmdW5jdGlvbiAoc2NvcGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlLnJvdy5zZXZlcml0eSA9PSAxCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgInNwYW4iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7IGNvbG9yOiAiIzE4OTBmZiIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KF92bS5fcyhzY29wZS5yb3cudnVsbk5hbWUpKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cuc2V2ZXJpdHkgPT0gMgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBjb2xvcjogIiMxM2NlNjYiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdihfdm0uX3Moc2NvcGUucm93LnZ1bG5OYW1lKSldCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LnNldmVyaXR5ID09IDMKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAic3BhbiIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgY29sb3I6ICIjZmZiYTAwIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoX3ZtLl9zKHNjb3BlLnJvdy52dWxuTmFtZSkpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlLnJvdy5zZXZlcml0eSA9PSA0CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgInNwYW4iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7IGNvbG9yOiAiI2ZmNDk0OSIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KF92bS5fcyhzY29wZS5yb3cudnVsbk5hbWUpKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cuc2V2ZXJpdHkgPT0gMAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBjb2xvcjogIiNmZjQ5NDkiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdihfdm0uX3Moc2NvcGUucm93LnZ1bG5OYW1lKSldCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG51bGwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxzZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDExODM5NDIwODIKICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAidnVsblR5cGUiLCBsYWJlbDogIua8j+a0nuexu+WeiyAgIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAic2V2ZXJpdHkiLCBsYWJlbDogIumjjumZqee6p+WIqyIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZWRTbG90czogX3ZtLl91KAogICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAiZGVmYXVsdCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm46IGZ1bmN0aW9uIChzY29wZSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LnNldmVyaXR5ID09IDEKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtdGFnIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBhdHRyczogeyB0eXBlOiAicHJpbWFyeSIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLkv6Hmga/po47pmakiKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cuc2V2ZXJpdHkgPT0gMgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJlbC10YWciLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IGF0dHJzOiB7IHR5cGU6ICJzdWNjZXNzIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIuS9jumjjumZqSIpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlLnJvdy5zZXZlcml0eSA9PSAzCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLXRhZyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgdHlwZTogIndhcm5pbmciIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigi5Lit6aOO6ZmpIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LnNldmVyaXR5ID09IDQKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IF9jKAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAiZWwtdGFnIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBhdHRyczogeyB0eXBlOiAiZGFuZ2VyIiB9IH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtfdm0uX3YoIumrmOmjjumZqSIpXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogX3ZtLl9lKCksCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlLnJvdy5zZXZlcml0eSA9PSAwCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgImVsLXRhZyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsgYXR0cnM6IHsgdHlwZTogImRhbmdlciIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLkv6Hmga/po47pmakiKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVsbCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhbHNlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgMjkzODc0NDAwNAogICAgICAgICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgIF9jKCJwYWdpbmF0aW9uIiwgewogICAgICAgICAgICAgICAgICAgICAgZGlyZWN0aXZlczogWwogICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZTogInNob3ciLAogICAgICAgICAgICAgICAgICAgICAgICAgIHJhd05hbWU6ICJ2LXNob3ciLAogICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBfdm0uc2NhblRvdGFsID4gMCwKICAgICAgICAgICAgICAgICAgICAgICAgICBleHByZXNzaW9uOiAic2NhblRvdGFsPjAiLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRvdGFsOiBfdm0uc2NhblRvdGFsLAogICAgICAgICAgICAgICAgICAgICAgICBwYWdlOiBfdm0ucXVlcnlTY2FuUGFyYW1zLnBhZ2VOdW0sCiAgICAgICAgICAgICAgICAgICAgICAgIGxpbWl0OiBfdm0ucXVlcnlTY2FuUGFyYW1zLnBhZ2VTaXplLAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICJ1cGRhdGU6cGFnZSI6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLiRzZXQoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdm0ucXVlcnlTY2FuUGFyYW1zLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgInBhZ2VOdW0iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgJGV2ZW50CiAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAidXBkYXRlOmxpbWl0IjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uJHNldCgKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5xdWVyeVNjYW5QYXJhbXMsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAicGFnZVNpemUiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgJGV2ZW50CiAgICAgICAgICAgICAgICAgICAgICAgICAgKQogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICBwYWdpbmF0aW9uOiBfdm0uZ2V0U2Nhbkxpc3QsCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApCiAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgX3ZtLmpvYlJvdy5qb2JUeXBlID09PSAxCiAgICAgICAgPyBfYygKICAgICAgICAgICAgImVsLXJvdyIsCiAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJzX2Rfcm93IiB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoImVsLWNvbCIsIFsKICAgICAgICAgICAgICAgIF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogInNfc3Bhbl90YWciIH0sIFsKICAgICAgICAgICAgICAgICAgX3ZtLl92KCLlvLHlj6Pku6TliJfooagiKSwKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApCiAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgX3ZtLmpvYlJvdy5qb2JUeXBlID09PSAxCiAgICAgICAgPyBfYygKICAgICAgICAgICAgImVsLXJvdyIsCiAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgIm1hcmdpbi10b3AiOiAiMjBweCIgfSwgYXR0cnM6IHsgZ3V0dGVyOiAxMCB9IH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygiZWwtY29sIiwgeyBhdHRyczogeyBzcGFuOiAyNCB9IH0sIFsKICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogImNvbGxhcHNlLWNvbnRlbnQtZGl2IiB9LAogICAgICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICAgICAiZWwtdGFibGUiLAogICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICBzdGF0aWNTdHlsZTogeyB3aWR0aDogIjEwMCUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogX3ZtLndwUmVzdWx0TGlzdCwKICAgICAgICAgICAgICAgICAgICAgICAgICAiaGVhZGVyLWNlbGwtc3R5bGUiOiBfdm0uaGVhZGVyQ2VsbFN0eWxlLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogImluZGV4IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5bqP5Y+3IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAiNzBweCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogImhvc3RJcCIsIGxhYmVsOiAiSVAiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJzZXJ2aWNlVHlwZSIsIGxhYmVsOiAi5pyN5Yqh57G75Z6LICAiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJob3N0UG9ydCIsIGxhYmVsOiAi56uv5Y+jIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAidXNlcm5hbWUiLCBsYWJlbDogIueUqOaIt+WQjSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogIndlYWtQYXNzd29yZCIsIGxhYmVsOiAi5a+G56CBIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICBfYygicGFnaW5hdGlvbiIsIHsKICAgICAgICAgICAgICAgICAgICAgIGRpcmVjdGl2ZXM6IFsKICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICJzaG93IiwKICAgICAgICAgICAgICAgICAgICAgICAgICByYXdOYW1lOiAidi1zaG93IiwKICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogX3ZtLndwVG90YWwgPiAwLAogICAgICAgICAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJ3cFRvdGFsPjAiLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgIHRvdGFsOiBfdm0ud3BUb3RhbCwKICAgICAgICAgICAgICAgICAgICAgICAgcGFnZTogX3ZtLnF1ZXJ5V3BQYXJhbXMucGFnZU51bSwKICAgICAgICAgICAgICAgICAgICAgICAgbGltaXQ6IF92bS5xdWVyeVdwUGFyYW1zLnBhZ2VTaXplLAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICJ1cGRhdGU6cGFnZSI6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLiRzZXQoX3ZtLnF1ZXJ5V3BQYXJhbXMsICJwYWdlTnVtIiwgJGV2ZW50KQogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAidXBkYXRlOmxpbWl0IjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uJHNldChfdm0ucXVlcnlXcFBhcmFtcywgInBhZ2VTaXplIiwgJGV2ZW50KQogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICBwYWdpbmF0aW9uOiBfdm0uZ2V0V3ByZXN1bHRMaXN0LAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgXSwKICAgICAgICAgICAgMQogICAgICAgICAgKQogICAgICAgIDogX3ZtLl9lKCksCiAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgID8gX2MoCiAgICAgICAgICAgICJlbC1yb3ciLAogICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAic19kX3JvdyIgfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCBbCiAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJzX3NwYW5fdGFnIiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdigi5byA5pS+56uv5Y+j5YiX6KGoIiksCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgXSwKICAgICAgICAgICAgMQogICAgICAgICAgKQogICAgICAgIDogX3ZtLl9lKCksCiAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgID8gX2MoCiAgICAgICAgICAgICJlbC1yb3ciLAogICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7ICJtYXJnaW4tdG9wIjogIjIwcHgiIH0sIGF0dHJzOiB7IGd1dHRlcjogMTAgfSB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogMjQgfSB9LCBbCiAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJjb2xsYXBzZS1jb250ZW50LWRpdiIgfSwKICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgIF9jKAogICAgICAgICAgICAgICAgICAgICAgImVsLXRhYmxlIiwKICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgc3RhdGljU3R5bGU6IHsgd2lkdGg6ICIxMDAlIiB9LAogICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IF92bS5wb3J0UmVzdWx0TGlzdCwKICAgICAgICAgICAgICAgICAgICAgICAgICAiaGVhZGVyLWNlbGwtc3R5bGUiOiBfdm0uaGVhZGVyQ2VsbFN0eWxlLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogImluZGV4IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5bqP5Y+3IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAiNzBweCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogImhvc3RJcCIsIGxhYmVsOiAiSVAiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJob3N0UG9ydCIsIGxhYmVsOiAi56uv5Y+jICAiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJwcm9jb3RvbCIsIGxhYmVsOiAi5Lyg6L6T5Y2P6K6uIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAic2VydmljZU5hbWUiLCBsYWJlbDogIuacjeWKoeWQjeensCIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogInByb2R1Y3QiLCBsYWJlbDogIuacjeWKoeS4jueJiOacrCIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgICAgX2MoInBhZ2luYXRpb24iLCB7CiAgICAgICAgICAgICAgICAgICAgICBkaXJlY3RpdmVzOiBbCiAgICAgICAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiAic2hvdyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgcmF3TmFtZTogInYtc2hvdyIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IF92bS5wb3J0VG90YWwgPiAwLAogICAgICAgICAgICAgICAgICAgICAgICAgIGV4cHJlc3Npb246ICJwb3J0VG90YWw+MCIsCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgdG90YWw6IF92bS5wb3J0VG90YWwsCiAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2U6IF92bS5xdWVyeVBvcnRQYXJhbXMucGFnZU51bSwKICAgICAgICAgICAgICAgICAgICAgICAgbGltaXQ6IF92bS5xdWVyeVBvcnRQYXJhbXMucGFnZVNpemUsCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgICAgICAgInVwZGF0ZTpwYWdlIjogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uJHNldCgKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF92bS5xdWVyeVBvcnRQYXJhbXMsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAicGFnZU51bSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAkZXZlbnQKICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICJ1cGRhdGU6bGltaXQiOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF92bS4kc2V0KAogICAgICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLnF1ZXJ5UG9ydFBhcmFtcywKICAgICAgICAgICAgICAgICAgICAgICAgICAgICJwYWdlU2l6ZSIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAkZXZlbnQKICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2luYXRpb246IF92bS5nZXRQb3J0cmVzdWx0TGlzdCwKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICkKICAgICAgICA6IF92bS5fZSgpLAogICAgICBfdm0uam9iUm93LmpvYlR5cGUgPT09IDIKICAgICAgICA/IF9jKAogICAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogInNfZF9yb3ciIH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygiZWwtY29sIiwgWwogICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAic19zcGFuX3RhZyIgfSwgWwogICAgICAgICAgICAgICAgICBfdm0uX3YoIumZhOW9lS3mvI/mtJ7nrYnnuqfpo47pmanor7TmmI4iKSwKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApCiAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgX3ZtLmpvYlJvdy5qb2JUeXBlID09PSAyCiAgICAgICAgPyBfYygKICAgICAgICAgICAgImVsLXJvdyIsCiAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgIm1hcmdpbi10b3AiOiAiMjBweCIgfSwgYXR0cnM6IHsgZ3V0dGVyOiAxMCB9IH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygiZWwtY29sIiwgeyBhdHRyczogeyBzcGFuOiAyNCB9IH0sIFsKICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiY29sbGFwc2UtY29udGVudC1kaXYiIH0sIFsKICAgICAgICAgICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAgICAgICAgICIg5ryP5rSe5piv5LiO5L+h5oGv6LWE5Lqn5pyJ5YWz55qE5byx54K55oiW5a6J5YWo6ZqQ5oKj44CC5ryP5rSe5pys6Lqr5bm25LiN5a+56LWE5Lqn5p6E5oiQ5Y2x5a6z77yM5L2G5piv5Zyo5LiA5a6a5p2h5Lu25b6X5Yiw5ruh5piv5pe277yM5ryP5rSe5Lya6KKr5aiB6IOB5Yqg5Lul5Yip55So5p2l5a+55L+h5oGv6LWE5Lqn6YCg5oiQ5Y2x6ZmpIeacrOaKpeWRiueahOa8j+a0nuWFseWIhuS6huS7peS4izTnp43mvI/mtJ7po47pmannrYnnuqfjgIIgIgogICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICkKICAgICAgICA6IF92bS5fZSgpLAogICAgICBfdm0uam9iUm93LmpvYlR5cGUgPT09IDIKICAgICAgICA/IF9jKAogICAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyAibWFyZ2luLXRvcCI6ICIyMHB4IiB9LCBhdHRyczogeyBndXR0ZXI6IDEwIH0gfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDI0IH0gfSwgWwogICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiY29sbGFwc2UtY29udGVudC1kaXYiIH0sCiAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC10YWJsZSIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IHdpZHRoOiAiMTAwJSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBfdm0ubGV2ZWxMaXN0LAogICAgICAgICAgICAgICAgICAgICAgICAgICJoZWFkZXItY2VsbC1zdHlsZSI6IF92bS5oZWFkZXJDZWxsU3R5bGUsCiAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9wOiAibGFiZWwiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLljbHpmannqIvluqYiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICIxMjBweCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZWRTbG90czogX3ZtLl91KAogICAgICAgICAgICAgICAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiAiZGVmYXVsdCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm46IGZ1bmN0aW9uIChzY29wZSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LmxhYmVsID09PSAi6auYIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBjb2xvcjogIiNmZjQ5NDkiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigi6auYIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LmxhYmVsID09PSAi5LitIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBjb2xvcjogIiNmZmJhMDAiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigi5LitIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LmxhYmVsID09PSAi5L2OIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBjb2xvcjogIiMxM2NlNjYiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigi5L2OIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LmxhYmVsID09PSAi6aOO6Zmp5L+h5oGvIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBjb2xvcjogIiMxODkwZmYiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigi6aOO6Zmp5L+h5oGvIildCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIG51bGwsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmYWxzZSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIDI4MjQ2NTYwMTkKICAgICAgICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX3ZtLl92KCI+ICIpLAogICAgICAgICAgICAgICAgICAgICAgICBfYygiZWwtdGFibGUtY29sdW1uIiwgewogICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJzOiB7IHByb3A6ICJ2YWx1ZSIsIGxhYmVsOiAi5Y2x6Zmp56iL5bqm6K+05piOIiB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgXSwKICAgICAgICAgICAgMQogICAgICAgICAgKQogICAgICAgIDogX3ZtLl9lKCksCiAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgID8gX2MoCiAgICAgICAgICAgICJlbC1yb3ciLAogICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAic19kX3JvdyIgfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCBbCiAgICAgICAgICAgICAgICBfYygic3BhbiIsIHsgc3RhdGljQ2xhc3M6ICJzX3NwYW5fdGFnIiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdigi6ZmE5b2VMS3mvI/mtJ7nrYnnuqfpo47pmanor7TmmI4iKSwKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApCiAgICAgICAgOiBfdm0uX2UoKSwKICAgICAgX3ZtLmpvYlJvdy5qb2JUeXBlID09PSAxCiAgICAgICAgPyBfYygKICAgICAgICAgICAgImVsLXJvdyIsCiAgICAgICAgICAgIHsgc3RhdGljU3R5bGU6IHsgIm1hcmdpbi10b3AiOiAiMjBweCIgfSwgYXR0cnM6IHsgZ3V0dGVyOiAxMCB9IH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygiZWwtY29sIiwgeyBhdHRyczogeyBzcGFuOiAyNCB9IH0sIFsKICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiY29sbGFwc2UtY29udGVudC1kaXYiIH0sIFsKICAgICAgICAgICAgICAgICAgX3ZtLl92KAogICAgICAgICAgICAgICAgICAgICIg5ryP5rSe5piv5LiO5L+h5oGv6LWE5Lqn5pyJ5YWz55qE5byx54K55oiW5a6J5YWo6ZqQ5oKj44CC5ryP5rSe5pys6Lqr5bm25LiN5a+56LWE5Lqn5p6E5oiQ5Y2x5a6z77yM5L2G5piv5Zyo5LiA5a6a5p2h5Lu25b6X5Yiw5ruh5piv5pe277yM5ryP5rSe5Lya6KKr5aiB6IOB5Yqg5Lul5Yip55So5p2l5a+55L+h5oGv6LWE5Lqn6YCg5oiQ5Y2x6ZmpIeacrOaKpeWRiueahOa8j+a0nuWFseWIhuS6huS7peS4izTnp43mvI/mtJ7po47pmannrYnnuqfjgIIgIgogICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICkKICAgICAgICA6IF92bS5fZSgpLAogICAgICBfdm0uam9iUm93LmpvYlR5cGUgPT09IDEKICAgICAgICA/IF9jKAogICAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyAibWFyZ2luLXRvcCI6ICIyMHB4IiB9LCBhdHRyczogeyBndXR0ZXI6IDEwIH0gfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDI0IH0gfSwgWwogICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiY29sbGFwc2UtY29udGVudC1kaXYiIH0sCiAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC10YWJsZSIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IHdpZHRoOiAiMTAwJSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBfdm0uaG9zdExldmVsTGlzdCwKICAgICAgICAgICAgICAgICAgICAgICAgICAiaGVhZGVyLWNlbGwtc3R5bGUiOiBfdm0uaGVhZGVyQ2VsbFN0eWxlLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcDogImxhYmVsIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5Y2x6Zmp56iL5bqmIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAiMTYwcHgiLAogICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgc2NvcGVkU2xvdHM6IF92bS5fdSgKICAgICAgICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogImRlZmF1bHQiLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZuOiBmdW5jdGlvbiAoc2NvcGUpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBbCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNjb3BlLnJvdy5sYWJlbCA9PT0gIuWPr+WFpeS+tea8j+a0nijkuKXph40pIgogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gX2MoCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJzcGFuIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyBjb2xvcjogIiNmZjQ5NDkiIH0gfSwKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW192bS5fdigi5Y+v5YWl5L615ryP5rSeKOS4pemHjSkiKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cubGFiZWwgPT09ICLpq5giCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgInNwYW4iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7IGNvbG9yOiAiI2ZmNDk0OSIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLpq5giKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cubGFiZWwgPT09ICLkuK0iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgInNwYW4iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7IGNvbG9yOiAiI2ZmYmEwMCIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLkuK0iKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzY29wZS5yb3cubGFiZWwgPT09ICLkvY4iCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfYygKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgInNwYW4iLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7IGNvbG9yOiAiIzEzY2U2NiIgfSB9LAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbX3ZtLl92KCLkvY4iKV0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IF92bS5fZSgpLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgbnVsbCwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZhbHNlLAogICAgICAgICAgICAgICAgICAgICAgICAgICAgNTI3ODY3Mzc5CiAgICAgICAgICAgICAgICAgICAgICAgICAgKSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF92bS5fdigiPiAiKSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogeyBwcm9wOiAidmFsdWUiLCBsYWJlbDogIuWNsemZqeeoi+W6puivtOaYjiIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAgICAgMQogICAgICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgICAgIF0sCiAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICkKICAgICAgICA6IF92bS5fZSgpLAogICAgICBfdm0uam9iUm93LmpvYlR5cGUgPT09IDEKICAgICAgICA/IF9jKAogICAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgICAgeyBzdGF0aWNDbGFzczogInNfZF9yb3ciIH0sCiAgICAgICAgICAgIFsKICAgICAgICAgICAgICBfYygiZWwtY29sIiwgWwogICAgICAgICAgICAgICAgX2MoInNwYW4iLCB7IHN0YXRpY0NsYXNzOiAic19zcGFuX3RhZyIgfSwgWwogICAgICAgICAgICAgICAgICBfdm0uX3YoIumZhOW9lTIt5Li75py65ryP5rSe5Yqg5Zu6562W55WlIiksCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgXSwKICAgICAgICAgICAgMQogICAgICAgICAgKQogICAgICAgIDogX3ZtLl9lKCksCiAgICAgIF92bS5qb2JSb3cuam9iVHlwZSA9PT0gMQogICAgICAgID8gX2MoCiAgICAgICAgICAgICJlbC1yb3ciLAogICAgICAgICAgICB7IHN0YXRpY1N0eWxlOiB7ICJtYXJnaW4tdG9wIjogIjIwcHgiIH0sIGF0dHJzOiB7IGd1dHRlcjogMTAgfSB9LAogICAgICAgICAgICBbCiAgICAgICAgICAgICAgX2MoImVsLWNvbCIsIHsgYXR0cnM6IHsgc3BhbjogMjQgfSB9LCBbCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImNvbGxhcHNlLWNvbnRlbnQtZGl2IiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdigKICAgICAgICAgICAgICAgICAgICAiIOWfuuS6juWkmuW5tOeahOWuieWFqOaVtOaUuee7j+mqjO+8jOaPkOS+m+S6huS7peS4izXnp43lronlhajliqDlm7rmlbTmlLnnrZbnlaXvvIzlubblr7nkuI3lkIzmlbTmlLnlu7rorq7nmoTmnInmlYjnmoTpmLLmiqTluqbjgIHmlbTmlLnlm7Dpmr7luqbkvZzkuobor4Tnuqflj4LogIPvvIzmnInmlYjpmLLmiqTluqbotorpq5jliJnooajnpLrliqDlm7rmlYjmnpzotorlpb3vvIzmlbTmlLnlm7Dpmr7luqbotorpq5jliJnooajnpLrmlbTmlLnmlrnmoYjlrp7mlr3otorpmr7jgILmgqjlj6/ku6XmoLnmja7lrp7pmYXnmoTkuJrliqHmg4XlhrXvvIzlj4LogIPku6XkuIvooajvvIzpgInmi6nliqDlm7rmlbTmlLnnrZbnlaXjgIIgIgogICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIDEKICAgICAgICAgICkKICAgICAgICA6IF92bS5fZSgpLAogICAgICBfdm0uam9iUm93LmpvYlR5cGUgPT09IDEKICAgICAgICA/IF9jKAogICAgICAgICAgICAiZWwtcm93IiwKICAgICAgICAgICAgeyBzdGF0aWNTdHlsZTogeyAibWFyZ2luLXRvcCI6ICIyMHB4IiB9LCBhdHRyczogeyBndXR0ZXI6IDEwIH0gfSwKICAgICAgICAgICAgWwogICAgICAgICAgICAgIF9jKCJlbC1jb2wiLCB7IGF0dHJzOiB7IHNwYW46IDI0IH0gfSwgWwogICAgICAgICAgICAgICAgX2MoCiAgICAgICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAiY29sbGFwc2UtY29udGVudC1kaXYiIH0sCiAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICBfYygKICAgICAgICAgICAgICAgICAgICAgICJlbC10YWJsZSIsCiAgICAgICAgICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXRpY1N0eWxlOiB7IHdpZHRoOiAiMTAwJSIgfSwKICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBfdm0uaG9zdFN0cmF0ZWd5TGlzdCwKICAgICAgICAgICAgICAgICAgICAgICAgICAiaGVhZGVyLWNlbGwtc3R5bGUiOiBfdm0uaGVhZGVyQ2VsbFN0eWxlLAogICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogImluZGV4IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5bqP5Y+3IiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAiODBweCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsgcHJvcDogInZhbHVlIiwgbGFiZWw6ICLliqDlm7rmlbTmlLnnrZbnlaUiIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgICBfdm0uX3YoIj4gIiksCiAgICAgICAgICAgICAgICAgICAgICAgIF9jKCJlbC10YWJsZS1jb2x1bW4iLCB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb3A6ICJsYWJlbCIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogIuacieaViOmYsuaKpOW6piIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbjogImNlbnRlciIsCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogIjEzMHB4IiwKICAgICAgICAgICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgICAgICAgICB9KSwKICAgICAgICAgICAgICAgICAgICAgICAgX2MoImVsLXRhYmxlLWNvbHVtbiIsIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyczogewogICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvcDogImxldmVsIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5pW05pS55Zuw6Zq+5bqmIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduOiAiY2VudGVyIiwKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAiMTMwcHgiLAogICAgICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgICAgIH0pLAogICAgICAgICAgICAgICAgICAgICAgXSwKICAgICAgICAgICAgICAgICAgICAgIDEKICAgICAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgICAgICBdLAogICAgICAgICAgICAgICAgICAxCiAgICAgICAgICAgICAgICApLAogICAgICAgICAgICAgIF0pLAogICAgICAgICAgICBdLAogICAgICAgICAgICAxCiAgICAgICAgICApCiAgICAgICAgOiBfdm0uX2UoKSwKICAgIF0sCiAgICAxCiAgKQp9CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXQpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWUKCmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH0="}]}