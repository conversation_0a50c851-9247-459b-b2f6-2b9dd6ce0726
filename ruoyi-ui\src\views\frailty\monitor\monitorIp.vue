<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          :model="queryParams"
          ref="queryForm"
          size="small"
          :inline="true"
          label-position="right"
          label-width="70px"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="任务名称" prop="jobName">
                <el-input
                  v-model="queryParams.jobName"
                  placeholder="请输入任务名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="扫描目标" prop="invokeTarget">
                <el-input
                  v-model="queryParams.invokeTarget"
                  placeholder="扫描目标"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="任务状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable>
                  <el-option
                    v-for="dict in dict.type.sys_job_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!--<el-col :span="6">-->
              <!--<el-form-item label="执行策略" prop="jobType">-->
                <!--<el-select v-model="queryParams.jobType" placeholder="请选择执行策略" clearable>-->
                  <!--<el-option label="资产扫描监控" :value="0"/>-->
                  <!--<el-option label="基础服务漏洞扫描" :value="1"/>-->
                  <!--<el-option label="基础Web漏洞扫描" :value="2"/>-->
                  <!--<el-option label="主机资产探测" :value="3"/>-->
                <!--</el-select>-->
              <!--</el-form-item>-->
            <!--</el-col>-->
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button class="btn1" size="small" @click="handleQuery">查询</el-button>
                <el-button class="btn2" size="small" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">主机漏扫任务列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleScan"
                  v-hasPermi="['monitor:ipschedule:add']"
                >新增
                </el-button>
              </el-col>
<!--              <el-col :span="1.5">-->
<!--                <el-button-->
<!--                  class="btn1"-->
<!--                  size="small"-->
<!--                  :disabled="single"-->
<!--                  @click="handleUpdate"-->
<!--                  v-hasPermi="['monitor:schedule:edit']"-->
<!--                >修改-->
<!--                </el-button>-->
<!--              </el-col>-->
              <el-col :span="1.5">
                <el-button
                  class="btn1"
                  size="small"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['monitor:ipschedule:remove']"
                >批量删除
                </el-button>
              </el-col>
              <!--            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>-->
            </el-row>
          </div>
        </div>
        <el-table
          height="100%"
          v-loading="loading"
          :data="jobList"
          ref="multipleTable"
          :row-key="getRowKey"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <!--<el-table-column label="任务编号" width="100"  prop="jobId"/>-->
          <el-table-column label="任务名称"  prop="jobName" :show-overflow-tooltip="true"/>
          <!--<el-table-column label="执行策略"  prop="jobGroup">-->
            <!--<template slot-scope="scope">-->
              <!--<span v-if="scope.row.jobType === 0">资产扫描监控</span>-->
              <!--<span v-else-if="scope.row.jobType === 1">基础服务漏洞扫描</span>-->
              <!--<span v-else-if="scope.row.jobType === 2">基础Web漏洞扫描</span>-->
              <!--<span v-else-if="scope.row.jobType === 3">主机资产探测</span>-->
            <!--</template>-->
          <!--</el-table-column>-->
          <el-table-column label="扫描目标"  prop="ipShow" :show-overflow-tooltip="false">
            <template slot-scope="scope">
              <el-tooltip v-if="scope.row.jobType !== 3" class="item" placement="top">
                <div v-html="scope.row.ipOver" slot="content"></div>
                <div class="oneLine">
                  {{ scope.row.ipShow }}
                </div>
              </el-tooltip>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="执行周期"  prop="cronTransfer"/>
          <el-table-column label="最近一次执行时间"  prop="lastRunTime"/>
          <el-table-column label="最近扫描状态"  prop="jobGroup">
            <template slot-scope="scope">
              <el-tag type="danger" v-if="scope.row.currentStatus === 0">未扫描</el-tag>
              <el-tag v-else-if="scope.row.currentStatus === 1">扫描中</el-tag>
              <el-tag type="success" v-else-if="scope.row.currentStatus === 2">已扫描</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="任务状态" >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" fixed="right" :show-overflow-tooltip="false" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleView(scope.row)"
                v-hasPermi="['monitor:ipschedule:query']"
              >详情
              </el-button>
              <el-button
                size="mini"
                type="text"
                :disabled="scope.row.currentStatus === 1"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['monitor:ipschedule:edit']"
              >编辑
              </el-button>
              <el-button
                size="mini"
                type="text"
                :class="{'table-delBtn':scope.row.currentStatus !== 1}"
                :disabled="scope.row.currentStatus === 1"
                @click="handleDelete(scope.row)"
                v-hasPermi="['monitor:ipschedule:remove']"
              >删除
              </el-button>
              <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)"
                           v-hasPermi="['monitor:ipschedule:query']">
            <span class="el-dropdown-link">
              <i class="el-icon-d-arrow-right el-icon--right"></i>更多
            </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :disabled="scope.row.status === '1' || scope.row.currentStatus === 1"
                                    command="handleRun" icon="el-icon-caret-right">执行一次
                  </el-dropdown-item>
                  <!--<el-dropdown-item command="handleView" icon="el-icon-view">任务详细</el-dropdown-item>-->
                  <el-dropdown-item command="handleJobLog" icon="el-icon-s-operation">调度日志</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <LeakScanDialog
      :title="title"
      :edit-form="editForm"
      :edit-title="editTitle"
      :is-disable.sync="invokeDisable"
      @getList="getList"
      :scan-strategy-visible.sync="scanStrategyVisible"/>



    <!-- 任务日志详细 -->
    <el-dialog title="任务详细" v-if="openView" :visible.sync="openView" v-dialog-drag width="1200px" append-to-body>
      <ff-job-tasks  v-if="openView" :jobId="jobId" :job-type="jobType" :job-row="editForm" />
    </el-dialog>
    <!-- 调度日志 -->
    <el-dialog title="调度日志" v-if="openLogView" :visible.sync="openLogView" v-dialog-drag width="1200px" append-to-body>
      <job-log v-if="openLogView" :jobId="logJobId"></job-log>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openLogView = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listJob, getJob, delJob, addJob, updateJob, runJob, changeJobStatus} from "@/api/safe/monitor";
import QuestResultDetails from '../../safe/server/questResultDetails'
import LeakScanDialog from '../../safe/server/components/LeakScanDialog'
import FfJobTasks from './ffJobTasks'
import JobLog from '../../monitor/job/log'

export default {
  name: "Job",
  components: { JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },
  dicts: ['sys_job_group', 'sys_job_status'],
  props: {
    toParams: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      jobType: undefined,
      // 是否显示Cron表达式弹出层
      openCron: false,
      // 展示最近一次运行结果
      openSelect: false,
      // 遮罩层
      loading: true,
      // 任务ID
      jobId: '',
      logJobId: null,
      totalScan: 0,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 定时任务表格数据
      jobList: [],
      // 弹出层标题
      title: "",
      editTitle: '',
      // 是否显示弹出层
      open: false,
      // 是否显示详细弹出层
      openView: false,
      openLogView: false,
      scanStrategyVisible:  false,
      editForm: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobType: 1,
        jobName: undefined,
        jobGroup: 'ASSET_SCAN',
        status: undefined
      },
      invokeDisable: false,
      isDisabled: false,
      // 周期转换文字
      cronText: '',
      rows: [],
      form: {},
      // 表单校验
      rules: {
        jobName: [
          {required: true, message: "任务名称不能为空", trigger: "blur"}
        ],
        invokeIp: [
          {required: true, message: "扫描IP不能为空", trigger: "blur"}
        ],
        cronExpression: [
          {required: true, message: "cron执行表达式不能为空", trigger: "blur"}
        ]
      },
      getListInterval: null,
      selectedIds: []
    };
  },
  watch: {
    toParams: {
      handler(newVal) {
        if(newVal && newVal.id){
          this.handleJobLog({
            jobId: newVal.id
          });
        }
      },
      immediate: true
    }
  },
  created() {
    this.getList()
    this.getListInterval = setInterval(() => {
      this.loopGetList()
    }, 10000)
  },
  destroyed() {
    if(this.getListInterval){
      clearInterval(this.getListInterval);
    }
  },
  // watch: {
  //   'dict.type.sys_job_group': {
  //     handler(newVal) {
  //       if (newVal.length > 0) {
  //         let tmp = newVal.filter(s => s.label === '资产扫描')
  //         this.form.jobGroup = tmp.length > 0 ? tmp[0].value : undefined
  //         this.queryParams.jobGroup = this.form.jobGroup
  //       }
  //     },
  //     deep: true
  //   }
  // },
  methods: {
    /** 查询定时任务列表 */
    getList() {
      this.loading = true;
      listJob(this.queryParams).then(response => {
        response.rows.forEach(s => {
          if (s.invokeTarget) {
            const target = s.invokeTarget
            const start = target.indexOf('\',\'') + 3
            const end = target.length - 2
            const ips = target.substring(start, end)
            const ipss = ips.split('|')
            if (ipss.length > 1) {
              s.ipShow = ipss[1].replaceAll(';', '  ')
              s.ipOver = ipss[1].replaceAll(';', '<br>')
            }
            if(s.period === 0){
              s.status = s.currentStatus === 1 ? '0' : '1'
            }
          }
        })
        this.jobList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    loopGetList() {
      listJob(this.queryParams).then(response => {
        response.rows.forEach(s => {
          if (s.invokeTarget) {
            const target = s.invokeTarget
            const start = target.indexOf('\',\'') + 3
            const end = target.length - 2
            const ips = target.substring(start, end)
            const ipss = ips.split('|')
            if (ipss.length > 1) {
              s.ipShow = ipss[1].replaceAll(';', ' ')
              s.ipOver = ipss[1].replaceAll(';', '<br>')
            }
            if(s.period === 0){
              s.status = s.currentStatus === 1 ? '0' : '1'
            }
          }
        })
        const newJobList = response.rows
        const selectedIds = [...this.selectedIds]
        this.jobList = response.rows;
        this.total = response.total;
        this.$nextTick(() => {
          const rowsToSelect = newJobList.filter(row =>
            selectedIds.includes(row.jobId)
          );
          this.$refs.multipleTable.clearSelection();
          rowsToSelect.forEach(row => {
            this.$refs.multipleTable.toggleRowSelection(row, true);
          })
        })
      });
    },
    handleScan() {
      this.invokeDisable = false
      this.title = '添加任务';
      this.editTitle = 'IP漏洞扫描'
      this.editForm = {}
      this.editForm.jobType = 1;
      this.editForm.weakPw = '1';
      this.editForm.status = '0';
      this.editForm.cronExpression= '* * * * * ?';
      this.editForm.period= 0;
      this.editForm.cronTransfer= '立即执行';
      this.scanStrategyVisible = true;
    },
    // 任务组名字典翻译
    jobGroupFormat(row, column) {
      return this.selectDictLabel(this.dict.type.sys_job_group, row.jobGroup);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.invokeDisable = false
      this.reset();
    },
    /** 确定后回传值 */
    cronTabFill(val) {
      this.form.cronExpression = val.cronText
      this.form.period = val.period
      this.form.cronTransfer = val.cronTransfer
    },
    // 表单重置
    reset() {
      this.form = {
        jobId: undefined,
        jobName: undefined,
        invokeTarget: undefined,
        cronExpression: undefined,
        misfirePolicy: 0,
        concurrent: 1,
        period: 0,
        jobType: 1,
        status: "0"
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    getRowKey(row) {
      return row.jobId;
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.jobId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
      this.rows = selection;
      this.selectedIds = [...this.ids]
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "lastResult":
          this.handleDetail(row);
          break;
        case "handleRun":
          this.handleRun(row);
          break;
        case "handleView":
          this.handleView(row);
          break;
        case "handleJobLog":
          this.handleJobLog(row);
          break;
        default:
          break;
      }
    },
    // 任务状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.jobName + '"任务吗？').then(function () {
        return changeJobStatus(row.jobId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0";
      }).finally(() => {
        var _this = this
        setTimeout(() => {
          _this.getList();
        }, 1000);
      });
    },
    /* 最近执行结果 */
    handleDetail(row) {
      this.jobId = row.jobId
    },
    /* 立即执行一次 */
    handleRun(row) {
      this.$modal.confirm('确认要立即执行一次"' + row.jobName + '"任务吗？').then(function () {
        return runJob(row.jobId, row.jobGroup);
      }).then(() => {
        this.$modal.msgSuccess("执行成功");
      }).catch(() => {
      }).finally(() => {
        this.getList();
      });
    },
    /** 任务详细信息 */
    handleView(row) {
      this.openView = true;
      this.jobType = 2;
      this.jobId = row.jobId;
      this.editForm = {...row}
    },
    editNow(jobId) {
      let filter = this.jobList.filter(item => item.jobId == jobId)
      if (filter.length === 0) {
        this.$message.error('未找到任务数据！')
        return
      } else {
        if (filter[0].currentStatus === 1) {
          this.$message.error('当前任务状态为正在扫描中，请勿更改！')
          return
        }
        this.openView = false
        this.handleUpdate(filter[0])
      }
    },
    executeNow(jobId) {
      let filter = this.jobList.filter(item => item.jobId == jobId)
      if (filter.length === 0) {
        this.$message.error('未找到任务数据！')
        return
      } else {
        if (filter[0].status === '1' || filter[0].currentStatus === 1) {
          this.$message.error('当前任务状态为暂停或正在扫描中，请勿执行！')
          return
        }
        this.openView = false
        this.handleRun(filter[0])
      }

    },
    /** 任务日志列表查询 */
    handleJobLog(row) {
      this.logJobId = row.jobId || 0;
      this.openLogView = true
      // this.$router.push({path: '/monitor/job-log/index', query: {jobId: jobId}})
    },
    handleSelect() {
      this.openSelect = true
    },
    /** 新增按钮操作 */
    handleAdd(val) {
      this.reset();
      switch (val) {
        case 0:
          this.editTitle = '资产扫描监控'
          break
        case 1:
          this.editTitle = '基础服务漏洞扫描'
          break
        case 2:
          this.editTitle = 'Web漏洞扫描'
          break
        case 3:
          this.editTitle = '主机资产探测'
          break
        default:
          break
      }
      this.openSelect = false
      this.form.jobType = val
      this.open = true;
      this.title = "添加任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      switch (row.jobType) {
        case 0:
          this.editTitle = '资产扫描监控'
          break
        case 1:
          this.editTitle = '基础服务漏洞扫描'
          break
        default:
          break
      }
      this.reset();
      const jobId = row.jobId || this.ids;
      getJob(jobId).then(response => {
        const target = response.data.invokeTarget
        const start = target.indexOf('\',\'') + 3
        const end = target.length - 2
        const ips = target.substring(start, end)
        const ipss = ips.split('|')
        if (ipss.length > 1) {
          response.data.invokeIp = ipss[1].replaceAll(';', '\n')
          response.data.weakPw = ipss[4]
        }
        this.editForm= response.data;
        this.scanStrategyVisible = true;
        this.invokeDisable = true
        this.title = "修改任务";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // ip正则
          const ipReg = /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])\.((1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.){2}(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)$/
          // ip段正则
          const ipSegmentReg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.([0-9]|\d{1,2}|1\d{1,2}|2[0-4]\d|25[0-5])-([1-9]|\d{1,2}|1\d{1,2}|2[0-4]\d|25[0-5])$/
          // 带子网掩码正则
          // const ipMaskReg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\/(\d{1,2})$/

          if (this.form.invokeIp) {
            let ips = this.form.invokeIp.split(/\n/g);
            // 调用目标字符串
            this.form.invokeIp = ips.join(' ')
          }
          /**
           for (let i = 0; i < ips.length; i++) {
           if (!(ipReg.test(ips[i]) || ipSegmentReg.test(ips[i]))) {
           this.$message.error('请输入正确的ip地址或地址段，检查是否有空格和特殊字符！')
           return
           }
           } */


          if (this.form.jobType === 0) {
            this.form.invokeTarget = 'jkServer.scan(\'${jobId}\',\'' + this.form.invokeIp + '\')'
          } else if (this.form.jobType === 1) {
            this.form.invokeTarget = 'BaseServerVulnScan.scan(\'${jobId}\',\'' + this.form.invokeIp + '\')'
          } else if (this.form.jobType === 2) {
            this.form.invokeTarget = 'WebServerVulnScan.scan(\'${jobId}\',\'' + this.form.invokeIp + '\')'
          } else if (this.form.jobType === 3) {
            this.form.invokeTarget = 'cloudWalkerScan.scan(\'${jobId}\')'
          }

          // 任务分组
          this.form.jobGroup = 'ASSET_SCAN'

          if (this.form.jobId != undefined) {
            updateJob(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.invokeDisable = false
              this.getList();
            })
          } else {
            addJob(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.invokeDisable = false
              this.getList();
            })
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let rows = [...this.rows];
      rows = rows.filter(item => item.currentStatus === 1);
      if (rows.length > 0) {
        this.$message.error('选择中有扫描中任务，无法批量删除');
        return false;
      }
      const jobIds = row.jobId || this.ids;
      this.$modal.confirm('是否确认删除定时任务编号为【' + jobIds + '】的数据项？').then(function () {
        return delJob(jobIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('monitor/schedule/export', {
        ...this.queryParams
      }, `job_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style src="../../../assets/styles/assetIndex.scss" scoped lang="scss"/>
<style scoped lang="scss">
.policyCol {
  min-width: 330px;
  margin-top: 10px;
}

.policyDesc {
  display: flex;
  height: 80px;
}

.policyTxt {
  margin-left: 10px;
  line-height: 20px;
}

.policyTitle {
  height: 40px;
  line-height: 40px;
}

.oneLine {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

::v-deep .el-table {
  display: flex;
  flex-direction: column;
}

::v-deep .el-table__body-wrapper {
  overflow-y: auto;
  flex: 1;
}
</style>
