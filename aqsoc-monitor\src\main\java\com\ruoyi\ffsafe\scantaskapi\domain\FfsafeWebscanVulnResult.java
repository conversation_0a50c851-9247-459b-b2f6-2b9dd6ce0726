package com.ruoyi.ffsafe.scantaskapi.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class FfsafeWebscanVulnResult extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    private Long id;

    /** 任务id */
    @Excel(name = "任务id")
    private Integer taskId;

    /** 漏洞名称 */
    @Excel(name = "漏洞名称")
    private String vulnName;

    /** 漏洞类型 */
    @Excel(name = "漏洞类型")
    private String vulnType;

    /** 漏洞影响 */
    @Excel(name = "漏洞影响")
    private String impact;

    /** 漏洞描述 */
    @Excel(name = "漏洞描述")
    private String description;

    /** 处置建议 */
    @Excel(name = "处置建议")
    private String recommendation;

    /** url */
    @Excel(name = "url")
    private String url;

    /** 参数 */
    @Excel(name = "参数")
    private String param;

    /** 证据 */
    @Excel(name = "证据")
    private String evidence;

    /** 级别: 0表示信息，1-3分别表示低中高 */
    @Excel(name = "级别: 0表示信息，1-3分别表示低中高")
    private Integer severity;

    /** 汇总记录ID */
    @Excel(name = "汇总记录ID")
    private Long summaryId;
}
