<template>
  <div>
    <el-row class="s_d_row">
      <el-col>
        <span class="s_span_tag">漏洞结果综述</span>
      </el-col>
    </el-row>
    <el-row :gutter="15" class="row-item">
      <el-col :span="12">
        <div ref="holeMap" id="holeMap" style="height: 300px"></div>
      </el-col>
      <el-col :span="12">
        <div ref="pieMap" id="pieMap" style="height: 300px"></div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="form" style="width: 100%" :header-cell-style="headerCellStyle" border v-if="jobRow.jobType === 1">
            <el-table-column label="任务名称">
              <template slot-scope="scope">
                <span> {{ jobRow.jobName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="存活主机" >
              <template slot-scope="scope">
                <span>{{ total }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="ipv4" label="弱口令" >
              <template slot-scope="scope">
                <span>{{ wpTotal }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="pocRiskNum" label="可入侵漏洞" />
            <el-table-column prop="highRiskNum" label="高风险漏洞" />
            <el-table-column prop="middleRiskNum" label="中风险漏洞" />
            <el-table-column prop="lowRiskNum" label="低风险漏洞" />
          </el-table>
          <el-table :data="form" style="width: 100%" :header-cell-style="headerCellStyle" border v-if="jobRow.jobType === 2">
            <el-table-column label="任务名称">
              <template slot-scope="scope">
                <span> {{ jobRow.jobName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="网站" >
              <template slot-scope="scope">
                <div v-html="jobRow.ipOver" slot="content"></div>
              </template>
            </el-table-column>
            <el-table-column prop="highRiskNum" label="高风险" />
            <el-table-column prop="middleRiskNum" label="中风险" />
            <el-table-column prop="lowRiskNum" label="低风险" />
            <el-table-column prop="infoRiskNum" label="信息风险" />
            <el-table-column prop="startTime" label="开始时间" />
            <el-table-column prop="endTime" label="结束时间" />
          </el-table>
        </div>
      </el-col>
    </el-row>
    <el-row class="s_d_row">
      <el-col>
        <span class="s_span_tag">任务参数</span>
      </el-col>
    </el-row>
    <el-row style="height: 40px;line-height: 40px;margin-top: 20px;border: solid 1px #e7e3e361;">
      <el-col :span="12">
        <div>
          <span class="b_tag">任务类型</span>
          <span v-if="jobRow.jobType === 1">基础服务漏洞扫描</span>
          <span v-if="jobRow.jobType === 2">基础Web漏洞扫描</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div>
          <span class="b_tag">任务目标</span>
          <span v-html="jobRow.ipShow" slot="content" class="truncate" :title="jobRow.ipShow"></span>
        </div>
      </el-col>
    </el-row>
    <el-row style="height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;">
      <el-col :span="12">
        <div>
          <span class="b_tag">任务状态</span>
          <el-tag type="danger" v-if="form[0].taskType === 2 && form[0].taskStatus === 3">任务异常</el-tag>
          <el-tag v-else-if="form[0].taskType === 2 && form[0].taskStatus === 1">扫描中</el-tag>
          <el-tag v-else-if="form[0].taskType === 2 && form[0].taskStatus === 2">扫描中</el-tag>
          <el-tag type="success" v-else-if="form[0].taskType === 2 && form[0].taskStatus === 4">已扫描</el-tag>
          <el-tag type="danger" v-else-if="form[0].taskType === 1 && form[0].taskStatus === 3">任务异常</el-tag>
          <el-tag type="danger" v-else-if="form[0].taskType === 1 && form[0].taskStatus === 4">任务终止</el-tag>
          <el-tag v-else-if="form[0].taskType === 1 && form[0].taskStatus === 0">扫描中</el-tag>
          <el-tag v-else-if="form[0].taskType === 1 && form[0].taskStatus === 1">扫描中</el-tag>
          <el-tag type="success" v-else-if="form[0].taskType === 1 && form[0].taskStatus === 2">已扫描</el-tag>
        </div>
      </el-col>
      <el-col :span="12">
        <div>
          <span class="b_tag">开始时间</span>
          <span>{{ form[0].startTime }}</span>
        </div>
      </el-col>
    </el-row>
    <el-row style="height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;">
      <el-col :span="12">
        <div>
          <span class="b_tag">探测参数</span>
          <span>  快速/存活探测启用/全连接</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div>
          <span class="b_tag">结束时间</span>
          <span>{{ form[0].endTime }}</span>
        </div>
      </el-col>
    </el-row>
    <el-row style="height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;">
      <el-col :span="12">
        <div>
          <span class="b_tag">端口范围</span>
          <span>常规端口</span>
        </div>
      </el-col>
    </el-row>
    <el-row class="s_d_row" v-if="jobRow.jobType === 1">
      <el-col>
        <span class="s_span_tag">主机漏洞列表</span>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 1">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="jobTaskList" style="width: 100%" :header-cell-style="headerCellStyle">
            <el-table-column type="index" label="序号" width="70" />
            <el-table-column prop="ip" label="IP" />
            <el-table-column prop="systemName" label="操作系统  " />
            <el-table-column prop="pocRiskNum" label="可入侵漏洞" />
            <el-table-column prop="highRiskNum" label="高风险" />
            <el-table-column prop="middleRiskNum" label="中风险" />
            <el-table-column prop="lowRiskNum" label="低风险" />
            <el-table-column prop="pwNum" label="弱口令数量" />
            <el-table-column prop="portNum" label="开放端口数量" />
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getIpList"
          />
        </div>
      </el-col>
    </el-row>
    <el-row class="s_d_row">
      <el-col>
        <span class="s_span_tag">漏洞分布列表</span>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 1">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="scanList" v-loading="loading" style="width: 100%" :header-cell-style="headerCellStyle">
            <el-table-column type="expand" v-if="jobRow.jobType === 1">
              <template slot-scope="props">
                <el-descriptions :column="1" border :label-style="ls">
                  <el-descriptions-item label="受影响IP">{{ props.row.hostIp }}</el-descriptions-item>
                  <el-descriptions-item label="受影响端口">{{ props.row.hostPort }}</el-descriptions-item>
                  <el-descriptions-item label="受影响版本信息">{{ props.row.versionInfo }}</el-descriptions-item>
                  <el-descriptions-item label="漏洞发布时间">{{ props.row.publishDate }}</el-descriptions-item>
                  <el-descriptions-item label="威胁类型">{{ props.row.vulnType }}</el-descriptions-item>
                  <el-descriptions-item label="漏洞简介">
                    {{ props.row.vulnInfo }}
                  </el-descriptions-item>
                  <el-descriptions-item label="解决方案"><span v-html="props.row.vulnSolve"></span></el-descriptions-item>
                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column type="index" label="序号" width="70" />
            <el-table-column prop="vulnName" label="漏洞名称">
              <template slot-scope="scope">
                <span v-if="scope.row.riskLevel == 1" style="color: #13ce66">{{ scope.row.vulnName }}</span>
                <span v-if="scope.row.riskLevel == 2" style="color: #ffba00">{{ scope.row.vulnName }}</span>
                <span v-if="scope.row.riskLevel == 3" style="color: #ff4949">{{ scope.row.vulnName }}</span>
                <span v-if="scope.row.riskLevel == 4"  style="color: #ff4949">{{ scope.row.vulnName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="vulnType" label="漏洞类型" />
            <el-table-column prop="riskLevel" label="风险级别"  >
              <template slot-scope="scope">
                <el-tag v-if="scope.row.riskLevel == 1" type="success"
                >低风险</el-tag
                >
                <el-tag v-if="scope.row.riskLevel == 2" type="warning"
                >中风险</el-tag
                >
                <el-tag v-if="scope.row.riskLevel == 3" type="danger"
                >高风险</el-tag
                >
                <el-tag v-if="scope.row.riskLevel == 4" type="danger">严重</el-tag>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="scanTotal>0"
            :total="scanTotal"
            :page.sync="queryScanParams.pageNum"
            :limit.sync="queryScanParams.pageSize"
            @pagination="getScanList"
          />
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 2">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="scanList" v-loading="loading" style="width: 100%" :header-cell-style="headerCellStyle">
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-descriptions :column="1" border :label-style="ls">
                  <el-descriptions-item label="漏洞描述">{{ props.row.description }}</el-descriptions-item>
                  <el-descriptions-item label="漏洞危害">{{ props.row.impact }}</el-descriptions-item>
                  <el-descriptions-item label="漏洞解决方案">{{ props.row.recommendation }}</el-descriptions-item>
                  <el-descriptions-item label="扫描目标">{{ props.row.url }}</el-descriptions-item>
                  <el-descriptions-item label="漏洞构造请求">
                    <span v-html="props.row.evidence" :style="props.row.evidence.includes('Host') ? { whiteSpace: 'pre-wrap' } : { whiteSpace: 'normal' }"></span>
                  </el-descriptions-item>

                </el-descriptions>
              </template>
            </el-table-column>
            <el-table-column type="index" label="序号" width="70" />
            <el-table-column prop="vulnName" label="漏洞名称">
              <template slot-scope="scope">
                <span v-if="scope.row.severity == 1" style="color: #1890ff">{{ scope.row.vulnName }}</span>
                <span v-if="scope.row.severity == 2" style="color: #13ce66">{{ scope.row.vulnName }}</span>
                <span v-if="scope.row.severity == 3" style="color: #ffba00">{{ scope.row.vulnName }}</span>
                <span v-if="scope.row.severity == 4"  style="color: #ff4949">{{ scope.row.vulnName }}</span>
                <span v-if="scope.row.severity == 0"  style="color: #ff4949">{{ scope.row.vulnName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="vulnType" label="漏洞类型  " />
            <el-table-column prop="severity" label="风险级别"  >
              <template slot-scope="scope">
                <el-tag v-if="scope.row.severity == 1" type="primary"
                >信息风险</el-tag
                >
                <el-tag v-if="scope.row.severity == 2" type="success"
                >低风险</el-tag
                >
                <el-tag v-if="scope.row.severity == 3" type="warning"
                >中风险</el-tag
                >
                <el-tag v-if="scope.row.severity == 4" type="danger">高风险</el-tag>
                <el-tag v-if="scope.row.severity == 0" type="danger">信息风险</el-tag>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="scanTotal>0"
            :total="scanTotal"
            :page.sync="queryScanParams.pageNum"
            :limit.sync="queryScanParams.pageSize"
            @pagination="getScanList"
          />
        </div>
      </el-col>
    </el-row>
    <el-row class="s_d_row" v-if="jobRow.jobType === 1">
      <el-col>
        <span class="s_span_tag">弱口令列表</span>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 1">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="wpResultList" style="width: 100%" :header-cell-style="headerCellStyle">
            <el-table-column type="index" label="序号" width="70px" />
            <el-table-column prop="hostIp" label="IP" />
            <el-table-column prop="serviceType" label="服务类型  " />
            <el-table-column prop="hostPort" label="端口" />
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="weakPassword" label="密码" />
          </el-table>
          <pagination
            v-show="wpTotal>0"
            :total="wpTotal"
            :page.sync="queryWpParams.pageNum"
            :limit.sync="queryWpParams.pageSize"
            @pagination="getWpresultList"
          />
        </div>
      </el-col>
    </el-row>
    <el-row class="s_d_row" v-if="jobRow.jobType === 1">
      <el-col>
        <span class="s_span_tag">开放端口列表</span>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 1">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="portResultList" style="width: 100%" :header-cell-style="headerCellStyle">
            <el-table-column type="index" label="序号" width="70px" />
            <el-table-column prop="hostIp" label="IP" />
            <el-table-column prop="hostPort" label="端口  " />
            <el-table-column prop="procotol" label="传输协议" />
            <el-table-column prop="serviceName" label="服务名称" />
            <el-table-column prop="product" label="服务与版本" />
          </el-table>
          <pagination
            v-show="portTotal>0"
            :total="portTotal"
            :page.sync="queryPortParams.pageNum"
            :limit.sync="queryPortParams.pageSize"
            @pagination="getPortresultList"
          />
        </div>
      </el-col>
    </el-row>
    <el-row class="s_d_row" v-if="jobRow.jobType === 2">
      <el-col>
        <span class="s_span_tag">附录-漏洞等级风险说明</span>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 2">
      <el-col :span="24">
        <div class="collapse-content-div">
          漏洞是与信息资产有关的弱点或安全隐患。漏洞本身并不对资产构成危害，但是在一定条件得到满是时，漏洞会被威胁加以利用来对信息资产造成危险!本报告的漏洞共分了以下4种漏洞风险等级。
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 2">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="levelList" style="width: 100%" :header-cell-style="headerCellStyle">
            <el-table-column prop="label" label="危险程度" width="120px">
              <template slot-scope="scope">
                <span v-if="scope.row.label === '高'" style="color: #ff4949">高</span>
                <span v-if="scope.row.label === '中'" style="color: #ffba00">中</span>
                <span v-if="scope.row.label === '低'" style="color: #13ce66">低</span>
                <span v-if="scope.row.label === '风险信息'" style="color: #1890ff">风险信息</span>
              </template>
            </el-table-column>>
            <el-table-column prop="value" label="危险程度说明" />
          </el-table>
        </div>
      </el-col>
    </el-row>

    <el-row class="s_d_row" v-if="jobRow.jobType === 1">
      <el-col>
        <span class="s_span_tag">附录1-漏洞等级风险说明</span>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 1">
      <el-col :span="24">
        <div class="collapse-content-div">
          漏洞是与信息资产有关的弱点或安全隐患。漏洞本身并不对资产构成危害，但是在一定条件得到满是时，漏洞会被威胁加以利用来对信息资产造成危险!本报告的漏洞共分了以下4种漏洞风险等级。
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 1">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="hostLevelList" style="width: 100%" :header-cell-style="headerCellStyle">
            <el-table-column prop="label" label="危险程度" width="160px">
              <template slot-scope="scope">
                <span v-if="scope.row.label === '可入侵漏洞(严重)'" style="color: #ff4949">可入侵漏洞(严重)</span>
                <span v-if="scope.row.label === '高'" style="color: #ff4949">高</span>
                <span v-if="scope.row.label === '中'" style="color: #ffba00">中</span>
                <span v-if="scope.row.label === '低'" style="color: #13ce66">低</span>
              </template>
            </el-table-column>>
            <el-table-column prop="value" label="危险程度说明" />
          </el-table>
        </div>
      </el-col>
    </el-row>

    <el-row class="s_d_row" v-if="jobRow.jobType === 1">
      <el-col>
        <span class="s_span_tag">附录2-主机漏洞加固策略</span>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 1">
      <el-col :span="24">
        <div class="collapse-content-div">
          基于多年的安全整改经验，提供了以下5种安全加固整改策略，并对不同整改建议的有效的防护度、整改困难度作了评级参考，有效防护度越高则表示加固效果越好，整改困难度越高则表示整改方案实施越难。您可以根据实际的业务情况，参考以下表，选择加固整改策略。
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="margin-top: 20px;" v-if="jobRow.jobType === 1">
      <el-col :span="24">
        <div class="collapse-content-div">
          <el-table :data="hostStrategyList" style="width: 100%" :header-cell-style="headerCellStyle">
            <el-table-column type="index" label="序号" width="80px" />
            <el-table-column prop="value" label="加固整改策略">
            </el-table-column>>
            <el-table-column prop="label" label="有效防护度" align="center"  width="130px" />
            <el-table-column prop="level" label="整改困难度" align="center" width="130px"/>
          </el-table>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import {
    hostriskstat,
    listIpPortresult,
    listIpTaskresult,
    listIpWpresult,
    listVulnresult,
    listWebresult,
    middlehighlevelstat
  } from '@/api/monitor2/wpresult'

  export default {
    name: 'ffJobTasksDetail',
    props: {
      form: {
        type: Array,
        default: null
      },
      jobRow: {
        type: Object,
        default: null
      },
      macAipList: {
        type: Array,
        default: null
      }
    },
    data() {
      return {
        activeNames: ['1', '2', '3', '4'],
        ls: {
          'width': '130px',
        },
        collapseLabelSpan: 4,
        collapseContentSpan: 8,
        levelList: [
          {
            label: '高',
            value: '攻击者可以远程操作系统文件、读写后台数据库、执行任意命令或进行远程拒绝服务攻击。'
          },
          {
            label: '中',
            value: '攻击者可以利用Web网站攻击其他用户，读取系统文件或后台数据库。'
          },
          {
            label: '低',
            value: '攻击者可以获取某些系统、网站、文件的信息或冒用身份。'
          },
          {
            label: '风险信息',
            value: '攻击者可以获取网站相关信息，可能是非敏感信息。'
          }
        ],
        hostLevelList: [
          {
            label: '可入侵漏洞(严重)',
            value: '有公开利用方法，已验证可远程执行任意命令或者代码，或对系统进行远程拒绝服务攻击，或可获取重要敏感效据。此部分漏洞基于漏洞原理验证识别，在漏洞名称中，会标识【原理扫描】。'
          },
          {
            label: '高',
            value: '攻击者可以远程执行任意命令或者代码，或对系统进行远程拒绝服务攻击，或可获取重要教感数据。此部分漏洞主要通过服务版本识别，可能会有一定的误报。'
          },
          {
            label: '中',
            value: '攻击者可以远程创建、修改、删除部分文件或数据，或对普通服务进行拒绝服务攻击。此部分温洞主要通过服务版本识别，可能会有一定的误报。'
          },
          {
            label: '低',
            value: '攻击者可以获取某些系统、服务的信息，或读取某些系统文件和教据。此部分漏洞主要通过服务版本识别，可能会有一定的误报。'
          }
        ],
        hostStrategyList: [
          {
            label: '高',
            level: '高',
            value: '根据漏洞整改建议打补丁或者修改配置进行安全加固，加固前建议作好相关备份以使回退;建议所有 Windows 系统使用"Windows Update"进行更新。'
          },
          {
            label: '高',
            level: '低',
            value: '若存在漏洞的应用服务平时不需要使用，建议关闭这些不必要的服务或应用。'
          },
          {
            label: '中',
            level: '低',
            value: '若存在漏洞的应用服务不需要对外开放或者只对部分用户开放，建议在主机防火墙上进行访问控制，限定只有合法IP才能访问此应用。'
          },
          {
            label: '低',
            level: '低',
            value: '若不便在主机防火墙上配置，建议在网络/出口防火墙上做白名单访问控制。'
          },
          {
            label: '低',
            level: '中',
            value: '建议修改应用的banner信息，隐藏应用名称、版本号等信息，让攻击者无法识别目标系统，便之难以进行针对性的攻击入侵。'
          }
        ],
        headerCellStyle: { 'font-weight': 'normal', color: '#979797' },
        cellStyle: { 'font-weight': 'bold' },
        queryParams: {
          pageNum: 1,
          pageSize: 10,
        },
        queryWpParams: {
          pageNum: 1,
          pageSize: 10,
        },
        queryPortParams: {
          pageNum: 1,
          pageSize: 10,
        },
        queryScanParams: {
          pageNum: 1,
          pageSize: 10,
        },
        jobTaskList: [],
        wpResultList: [],
        scanList: [],
        portResultList: [],
        holeMapData: [],
        xAsisArr: [],
        pieMapData: [],
        portTotal: 0,
        wpTotal: 0,
        total: 0,
        scanTotal: 0,
        loading: false
      }
    },
    mounted() {
      this.getIpList()
      this.getWpresultList()
      this.getPortresultList()
      this.getScanList()
      this.getMiddlehighlevelstat()
    },
    methods: {
      getIpList() {
        this.queryParams.taskId = this.form[0].taskId
        if (this.jobRow.jobType === 1) {
          listIpTaskresult(this.queryParams).then(response => {
            this.jobTaskList = response.rows
            this.total = response.total;
            this.$nextTick(() => this.getHostriskstat())
          }).catch(() => {
          });
        }
      },
      getWpresultList() {
        this.queryWpParams.taskId = this.form[0].taskId
        if (this.jobRow.jobType === 1) {
          listIpWpresult(this.queryWpParams).then(response => {
            this.wpResultList = response.rows
            this.wpTotal = response.total;
          }).catch(() => {
          });
        }
      },
      getPortresultList() {
        this.queryPortParams.taskId = this.form[0].taskId
        if (this.jobRow.jobType === 1) {
          listIpPortresult(this.queryPortParams).then(response => {
            this.portResultList = response.rows
            this.portTotal = response.total;
          }).catch(() => {
          });
        }
      },
      getHostriskstat() {
        if (this.jobRow.jobType === 1) {
          this.xAsisArr = []
          this.holeMapData = []
          if (this.total > 1) {
            hostriskstat(this.form[0].taskId).then(response => {
              response.data.forEach(e => {
                if (e.max_risk_level === 1) {
                  this.xAsisArr.push('低风险主机数')
                  const low = {
                    value: e.host_num,
                    itemStyle: {
                      color: '#13ce66'
                    }
                  }
                  this.holeMapData.push(low)
                }
                if (e.max_risk_level === 2) {
                  this.xAsisArr.push('中风险主机数')
                  const middle = {
                    value: e.host_num,
                    itemStyle: {
                      color: '#ffba00'
                    }
                  }
                  this.holeMapData.push(middle)
                }
                if (e.max_risk_level === 3) {
                  this.xAsisArr.push('高风险主机数')
                  const high = {
                    value: e.host_num,
                    itemStyle: {
                      color: '#ff4949'
                    }
                  }
                  this.holeMapData.push(high)
                }
                if (e.max_risk_level === 4) {
                  this.xAsisArr.push('可入侵主机数')
                  const poc = {
                    value: e.host_num,
                    itemStyle: {
                      color: '#1890FF'
                    }
                  }
                  this.holeMapData.push(poc)
                }
              })
              this.$nextTick(() => this.barMapInit())
            })

          } else {
            this.xAsisArr.push('可入侵漏洞')
            const poc = {
              value: this.form[0].pocRiskNum,
              itemStyle: {
                color: '#1890FF'
              }
            }
            this.holeMapData.push(poc)

            this.xAsisArr.push('高风险')
            const high = {
              value: this.form[0].highRiskNum,
              itemStyle: {
                color: '#ff4949'
              }
            }
            this.holeMapData.push(high)
            this.xAsisArr.push('中风险')
            const middle = {
              value: this.form[0].middleRiskNum,
              itemStyle: {
                color: '#ffba00'
              }
            }
            this.holeMapData.push(middle)
            this.xAsisArr.push('低风险')
            const low = {
              value: this.form[0].lowRiskNum,
              itemStyle: {
                color: '#13ce66'
              }
            }
            this.holeMapData.push(low)
            this.barMapInit()
          }
        }
      },
      getMiddlehighlevelstat() {
        if (this.jobRow.jobType !== 1) {
          this.pieMapData =[]
          middlehighlevelstat(this.form[0].taskId).then(response => {
            this.pieMapData = response.data
            this.$nextTick(() => this.pieMapInit())
          })
          this.xAsisArr = []
          this.holeMapData = []
          this.xAsisArr.push('高风险')
          const high = {
            value: this.form[0].highRiskNum,
            itemStyle: {
              color: '#ff4949'
            }
          }
          this.holeMapData.push(high)
          this.xAsisArr.push('中风险')
          const middle = {
            value: this.form[0].middleRiskNum,
            itemStyle: {
              color: '#ffba00'
            }
          }
          this.holeMapData.push(middle)
          this.xAsisArr.push('低风险')
          const low = {
            value: this.form[0].lowRiskNum,
            itemStyle: {
              color: '#13ce66'
            }
          }
          this.holeMapData.push(low)
          this.xAsisArr.push('风险信息')
          const poc = {
            value: this.form[0].infoRiskNum,
            itemStyle: {
              color: '#1890FF'
            }
          }
          this.holeMapData.push(poc)
          this.barMapInit()
        }
      },
      getScanList() {
        this.xAsisArr = []
        this.holeMapData = []
        this.pieMapData = []
        this.loading = true;
        this.queryScanParams.taskId = this.form[0].taskId
        if (this.jobRow.jobType === 1) {
          listVulnresult(this.queryScanParams).then(response => {
            this.scanList = response.rows
            this.scanTotal = response.total;
            this.loading = false;
          }).catch(() => {
            this.loading = false;
          });
          const highRisk = {
            name : '高风险',
            value: this.form[0].highRiskNum || 0
          }
          const middleRisk = {
            name : '中风险',
            value: this.form[0].middleRiskNum || 0
          }
          const lowRisk = {
            name : '低风险',
            value: this.form[0].lowRiskNum || 0
          }
          const pocRisk = {
            name : '可入侵漏洞',
            value: this.form[0].pocRiskNum || 0
          }
          this.pieMapData.push(highRisk)
          this.pieMapData.push(middleRisk)
          this.pieMapData.push(lowRisk)
          this.pieMapData.push(pocRisk)
          this.pieMapInit()


        } else {
          listWebresult(this.queryScanParams).then(response => {
            this.scanList = response.rows
            this.scanList.forEach(e => {
              e.evidence = this.decodeBase64(e.evidence)
            })
            this.scanTotal = response.total;
            this.loading = false;
          }).catch(() => {
            this.loading = false;
          });
        }

      },
      decodeBase64(base64String) {
        return decodeURIComponent(escape(window.atob(base64String)));
      },
      pieMapInit() {
        const pieMapChar = this.$echarts.init(this.$refs.pieMap)
        let title = ''
        if (this.jobRow.jobType === 1) {
          title =  '风险漏洞比例'
        } else {
          title =  '高中风险漏洞类型比例'
        }
        pieMapChar.setOption({
          title: {
            text: title,
            left: 'center'
          },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            orient: 'vertical',
            x: 'right',
            y: 'center'
          },
          color: ['#ff4949','#ffba00','#13ce66','#1890FF','#ea7ccc','#fc8452','#3ba272','#73c0de','#fac858','#91cc75','#ee6666','#9a60b4'],
          series: [
            {
              name: title,
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false,
                position: 'center'
              },
              labelLine: {
                show: false
              },
              data: this.pieMapData
            }
          ]
        })
      },
      barMapInit() {
        const holeMapChar = this.$echarts.init(this.$refs.holeMap)
        let title = ''
        if (this.jobRow.jobType === 1) {
          title =  '风险主机数'
        } else {
          title =  '漏洞风险级别'
        }
        holeMapChar.setOption({
          title: {
            text:title,
            left: 'center'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            orient: 'vertical',
            x: 'right',
            y: 'center'
          },
          color: ['#1890FF'],
          grid: {
            top: 30,
            left: 20,
            right: 20,
            bottom: 10,
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.xAsisArr
          },
          yAxis: {
            type: 'value',
            show: false,
          },
          series: [
            {
              type: 'bar',
              data: this.holeMapData,
              barWidth: '30%'
            }
          ]
        })
      }
    }
  }
</script>

<style scoped>
  .back_card {
    background-color: #f3f3f3;
  }

  .s_d_row {
    height: 50px;
    line-height: 50px;
    color: #1890ff;
    background-color: #f3f3f3;
    margin-top: 10px;
    border-bottom: solid 2px #1890ff;
  }
  .s_span_tag {
    margin-left: 10px;
    font-weight: 600;
  }
  ::v-deep .el-card__body {
    padding: 15px 20px;
  }
  .b_tag {
    display: inline-block;
    padding: 0px 20px;
    font-weight: 600;
    width: 150px;
    background-color: #f9f7f7;
    margin-right: 10px;
  }
  .truncate {
    display: inline-flex;
    width: 70%; /* 定义容器宽度 */
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
    text-align: center; /* 文本居中 */
  }
</style>

<style lang="scss" scoped>
  .asset-tag{
    margin-left: 5px;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
  .overflow-tag:not(:first-child){
    margin-top: 5px;
  }
  .my-descriptions .fixed-width-label {
    width: 140px;
  }
</style>
