{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\honeypotAlarmList.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\component\\honeypotAlarmList.vue", "mtime": 1755679994096}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7cGFyc2VUaW1lfSBmcm9tICJAL3V0aWxzL3J1b3lpIjsKaW1wb3J0IHtnZXRNdWxUeXBlRGljdH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CmltcG9ydCB7Z2V0RGVwdFN5c3RlbX0gZnJvbSAiQC9hcGkvbW9uaXRvcjIvYXBwbGljYXRpb25Bc3NldHMiOwppbXBvcnQge2dldEFsYXJtLCBkZWxBbGFybSwgbGlzdEFsYXJtLCBhZGRBbGFybSwgdXBkYXRlQWxhcm0sYWRkQmxvY2tJcH0gZnJvbSAiQC9hcGkvdGhyZWF0ZW4vaG9uZXlwb3RBbGFybSI7CmltcG9ydCB7Z2V0QXNzZXRJbmZvQnlJcH0gZnJvbSAiQC9hcGkvc2FmZS9vdmVydmlldyI7CmltcG9ydCBEeW5hbWljVGFnIGZyb20gIi4uLy4uLy4uLy4uL2NvbXBvbmVudHMvRHluYW1pY1RhZyI7CmltcG9ydCBBbGFybURldGFpbCBmcm9tICIuLi8uLi8uLi9iYXNpcy9zZWN1cml0eVdhcm4vYWxhcm1EZXRhaWwiOwppbXBvcnQgaW1wb3J0VGhyZWF0ZW4gZnJvbSAiQC92aWV3cy9iYXNpcy9zZWN1cml0eVdhcm4vaW1wb3J0VGhyZWF0ZW4udnVlIgppbXBvcnQgVGhyZWF0ZW5Db25maWdMaXN0IGZyb20gIkAvdmlld3MvYmFzaXMvc2VjdXJpdHlXYXJuL3RocmVhdGVuQ29uZmlnTGlzdC52dWUiCmltcG9ydCBTZXJ2ZXJBZGQgZnJvbSAiLi4vLi4vLi4vaGhsQ29kZS9jb21wb25lbnQvYXBwbGljYXRpb24vYWRkcy9zZXJ2ZXJBZGQiOwppbXBvcnQgU2FmZUFkZCBmcm9tICIuLi8uLi8uLi9oaGxDb2RlL2NvbXBvbmVudC9hcHBsaWNhdGlvbi9hZGRzL3NhZmVBZGQiOwppbXBvcnQgVmlld1N0cmF0ZWd5IGZyb20gIi4uLy4uLy4uL2Jhc2lzL3NlY3VyaXR5V2Fybi92aWV3U3RyYXRlZ3kiOwppbXBvcnQgUHVibGlzaENsaWNrRGlhbG9nIGZyb20gIi4uLy4uLy4uL2Jhc2lzL3NlY3VyaXR5V2Fybi9wdWJsaXNoQ2xpY2tEaWFsb2ciOwppbXBvcnQgRmxvd0JveCBmcm9tICIuLi8uLi8uLi96ZXJvQ29kZS93b3JrRmxvdy9jb21wb25lbnRzL0Zsb3dCb3giOwppbXBvcnQgRmxvd1RlbXBsYXRlU2VsZWN0IGZyb20gIi4uLy4uLy4uLy4uL2NvbXBvbmVudHMvRmxvd1RlbXBsYXRlU2VsZWN0IjsKaW1wb3J0IEF0dGFja1N0YWdlIGZyb20gIi4uLy4uLy4uL3RocmVhdC9vdmVydmlldy9hdHRhY2tTdGFnZSI7CmltcG9ydCBBdHRhY2tWaWV3TGlzdCBmcm9tICIuL2F0dGFja1ZpZXdMaXN0IjsKaW1wb3J0IFN1ZmZlclZpZXdMaXN0IGZyb20gIi4vc3VmZmVyVmlld0xpc3QiOwppbXBvcnQgYXR0YWNrRGV0YWlsIGZyb20gIi4vZGV0YWlsL2luZGV4LnZ1ZSI7CmltcG9ydCBzdWZmZXJEZXRhaWwgZnJvbSAiLi9kZXRhaWwvaW5kZXgudnVlIjsKaW1wb3J0IERlcHRTZWxlY3QgZnJvbSAnQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdC9kZXB0U2VsZWN0LnZ1ZScKaW1wb3J0IHt1bmlxdWVBcnJ9IGZyb20gJ0AvdXRpbHMnCmltcG9ydCB7Rmxvd0VuZ2luZUluZm99IGZyb20gIkAvYXBpL2xvd0NvZGUvRmxvd0VuZ2luZSI7CmltcG9ydCB7bGlzdFVzZXJ9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIjsKaW1wb3J0IEF0dGFja1N0YWdlVGV4dCBmcm9tICdAL3ZpZXdzL3RocmVhdC9vdmVydmlldy9hdHRhY2tTdGFnZVRleHQudnVlJwppbXBvcnQge2xpc3REZXZpY2VDb25maWd9IGZyb20gIkAvYXBpL2Zmc2FmZS9kZXZpY2VDb25maWciOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJldmVudExpc3QiLAogIGNvbXBvbmVudHM6IHsKICAgIEF0dGFja1N0YWdlVGV4dCwKICAgIERlcHRTZWxlY3QsCiAgICBTdWZmZXJWaWV3TGlzdCwKICAgIEF0dGFja1ZpZXdMaXN0LAogICAgQXR0YWNrU3RhZ2UsCiAgICBGbG93VGVtcGxhdGVTZWxlY3QsCiAgICBGbG93Qm94LAogICAgUHVibGlzaENsaWNrRGlhbG9nLAogICAgYXR0YWNrRGV0YWlsLAogICAgc3VmZmVyRGV0YWlsLAogICAgVGhyZWF0ZW5Db25maWdMaXN0LCBWaWV3U3RyYXRlZ3ksIFNhZmVBZGQsIFNlcnZlckFkZCwgaW1wb3J0VGhyZWF0ZW4sIEFsYXJtRGV0YWlsLCBEeW5hbWljVGFnCiAgfSwKICBkaWN0czogWyd0aHJlYXRlbl90eXBlJywgJ2F0dGFja19zdGFnZScsICdhdHRhY2tfcmVzdWx0JywnaGFuZGxlX3N0YXRlJywgJ3N5bmNocm9uaXphdGlvbl9zdGF0dXMnXSwKICBwcm9wczogewogICAgcHJvcHNBY3RpdmVOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZwogICAgfSwKICAgIHByb3BzUXVlcnlQYXJhbXM6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuIG51bGwKICAgICAgfQogICAgfSwKICAgIGN1cnJlbnRCdG46IHsKICAgICAgdHlwZTogTnVtYmVyCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgbGV0IHZhbGlkYXRlQmxvY2tJcCA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF2YWx1ZSkgewogICAgICAgIHJldHVybiBjYWxsYmFjayhuZXcgRXJyb3IoJ0lQ5LiN6IO95Li656m6JykpOwogICAgICB9CiAgICAgIC8vIGxldCBwYXR0ZXJuID0gL14oKDFbMC05XXsyfXwyWzAtNF1bMC05XXwyNVswLTVdfChcZCl7MSwyfSlcLigxWzAtOV17Mn18MlswLTRdWzAtOV18MjVbMC01XXwoXGQpezEsMn18MClcLigxWzAtOV17Mn18MlswLTRdWzAtOV18MjVbMC01XXwoXGQpezEsMn18MClcLigxWzAtOV17Mn18MlswLTRdWzAtOV18MjVbMC01XXwoXGQpezEsMn18MCkpJC87CiAgICAgIGxldCBwYXR0ZXJuID0gL15ccyooKDI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPylcLil7M30oMjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KShccyo7XHMqKCgyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pXC4pezN9KDI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPykpKlxzKiQvOwogICAgICBpZiAoIXBhdHRlcm4udGVzdCh2YWx1ZSkpIHsKICAgICAgICByZXR1cm4gY2FsbGJhY2sobmV3IEVycm9yKCfor7fovpPlhaXmraPnoa7nmoRJUCcpKTsKICAgICAgfQogICAgICByZXR1cm4gY2FsbGJhY2soKTsKICAgIH07CiAgICByZXR1cm4gewogICAgICBkZXZpY2VDb25maWdMaXN0OiBbXSwKICAgICAgdXNlckxpc3Q6IFtdLAogICAgICBzaG93SGFuZGxlRGlhbG9nOiBmYWxzZSwKICAgICAgaGFuZGxlRm9ybTogewogICAgICAgIGlkOiAnJywKICAgICAgICBoYW5kbGVEZXNjOiAnJywKICAgICAgICBoYW5kbGVTdGF0ZTogJycKICAgICAgfSwKICAgICAgaGFuZGxlUnVsZXM6IHsKICAgICAgICBoYW5kbGVTdGF0ZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5aSE55CG54q25oCBJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdCiAgICAgIH0sCiAgICAgIHNob3dBbGw6IGZhbHNlLAogICAgICB0aHJlYXRlbkRpY3Q6IFtdLAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGhhbmRsZVN0YXRlOiAnMCcsCiAgICAgIH0sCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgcmFuZ2VUaW1lOiBbXSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHRocmVhdGVuV2Fybkxpc3Q6IFtdLAogICAgICB0b3RhbDogMCwKICAgICAgdGl0bGU6ICcnLAogICAgICBvcGVuVGhyZW50ZW46IGZhbHNlLAogICAgICBmb3JtOiB7fSwKICAgICAgcnVsZXM6IHsKICAgICAgICB0aHJlYXRlbk5hbWU6IFsKICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiA1MDAsIG1lc3NhZ2U6ICflkYrorablkI3np7DkuI3og73otoXov4c1MDDlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZGK6K2m5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICAgIHBhdHRlcm46IC9eW15cc10rLywKICAgICAgICAgICAgbWVzc2FnZTogJ+S4jeiDveS7peepuuagvOW8gOWktO+8gScsCiAgICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgYWxhcm1MZXZlbDogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZGK6K2m562J57qnJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIHRocmVhdGVuVHlwZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5ZGK6K2m57G75Z6LJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIHJlYXNvbjogWwogICAgICAgICAge3JlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDIwMDAsIG1lc3NhZ2U6ICflkYrorabljp/lm6DkuI3og73otoXov4cyMDAw5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWRiuitpuWOn+WboCcsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgXSwKICAgICAgICBoYW5kU3VnZ2VzdDogWwogICAgICAgICAge3JlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDIwMDAsIG1lc3NhZ2U6ICflkYrorablu7rorq7kuI3og73otoUyMDAw5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICfor7fovpPlhaXlkYrorablu7rorq4nLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgICAgbG9nVGltZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5pel5b+X5pe26Ze0JywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIGNyZWF0ZVRpbWU6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWRiuitpuaXtumXtCcsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgXSwKICAgICAgICBzcmNJcDogWwogICAgICAgICAge3JlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDMwLCBtZXNzYWdlOiAn5rqQSVDkuI3og73otoXov4czMOWtl+espicsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBwYXR0ZXJuOiAnXigyNVswLTVdfDJbMC00XVxcZHxbMC0xXT9cXGQ/XFxkKShcXC4oMjVbMC01XXwyWzAtNF1cXGR8WzAtMV0/XFxkP1xcZCkpezN9JCcsCiAgICAgICAgICAgIG1lc3NhZ2U6ICJJUOWcsOWdgOS4jeiDveS4uuepuuaIluagvOW8j+S4jeato+ehriIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIHNyY1BvcnQ6IFsKICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAxMSwgbWVzc2FnZTogJ+a6kElQ56uv5Y+j5LiN6IO96LaF6L+HMTHlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBwYXR0ZXJuOiAnXlswLTldKlsxLTldWzAtOV0qJCcsIG1lc3NhZ2U6ICfmupBJUOerr+WPo+S4jeiDveS4uuepuuaIluagvOW8j+S4jeato+ehricsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgXSwKICAgICAgICBkZXN0SXA6IFsKICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAzMCwgbWVzc2FnZTogJ+ebruagh0lQ5LiN6IO96LaF6L+HMzDlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAgewogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgcGF0dGVybjogJ14oMjVbMC01XXwyWzAtNF1cXGR8WzAtMV0/XFxkP1xcZCkoXFwuKDI1WzAtNV18MlswLTRdXFxkfFswLTFdP1xcZD9cXGQpKXszfSQnLAogICAgICAgICAgICBtZXNzYWdlOiAiSVDlnLDlnYDkuI3og73kuLrnqbrmiJbmoLzlvI/kuI3mraPnoa4iLAogICAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgICBkZXN0UG9ydDogWwogICAgICAgICAge3JlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDExLCBtZXNzYWdlOiAn55uu5qCHSVDnq6/lj6PkuI3og73otoXov4cxMeWtl+espicsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIHBhdHRlcm46ICdeWzAtOV0qWzEtOV1bMC05XSokJywgbWVzc2FnZTogJ+ebruagh0lQ56uv5Y+j5LiN6IO95Li656m65oiW5qC85byP5LiN5q2j56GuJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIG1hdGVSdWxlOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IGZhbHNlLCBtaW46IDAsIG1heDogMjAwLCBtZXNzYWdlOiAn5YiG5p6Q6KeE5YiZ5LiN6IO96LaF6L+HMjAw5a2X56ymJywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIGFzc29jaWFEZXZpY2U6IFsKICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAyMDAsIG1lc3NhZ2U6ICflhbPogZTorr7lpIfkuI3og73otoXov4cyMDDlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgICAgYXR0YWNrVHlwZTogWwogICAgICAgICAge3JlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDEwMCwgbWVzc2FnZTogJ+aUu+WHu+aWueW8j+S4jeiDvei2hei/hzEwMOWtl+espicsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmlLvlh7vmlrnlvI8nLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgICAgYXR0YWNrU3RhZ2U6IFsKICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiAxMDAsIG1lc3NhZ2U6ICfmlLvlh7vpk77pmLbmrrXkuI3og73otoXov4cxMDDlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5pS75Ye76ZO+6Zi25q61JywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIGF0dGFja1Jlc3VsdDogWwogICAgICAgICAge3JlcXVpcmVkOiBmYWxzZSwgbWluOiAwLCBtYXg6IDEwMCwgbWVzc2FnZTogJ+aUu+WHu+e7k+aenOS4jeiDvei2hei/hzEwMOWtl+espicsIHRyaWdnZXI6ICdibHVyJ30sCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXmlLvlh7vnu5PmnpwnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIGJsb2NraW5nRm9ybToge30sCiAgICAgIGJsb2NraW5nUnVsZXM6IHsKICAgICAgICBibG9ja19pcDogWwogICAgICAgICAgLy/lj6/lkIzml7bkvKDlpJrkuKrvvIznlKgiOyLpmpTlvIAKICAgICAgICAgIHsgdmFsaWRhdG9yOiB2YWxpZGF0ZUJsb2NrSXAsIHRyaWdnZXI6ICdibHVyJyB9LAogICAgICAgIF0sCiAgICAgICAgZHVyYXRpb25fdGltZTogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6Zi75pat5pe26ZW/JywgdHJpZ2dlcjogJ2JsdXInfSwKICAgICAgICBdLAogICAgICAgIHJlbWFya3M6IFsKICAgICAgICAgIHtyZXF1aXJlZDogZmFsc2UsIG1pbjogMCwgbWF4OiA1MDAsIG1lc3NhZ2U6ICflpIfms6jkuI3og73otoXov4c1MDDlrZfnrKYnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0KICAgICAgfSwKICAgICAgYmxvY2tpbmdJcExpc3Q6IFtdLAogICAgICBibG9ja2luZ0RpYWxvZ1Zpc2libGU6IGZhbHNlLCAvLyDmibnph4/pmLvmlq3lvLnnqpcKICAgICAgZWRpdGFibGU6IHRydWUsCiAgICAgIGFzc2V0SW5mb0xpc3Q6IFtdLAogICAgICBvcGVuRGlhbG9nOiBmYWxzZSwKICAgICAgYXNzZXREYXRhOiB7fSwKICAgICAgaW1wb3J0RGlhbG9nOiBmYWxzZSwKICAgICAgc2VydmVyT3BlbjogZmFsc2UsCiAgICAgIGFzc2V0SWQ6IG51bGwsCiAgICAgIHNhZmVPcGVuOiBmYWxzZSwKICAgICAgdGhyZWF0ZW5Db25maWdGbGFnOiBmYWxzZSwKICAgICAgdmlld1N0cmF0ZWd5OiBmYWxzZSwKICAgICAgcHVibGlzaERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBmbG93VmlzaWJsZTogZmFsc2UsCiAgICAgIGZsb3dUZW1wbGF0ZVNlbGVjdFZpc2libGU6IGZhbHNlLAogICAgICBmbG93U3RhdGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflvoXlrqHmoLgnLAogICAgICAgICAgdmFsdWU6IDAKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5b6F5aSE572uJywKICAgICAgICAgIHZhbHVlOiAxCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogIuW+heWPjemmiOWuoeaguCIsCiAgICAgICAgICB2YWx1ZTogMiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5b6F6aqM6K+BJywKICAgICAgICAgIHZhbHVlOiAzCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+W3suWujOaIkCcsCiAgICAgICAgICB2YWx1ZTogNAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICflvoXmj5DkuqQnLAogICAgICAgICAgdmFsdWU6IC0xCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+acquWIhumFjScsCiAgICAgICAgICB2YWx1ZTogOTkKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGhhbmRsZVN0YXRlT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pyq5aSE572uJywKICAgICAgICAgIHZhbHVlOiAnMCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5bey5aSE572uJywKICAgICAgICAgIHZhbHVlOiAnMScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5b+955WlJywKICAgICAgICAgIHZhbHVlOiAnMicKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5aSE572u5LitJywKICAgICAgICAgIHZhbHVlOiAnMycKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGFjdGl2ZU5hbWU6ICdkZXRhaWwnLAogICAgICBzeW5jU3RhdGVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfmnKrlkIzmraUnLAogICAgICAgICAgdmFsdWU6IDAKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5bey5ZCM5q2lJywKICAgICAgICAgIHZhbHVlOiAxCiAgICAgICAgfQogICAgICBdLAogICAgICBibG9ja2luZ0R1cmF0aW9uOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICczMOWIhumSnycsCiAgICAgICAgICB2YWx1ZTogJzMwbScKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAnMjTlsI/ml7YnLAogICAgICAgICAgdmFsdWU6ICcyNGgnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJzQ45bCP5pe2JywKICAgICAgICAgIHZhbHVlOiAnNDhoJwogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICc35aSpJywKICAgICAgICAgIHZhbHVlOiAnMTY4aCcKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5rC45LmFJywKICAgICAgICAgIHZhbHVlOiAn5rC45LmFJwogICAgICAgIH0KICAgICAgXSwKICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdCiAgICB9CiAgfSwKICB3YXRjaDogewogICAgLy8g55uR5ZCs55uu5qCHaXAKICAgICdmb3JtLmRlc3RJcCcodmFsdWUsIG9sZFZhbHVlKSB7CiAgICAgIHZhciByZyA9IC9eKDI1WzAtNV18MlswLTRdXGR8WzAtMV0/XGQ/XGQpKFwuKDI1WzAtNV18MlswLTRdXGR8WzAtMV0/XGQ/XGQpKXszfSQvOwogICAgICB2YXIgcmVnID0gcmcudGVzdCh2YWx1ZSk7CiAgICAgIGlmIChyZWcpIHsKICAgICAgICAvLyDmoLnmja5pcOiOt+WPlui1hOS6p+aVsOaNrgogICAgICAgIGdldEFzc2V0SW5mb0J5SXAodmFsdWUpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEubGVuZ3RoKSB7CiAgICAgICAgICAgIGxldCBhc3NldERhdGEgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgICBhc3NldERhdGEuZm9yRWFjaChpdGVtID0+IGl0ZW0udmFsdWUgPSBpdGVtLmFzc2V0TmFtZSArICctJyArIGl0ZW0uYXNzZXRUeXBlRGVzYyk7CiAgICAgICAgICAgIGlmICh2YWx1ZSAhPT0gb2xkVmFsdWUgJiYgb2xkVmFsdWUpIHsKICAgICAgICAgICAgICB0aGlzLmZvcm0uYXNzZXRJZCA9ICcnCiAgICAgICAgICAgICAgdGhpcy5mb3JtLmRlcHRJZCA9ICcnCiAgICAgICAgICAgIH0KICAgICAgICAgICAgLy8g6LWE5Lqn5pWw5o2u5pyJ5aSa5p2h5pi+56S65LiL5ouJ5qGG77yM5Y+q5pyJ5LiA5p2h5LiN5pi+56S6CiAgICAgICAgICAgIGlmIChhc3NldERhdGEubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICAgICAgdGhpcy5mb3JtLmFzc2V0SWQgPSBhc3NldERhdGFbMF0uYXNzZXRJZAogICAgICAgICAgICAgIHRoaXMuZm9ybS5kZXB0SWQgPSBhc3NldERhdGFbMF0uZGVwdElkCiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKGFzc2V0RGF0YS5sZW5ndGggPiAxICYmICF0aGlzLmZvcm0uYXNzZXRJZCkgewogICAgICAgICAgICAgIHRoaXMuZm9ybS5hc3NldElkID0gJycKICAgICAgICAgICAgICB0aGlzLmZvcm0uZGVwdElkID0gJycKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmFzc2V0SW5mb0xpc3QgPSBhc3NldERhdGE7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLmFzc2V0SW5mb0xpc3QgPSBbXTsKICAgICAgICAgICAgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pyq5p+l6K+i5Yiw6LWE5Lqn5pWw5o2uJyk7CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmFzc2V0SW5mb0xpc3QgPSBbXTsKICAgICAgICB0aGlzLmZvcm0uYXNzZXRJZCA9ICcnOwogICAgICAgIHRoaXMuZm9ybS5kZXB0SWQgPSAnJzsKICAgICAgfQogICAgfSwKICAgIHByb3BzQWN0aXZlTmFtZSgpIHsKICAgICAgdGhpcy5pbml0KCkKICAgIH0sCiAgICBwcm9wc1F1ZXJ5UGFyYW1zOiB7CiAgICAgIGhhbmRsZXIodmFsKSB7CiAgICAgICAgdGhpcy5oYW5kbGVQcm9wc1F1ZXJ5KHZhbCk7CiAgICAgIH0KICAgIH0sCiAgICAvKnJhbmdlVGltZSh2YWwpIHsKICAgICAgY29uc29sZS5sb2codmFsKQogICAgfSwqLwogICAgJ2Jsb2NraW5nRm9ybS5ibG9ja19pcCc6IHsKICAgICAgaGFuZGxlcih2YWx1ZSkgewogICAgICAgIGlmICh2YWx1ZSkgewogICAgICAgICAgdGhpcy5ibG9ja2luZ0lwTGlzdCA9IHZhbHVlLnNwbGl0KCc7JykubWFwKGlwID0+IGlwLnRyaW0oKSkuZmlsdGVyKGlwID0+IGlwKTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0RGV2aWNlQ29uZmlnTGlzdCgpOwogIH0sCiAgbW91bnRlZCgpIHsKICAgIGlmICghdGhpcy4kcm91dGUucXVlcnkgfHwgT2JqZWN0LmtleXModGhpcy4kcm91dGUucXVlcnkpLmxlbmd0aCA8IDEpIHsKICAgICAgdGhpcy5pbml0KCkKICAgIH0gZWxzZSB7CiAgICAgIHRoaXMuaGFuZGxlUHJvcHNRdWVyeSh0aGlzLiRyb3V0ZS5xdWVyeSk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXREZXZpY2VDb25maWdMaXN0KCl7CiAgICAgIGxpc3REZXZpY2VDb25maWcoe3F1ZXJ5QWxsRGF0YTogdHJ1ZX0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmRldmljZUNvbmZpZ0xpc3QgPSByZXMucm93czsKICAgICAgfSkKICAgIH0sCiAgICBpbml0KCkgewogICAgICAvL3RoaXMucmVzZXRRdWVyeSgpCiAgICAgIHRoaXMuZ2V0VGhyZWF0ZW5EaWN0KCkKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpCiAgICAgIHRoaXMuZ2V0RGVwdHNEYXRhKCkKICAgICAgdGhpcy5nZXRVc2VyTGlzdCgpCiAgICB9LAogICAgZ2V0VXNlckxpc3QoKXsKICAgICAgbGlzdFVzZXIoe3BhZ2VOdW06MSxwYWdlU2l6ZToxMDAwfSkudGhlbihyZXM9PnsKICAgICAgICBpZiAocmVzLnJvd3MpewogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlcy5yb3dzCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnByb3BzUXVlcnlQYXJhbXMuYWxhcm1MZXZlbCA9IHRoaXMucXVlcnlQYXJhbXMuYWxhcm1MZXZlbAogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6Y3VycmVudEJ0bicsdGhpcy5xdWVyeVBhcmFtcy5hbGFybUxldmVsP3BhcnNlSW50KHRoaXMucXVlcnlQYXJhbXMuYWxhcm1MZXZlbCkgOiBudWxsKQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gey4uLnRoaXMucXVlcnlQYXJhbXMsLi4udGhpcy5wcm9wc1F1ZXJ5UGFyYW1zfTsKICAgICAgaWYgKHRoaXMucmFuZ2VUaW1lICE9IG51bGwpIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSA9IHBhcnNlVGltZSh0aGlzLnJhbmdlVGltZVswXSk7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gcGFyc2VUaW1lKHRoaXMucmFuZ2VUaW1lWzFdKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSA9IG51bGw7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gbnVsbDsKICAgICAgfQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VTaXplID0gMTA7CgogICAgICBpZighdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUpewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhcnRUaW1lID0gcGFyc2VUaW1lKG5ldyBEYXRlKCkuc2V0SG91cnMoLTE2OCwgMCwgMCwgMCksICd7eX0te219LXtkfSAwMDowMDowMCcpOyAvLyDkuIDlkajliY3vvIzml7bpl7Tpg6jliIbkuLogMDA6MDA6MDAKICAgICAgfQogICAgICBpZighdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lKXsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmVuZFRpbWUgPSBwYXJzZVRpbWUobmV3IERhdGUoKS5zZXRIb3VycygyMywgNTksIDU5LCA5OTkpLCAne3l9LXttfS17ZH0gMjM6NTk6NTknKTsgLy8g5b2T5YmN5pel5pyf77yM5pe26Ze06YOo5YiG5Li6IDIzOjU5OjU5CiAgICAgIH0KICAgICAgdGhpcy5yYW5nZVRpbWUgPSBbdGhpcy5xdWVyeVBhcmFtcy5zdGFydFRpbWUsIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZV07CiAgICAgIHRoaXMudG90YWwgPSAwOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIGNvbnN0IGRhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMucXVlcnlQYXJhbXMpKQogICAgICAgIGlmIChkYXRhLnRocmVhdGVuVHlwZSAhPSBudWxsKSB7CiAgICAgICAgICBkYXRhLnRocmVhdGVuVHlwZSA9IGRhdGEudGhyZWF0ZW5UeXBlLmpvaW4oJy8nKTsKICAgICAgICB9CiAgICAgICAgdGhpcy4kcmVmcy5hdGNBZ2UuaW5pdEF0dGFja1N0YWdlKGRhdGEpCiAgICAgIH0pCiAgICB9LAogICAgLy8g6I635Y+W5ZGK6K2m57G75Z6L5aSa57qn5a2X5YW45pWw5o2uCiAgICBnZXRUaHJlYXRlbkRpY3QoKSB7CiAgICAgIGdldE11bFR5cGVEaWN0KHsKICAgICAgICBkaWN0VHlwZTogJ3RocmVhdGVuX2FsYXJtX3R5cGUnCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnRocmVhdGVuRGljdCA9IHJlcy5kYXRhOwogICAgICB9KQogICAgfSwKICAgIC8vIOiOt+WPlumDqOmXqOaVsOaNrgogICAgZ2V0RGVwdHNEYXRhKCkgewogICAgICBnZXREZXB0U3lzdGVtKCkudGhlbihyZXMgPT4gdGhpcy5kZXB0T3B0aW9ucyA9IHJlcy5kYXRhKQogICAgfSwKICAgIGhhbmRsZUNoYW5nZSh2YWwpIHsKICAgICAgLy8g6I635Y+W5omA5bGe6YOo6Zeo5pyA5ZCOaWQKICAgICAgaWYgKHZhbCkgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gdmFsW3ZhbC5sZW5ndGggLSAxXTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCA9ICcnOwogICAgICB9CiAgICB9LAogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsKICAgICAgICB0aHJlYXRlbk5hbWU6IG51bGwsCiAgICAgICAgdGhyZWF0ZW5UeXBlOiBudWxsLAogICAgICAgIGFsYXJtTGV2ZWw6IG51bGwsCiAgICAgICAgc3JjSXA6IG51bGwsCiAgICAgICAgZGVzdElwOiBudWxsLAogICAgICAgIGhhbmRsZVN0YXRlOiAnMCcsCiAgICAgICAgZmxvd1N0YXRlOiBudWxsLAogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfTsKICAgICAgbGV0IGF0Y0FnZSA9IHRoaXMuJHJlZnMuYXRjQWdlOwogICAgICBpZiAoYXRjQWdlKSB7CiAgICAgICAgYXRjQWdlLmN1cnJlbnRTZWxlY3RlZENhcmQgPSBudWxsOwogICAgICB9CiAgICAgIHRoaXMucmFuZ2VUaW1lID0gbnVsbDsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8v5paw5aKe5aiB6IOB5oOF5oqlCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMub3BlblRocmVudGVuID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgIHRoaXMuZWRpdGFibGUgPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIuaWsOWinuWogeiDgeaDheaKpSI7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdhc3NldElkJywgJycpOyAvLyDop6PlhrNlbC1zZWxlY3Tml6Dms5Xop4blm77kuI7mlbDmja7nmoTmm7TmlrAKICAgIH0sCiAgICAvLyDlr7zlhaXlip/og70KICAgIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy5pbXBvcnREaWFsb2cgPSB0cnVlOwogICAgfSwKICAgIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgKICAgICAgICAiL3N5c3RlbS9ob25leXBvdEFsYXJtL2V4cG9ydCIsCiAgICAgICAgewogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywKICAgICAgICB9LAogICAgICAgIGDlqIHog4HlkYroraZfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGAKICAgICAgKTsKICAgIH0sCgogICAgLy8g6I635Y+W5YiX6KGo5pWw5o2u5p+l6K+iCiAgICBoYW5kbGVSb3dDbGljayhyb3csIGNvbHVtbiwgZXZlbnQpIHsKICAgICAgLy8g6I635Y+W5ZGK6K2m6K+m5oOF5Y2V5Liq5Y2V5YWD5qC85pWw5o2u6L+b6KGM562b6YCJCiAgICAgIGlmIChyb3cgJiYgcm93LmlkKSB7CiAgICAgICAgaWYgKGNvbHVtbi5wcm9wZXJ0eSkgewogICAgICAgICAgaWYgKGNvbHVtbi5wcm9wZXJ0eSA9PT0gJ2Zsb3dTdGF0ZScpIHsKICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtc1tjb2x1bW4ucHJvcGVydHldID0gIXJvd1tjb2x1bW4ucHJvcGVydHldID8gOTkgOiBOdW1iZXIocm93W2NvbHVtbi5wcm9wZXJ0eV0pOwogICAgICAgICAgICBsaXN0QWxhcm0oewogICAgICAgICAgICAgIFtjb2x1bW4ucHJvcGVydHldOiAhcm93W2NvbHVtbi5wcm9wZXJ0eV0gPyA5OSA6IE51bWJlcihyb3dbY29sdW1uLnByb3BlcnR5XSksCiAgICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICAgICAgdGhpcy50aHJlYXRlbldhcm5MaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICAgICAgICB0aGlzLnRocmVhdGVuV2Fybkxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgICAgIGl0ZW0uYXNzZXRUeXBlID0gaXRlbS5hc3NldENsYXNzRGVzYyArICctJyArIGl0ZW0uYXNzZXRUeXBlRGVzYzsKICAgICAgICAgICAgICAgIGlmIChpdGVtLmFzc2V0VHlwZSA9PSAnbnVsbC1udWxsJykgewogICAgICAgICAgICAgICAgICBpdGVtLmFzc2V0VHlwZSA9IG51bGw7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfSBlbHNlIGlmIChjb2x1bW4ucHJvcGVydHkgPT09ICd0aHJlYXRlblR5cGUnKSB7CiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXNbY29sdW1uLnByb3BlcnR5XSA9IHJvd1tjb2x1bW4ucHJvcGVydHldLnNwbGl0KCcvJyk7CiAgICAgICAgICB9IGVsc2UgaWYgKGNvbHVtbi5wcm9wZXJ0eSA9PT0gJ2FsYXJtTGV2ZWwnKSB7CiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXNbY29sdW1uLnByb3BlcnR5XSA9IHJvd1tjb2x1bW4ucHJvcGVydHldLnRvU3RyaW5nKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zW2NvbHVtbi5wcm9wZXJ0eV0gPSByb3dbY29sdW1uLnByb3BlcnR5XTsKICAgICAgICAgIH0KICAgICAgICAgIGxpc3RBbGFybSh7CiAgICAgICAgICAgIFtjb2x1bW4ucHJvcGVydHldOiByb3dbY29sdW1uLnByb3BlcnR5XSwKICAgICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgdGhpcy50aHJlYXRlbldhcm5MaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICAgICAgdGhpcy50aHJlYXRlbldhcm5MaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgICAgaXRlbS5hc3NldFR5cGUgPSBpdGVtLmFzc2V0Q2xhc3NEZXNjICsgJy0nICsgaXRlbS5hc3NldFR5cGVEZXNjOwogICAgICAgICAgICAgIGlmIChpdGVtLmFzc2V0VHlwZSA9PSAnbnVsbC1udWxsJykgewogICAgICAgICAgICAgICAgaXRlbS5hc3NldFR5cGUgPSBudWxsOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgLy8g5aSa6YCJCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMubXVsdGlwbGVTZWxlY3Rpb24gPSB2YWw7CiAgICB9LAoKICAgIGZsb3dTdGF0ZUZvcm1hdHRlcihyb3csIGNvbHVtbiwgY2VsbFZhbHVlLCBpbmRleCkgewogICAgICBsZXQgbmFtZSA9ICfmnKrliIbphY0nOwogICAgICBsZXQgbWF0Y2ggPSB0aGlzLmZsb3dTdGF0ZU9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0udmFsdWUgPT0gY2VsbFZhbHVlKTsKICAgICAgaWYgKG1hdGNoKSB7CiAgICAgICAgbmFtZSA9IG1hdGNoLmxhYmVsOwogICAgICB9CiAgICAgIHJldHVybiBuYW1lOwogICAgfSwKICAgIGRpc3Bvc2VyRm9ybWF0dGVyKHJvdywgY29sdW1uLCBjZWxsVmFsdWUsIGluZGV4KXsKICAgICAgbGV0IG5hbWUgPSAnJzsKICAgICAgaWYgKGNlbGxWYWx1ZSl7CiAgICAgICAgdGhpcy51c2VyTGlzdC5mb3JFYWNoKGUgPT4gewogICAgICAgICAgaWYgKGUudXNlcklkID09IGNlbGxWYWx1ZSl7CiAgICAgICAgICAgIG5hbWUgPSBlLm5pY2tOYW1lCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICByZXR1cm4gbmFtZTsKICAgICAgfQogICAgICByZXR1cm4gbmFtZTsKICAgIH0sCgoKICAgIGhhbmRsZVN0YXRlRm9ybWF0dGVyKHJvdywgY29sdW1uLCBjZWxsVmFsdWUsIGluZGV4KSB7CiAgICAgIGxldCBuYW1lID0gJ+acquWkhOe9ric7CiAgICAgIGxldCBtYXRjaCA9IHRoaXMuaGFuZGxlU3RhdGVPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLnZhbHVlID09IGNlbGxWYWx1ZSk7CiAgICAgIGlmIChtYXRjaCkgewogICAgICAgIG5hbWUgPSBtYXRjaC5sYWJlbDsKICAgICAgfQogICAgICByZXR1cm4gbmFtZTsKICAgIH0sCiAgICBoYW5kbGVEZXRhaWwocm93KSB7CiAgICAgIHRoaXMuYXNzZXREYXRhID0gey4uLnJvd307CiAgICAgIHRoaXMudGl0bGUgPSAi5p+l55yL5ZGK6K2m6K+m5oOFIjsKICAgICAgdGhpcy5vcGVuRGV0YWlsKHRydWUpOwogICAgfSwKICAgIHNob3dIYW5kbGUocm93KSB7CiAgICAgIC8vIOiOt+WPluS6i+S7tuivpuaDheWNleS4quWNleWFg+agvOaVsOaNrui/m+ihjOetm+mAiQogICAgICBpZiAocm93LmhhbmRsZVN0YXRlID09PSAnMScgfHwgcm93LmhhbmRsZVN0YXRlID09PSAnMicgKSB7CiAgICAgICAgdGhpcy5oYW5kbGVGb3JtLmhhbmRsZVN0YXRlID0gcGFyc2VJbnQocm93LmhhbmRsZVN0YXRlKTsKICAgICAgICB0aGlzLmhhbmRsZUZvcm0uaGFuZGxlRGVzYyA9IHJvdy5oYW5kbGVEZXNjOwogICAgICB9ZWxzZSB7CiAgICAgICAgdGhpcy5oYW5kbGVGb3JtID0gewogICAgICAgICAgaGFuZGxlRGVzYzogJycsCiAgICAgICAgICBoYW5kbGVTdGF0ZTogJycKICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5oYW5kbGVGb3JtLmlkID0gcm93LmlkOwogICAgICB0aGlzLnNob3dIYW5kbGVEaWFsb2cgPSB0cnVlOwogICAgfSwKICAgIGhhbmRsZUVkaXQocm93KSB7CiAgICAgIGdldEFsYXJtKHJvdy5pZCkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHsuLi5yZXMuZGF0YX07CiAgICAgICAgaWYgKHRoaXMuZm9ybS5hbGFybUxldmVsICE9IG51bGwpIHsKICAgICAgICAgIHRoaXMuZm9ybS5hbGFybUxldmVsID0gKHRoaXMuZm9ybS5hbGFybUxldmVsKS50b1N0cmluZygpOwogICAgICAgIH0KICAgICAgICBpZiAodGhpcy5mb3JtLnRocmVhdGVuVHlwZSAhPSBudWxsKSB7CiAgICAgICAgICB0aGlzLmZvcm0udGhyZWF0ZW5UeXBlID0gdGhpcy5mb3JtLnRocmVhdGVuVHlwZS5zcGxpdCgnLycpOwogICAgICAgIH0KICAgICAgICBpZiAodGhpcy5mb3JtLmF0dGFja051bSAhPSBudWxsKSB7CiAgICAgICAgICB0aGlzLmZvcm0uYXR0YWNrTnVtID0gKHRoaXMuZm9ybS5hdHRhY2tOdW0pLnRvU3RyaW5nKCk7CiAgICAgICAgfQogICAgICAgIGlmICh0aGlzLmZvcm0uc3JjUG9ydCAhPSBudWxsKSB7CiAgICAgICAgICB0aGlzLmZvcm0uc3JjUG9ydCA9ICh0aGlzLmZvcm0uc3JjUG9ydCkudG9TdHJpbmcoKTsKICAgICAgICB9CiAgICAgICAgaWYgKHRoaXMuZm9ybS5kZXN0UG9ydCAhPSBudWxsKSB7CiAgICAgICAgICB0aGlzLmZvcm0uZGVzdFBvcnQgPSAodGhpcy5mb3JtLmRlc3RQb3J0KS50b1N0cmluZygpOwogICAgICAgIH0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWogeiDgeaDheaKpSI7CiAgICAgICAgdGhpcy5vcGVuVGhyZW50ZW4gPSB0cnVlOwogICAgICB9KQogICAgfSwKICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgaWRzID0gcm93LmlkOwogICAgICBjb25zdCB0aXRsZSA9IHJvdy50aHJlYXRlbk5hbWU7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOWRiuitpuWQjeensOS4uuOAkCcgKyB0aXRsZSArICfjgJHnmoTmlbDmja7pobk/JykudGhlbigoKSA9PiB7CiAgICAgICAgcmV0dXJuIGRlbEFsYXJtKGlkcyk7CiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKCiAgICAgIH0pCiAgICB9LAogICAgYWRkT3JVcGRhdGVGbG93SGFuZGxlKGlkLCBmbG93U3RhdGUsIHJvdykgewogICAgICBsZXQgZGF0YSA9IHsKICAgICAgICBpZDogaWQgfHwgJycsCiAgICAgICAgZm9ybVR5cGU6IDEsCiAgICAgICAgb3BUeXBlOiBmbG93U3RhdGUgPyAwIDogJy0xJywKICAgICAgICBzdGF0dXM6IGZsb3dTdGF0ZSwKICAgICAgICByb3c6IHJvdywKICAgICAgICBpc1dvcms6IHRydWUKICAgICAgfQogICAgICBkYXRhLnJvdy53b3JrVHlwZSA9ICcyJzsKICAgICAgZGF0YS5yb3cuZXZlbnRUeXBlID0gMzsKICAgICAgZGF0YS5vcmlnaW5UeXBlID0gJ2V2ZW50JzsKICAgICAgdGhpcy5jdXJyZW50Rmxvd0RhdGEgPSBkYXRhOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLmdldENvbmZpZ0tleSgiZGVmYXVsdC5mbG93VGVtcGxhdGVJZCIpLnRoZW4ocmVzID0+IHsKICAgICAgICBsZXQgZmxvd0lkID0gcmVzLm1zZzsKICAgICAgICBpZiAoZmxvd0lkKSB7CiAgICAgICAgICB0aGlzLmdldEZsb3dFbmdpbmVJbmZvKGZsb3dJZCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuZmxvd1RlbXBsYXRlU2VsZWN0VmlzaWJsZSA9IHRydWU7CiAgICAgICAgfQogICAgICB9KS5maW5hbGx5KCgpID0+IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSkKICAgIH0sCiAgICBnZXRGbG93RW5naW5lSW5mbyh2YWwpIHsKICAgICAgRmxvd0VuZ2luZUluZm8odmFsKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5kYXRhICYmIHJlcy5kYXRhLmZsb3dUZW1wbGF0ZUpzb24pIHsKICAgICAgICAgIGxldCBkYXRhID0gSlNPTi5wYXJzZShyZXMuZGF0YS5mbG93VGVtcGxhdGVKc29uKTsKICAgICAgICAgIGlmICghZGF0YVswXS5mbG93SWQpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+l5rWB56iL5qih5p2/5byC5bi4LOivt+mHjeaWsOmAieaLqScpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy5jdXJyZW50Rmxvd0RhdGEuZmxvd0lkID0gZGF0YVswXS5mbG93SWQ7CiAgICAgICAgICAgIHRoaXMuZmxvd1Zpc2libGUgPSB0cnVlOwogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5GbG93Qm94LmluaXQodGhpcy5jdXJyZW50Rmxvd0RhdGEpOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KQogICAgfSwKICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxldCBxdWVyeVBhcmFtcyA9IHsKICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zCiAgICAgIH07CiAgICAgIGlmIChxdWVyeVBhcmFtcy50aHJlYXRlblR5cGUgIT0gbnVsbCkgewogICAgICAgIHF1ZXJ5UGFyYW1zLnRocmVhdGVuVHlwZSA9IHF1ZXJ5UGFyYW1zLnRocmVhdGVuVHlwZS5qb2luKCcvJyk7CiAgICAgIH0KICAgICAgLy/lkIzmraXor7fmsYLnsbvlnovnu5/orqHmlbDmja4KICAgICAgdGhpcy4kZW1pdCgnZ2V0TGlzdCcsey4uLnF1ZXJ5UGFyYW1zfSk7CiAgICAgIGxpc3RBbGFybShxdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy50aHJlYXRlbldhcm5MaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRocmVhdGVuV2Fybkxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0uYXNzZXRUeXBlID0gaXRlbS5hc3NldENsYXNzRGVzYyArICctJyArIGl0ZW0uYXNzZXRUeXBlRGVzYzsKICAgICAgICAgIGlmIChpdGVtLmFzc2V0VHlwZSA9PSAnbnVsbC1udWxsJykgewogICAgICAgICAgICBpdGVtLmFzc2V0VHlwZSA9IG51bGw7CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoaXRlbS5kZXB0TmFtZSkgewogICAgICAgICAgICBsZXQgZGVwdE5hbWVBcnIgPSB1bmlxdWVBcnIoaXRlbS5kZXB0TmFtZS5zcGxpdCgnLCcpKTsKICAgICAgICAgICAgaXRlbS5kZXB0TmFtZSA9IGRlcHROYW1lQXJyLmpvaW4oJywnKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUNsb3NlKGRvbmUpIHsKICAgICAgZG9uZSgpOwogICAgICB0aGlzLmZvcm0gPSB7fTsKICAgICAgdGhpcy4kcmVmcy5mb3JtLnJlc2V0RmllbGRzKCk7CiAgICB9LAogICAgc3VibWl0SGFuZGxlRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siaGFuZGxlU3RhdGVGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdXBkYXRlQWxhcm0odGhpcy5oYW5kbGVGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5aSE572u5oiQ5YqfIik7CiAgICAgICAgICAgIHRoaXMuaGFuZGxlRm9ybSA9IHt9OwogICAgICAgICAgICB0aGlzLnNob3dIYW5kbGVEaWFsb2cgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgc3VibWl0Rm9ybSgpIHsKICAgICAgaWYgKHRoaXMuZm9ybS50aHJlYXRlblR5cGUgIT0gbnVsbCkgewogICAgICAgIHRoaXMuZm9ybS50aHJlYXRlblR5cGUgPSB0aGlzLmZvcm0udGhyZWF0ZW5UeXBlLmpvaW4oJy8nKTsKICAgICAgfQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCA9PSBudWxsKSB7CiAgICAgICAgICAgIGFkZEFsYXJtKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgICAgICAgICAgdGhpcy5vcGVuVGhyZW50ZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHVwZGF0ZUFsYXJtKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgICAgICAgICAgdGhpcy5vcGVuVGhyZW50ZW4gPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgfSkKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW5UaHJlbnRlbiA9IGZhbHNlOwogICAgICB0aGlzLiRyZWZzLmZvcm0ucmVzZXRGaWVsZHMoKTsKICAgIH0sCiAgICBvcGVuRGV0YWlsKHZhbCkgewogICAgICB0aGlzLm9wZW5EaWFsb2cgPSB2YWw7CiAgICB9LAogICAgY2xvc2VEaWFsb2coKSB7CiAgICAgIHRoaXMuaW1wb3J0RGlhbG9nID0gZmFsc2U7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICBjbG9zZUFzc2V0RGlhbG9nKCkgewogICAgICB0aGlzLnNlcnZlck9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5zYWZlT3BlbiA9IGZhbHNlOwogICAgICB0aGlzLm5ldHdvcmtPcGVuID0gZmFsc2U7CiAgICB9LAogICAgY2xvc2VGbG93KGlzclJlZnJlc2gpIHsKICAgICAgdGhpcy5mbG93VmlzaWJsZSA9IGZhbHNlCiAgICAgIGlmIChpc3JSZWZyZXNoKSB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICBmbG93VGVtcGxhdGVTZWxlY3RDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuZmxvd1RlbXBsYXRlU2VsZWN0VmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLmZsb3dWaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5jdXJyZW50Rmxvd0RhdGEuZmxvd0lkID0gdmFsOwogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy4kcmVmcy5GbG93Qm94LmluaXQodGhpcy5jdXJyZW50Rmxvd0RhdGEpCiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlQXRjQWdlQ2xpY2soYXRjQWdlKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXR0YWNrU2VnID0gYXRjQWdlOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgaGFuZGxlUHJvcHNRdWVyeSh2YWwpIHsKICAgICAgaWYgKHZhbCAmJiBPYmplY3Qua2V5cyh2YWwpLmxlbmd0aCA+IDApIHsKICAgICAgICBpZiAodmFsLmF0dGFja1NlZyAmJiB0aGlzLiRyZWZzLmF0Y0FnZSkgewogICAgICAgICAgdGhpcy4kcmVmcy5hdGNBZ2UuY3VycmVudFNlbGVjdGVkQ2FyZCA9IHZhbC5hdHRhY2tTZWc7CiAgICAgICAgfQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB2YWw7CiAgICAgICAgaWYgKHZhbC5zdGFydFRpbWUgJiYgdmFsLmVuZFRpbWUpIHsKICAgICAgICAgIHRoaXMucmFuZ2VUaW1lID0gW3ZhbC5zdGFydFRpbWUsIHZhbC5lbmRUaW1lXTsKICAgICAgICB9CiAgICAgICAgaWYgKHZhbC5oYW5kbGUgPT0gJzEnKSB7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmhhbmRsZVN0YXRlID0gJzEnCiAgICAgICAgfSBlbHNlIGlmICh2YWwuaGFuZGxlID09ICcwJykgewogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5oYW5kbGVTdGF0ZSA9ICcwJwogICAgICAgIH0KICAgICAgICBpZiAodmFsLmRhdGFzb3VyY2UpIHsKICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGF0YVNvdXJjZSA9IHBhcnNlSW50KHZhbC5kYXRhc291cmNlKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5nZXRUaHJlYXRlbkRpY3QoKQogICAgICAgIHRoaXMuZ2V0RGVwdHNEYXRhKCkKICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUFwcGxpY2F0aW9uVGFnU2hvdyhhcHBsaWNhdGlvbkxpc3QpIHsKICAgICAgaWYgKCFhcHBsaWNhdGlvbkxpc3QgfHwgYXBwbGljYXRpb25MaXN0Lmxlbmd0aCA8IDEpIHsKICAgICAgICByZXR1cm4gJyc7CiAgICAgIH0KICAgICAgbGV0IHJlc3VsdCA9IGFwcGxpY2F0aW9uTGlzdFswXS5hc3NldE5hbWU7CiAgICAgIGlmIChhcHBsaWNhdGlvbkxpc3QubGVuZ3RoID4gMSkgewogICAgICAgIHJlc3VsdCArPSAnLi4uJzsKICAgICAgfQogICAgICByZXR1cm4gcmVzdWx0OwogICAgfSwKCiAgICBoYW5kbGVCbG9ja2luZygpIHsKICAgICAgaWYgKHRoaXMubXVsdGlwbGVTZWxlY3Rpb24ubGVuZ3RoIDwgMSkgcmV0dXJuIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB6Zi75pat55qEaXAnKTsKICAgICAgdGhpcy5ibG9ja2luZ0RpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICBsZXQgYXJyID0gdGhpcy5tdWx0aXBsZVNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLnNyY0lwKTsKICAgICAgYXJyID0gQXJyYXkuZnJvbShuZXcgU2V0KGFycikpOwogICAgICB0aGlzLiRzZXQodGhpcy5ibG9ja2luZ0Zvcm0sJ2Jsb2NrX2lwJyxhcnIuam9pbignOycpKTsKICAgIH0sCiAgICBibG9ja2luZ1N1Ym1pdCgpIHsKICAgICAgdGhpcy4kcmVmc1siYmxvY2tpbmdGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgYWRkQmxvY2tJcCh0aGlzLmJsb2NraW5nRm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+a3u+WKoOaIkOWKnycpOwogICAgICAgICAgfSkuZmluYWxseSgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuYmxvY2tpbmdEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMuJHJlZnMubXVsdGlwbGVUYWJsZS5jbGVhclNlbGVjdGlvbigpOwogICAgICAgICAgICB0aGlzLm11bHRpcGxlU2VsZWN0aW9uID0gW107CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgfQp9Cg=="}, {"version": 3, "sources": ["honeypotAlarmList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6l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file": "honeypotAlarmList.vue", "sourceRoot": "src/views/frailty/event/component", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"最近告警时间\" label-width=\"98px\">\n                <el-date-picker\n                  v-model=\"rangeTime\"\n                  type=\"datetimerange\"\n                  range-separator=\"至\"\n                  start-placeholder=\"开始日期\"\n                  end-placeholder=\"结束日期\"\n                  :default-time=\"['00:00:00', '23:59:59']\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n                <el-select\n                  clearable\n                  v-model=\"queryParams.alarmLevel\"\n                  placeholder=\"请选择告警等级\"\n                >\n                  <el-option\n                    v-for=\"dict in dict.type.threaten_type\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置状态\" prop=\"\">\n                <el-select v-model=\"queryParams.handleState\" placeholder=\"请选择处置状态\" clearable>\n                  <el-option :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\n                             v-for=\"(item,index) in handleStateOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警类型\" prop=\"threatenType\">\n                <el-cascader v-model=\"queryParams.threatenType\" :options=\"threatenDict\" clearable\n                             :props=\"{ label: 'dictLabel', value: 'dictValue' }\" placeholder=\"请选择告警类型\">\n                  <template slot-scope=\"{ node, data }\">\n                    <span>{{ data.dictLabel }}</span>\n                    <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                  </template>\n                </el-cascader>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleQuery\">查询\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-down\" @click=\"showAll=true\" v-if=\"!showAll\">\n                  展开\n                </el-button>\n                <el-button class=\"btn2\" size=\"small\" icon=\"el-icon-arrow-up\" @click=\"showAll=false\" v-else>收起\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"告警名称\" prop=\"threatenName\">\n                <el-input\n                  v-model=\"queryParams.threatenName\"\n                  placeholder=\"请输入告警名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"源IP\" prop=\"srcIp\">\n                <el-input\n                  v-model=\"queryParams.srcIp\"\n                  placeholder=\"请输入源IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"目标IP\" prop=\"destIp\">\n                <el-input\n                  v-model=\"queryParams.destIp\"\n                  placeholder=\"请输入目标IP\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"通报状态\" prop=\"flowState\">\n                <el-select v-model=\"queryParams.flowState\" placeholder=\"请选择通报状态\" clearable>\n                  <el-option :key=\"item.value\" :label=\"item.label\" :value=\"item.value\"\n                             v-for=\"(item,index) in flowStateOptions\"/>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"处置人\">\n                <el-input\n                  v-model=\"queryParams.disposer\"\n                  placeholder=\"请输入处置人\"\n                  clearable\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"同步状态\">\n                <el-select v-model=\"queryParams.synchronizationStatus\" placeholder=\"请选择同步状态\" filterable clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.synchronization_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  ></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"10\" v-if=\"showAll\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属部门\" prop=\"deptId\">\n                <dept-select v-model=\"queryParams.deptId\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"6\">\n              <el-form-item label=\"所属探针\">\n                <el-select v-model=\"queryParams.deviceConfigId\" filterable clearable placeholder=\"请选择\">\n                  <el-option\n                    v-for=\"item in deviceConfigList\"\n                    :key=\"item.id\"\n                    :label=\"item.deviceName\"\n                    :value=\"item.id\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n<!--      <div class=\"custom-content-search-chunk\" style=\"margin-bottom: 8px\">\n        <attack-stage ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\"/>\n      </div>-->\n      <div class=\"custom-content-container\"\n           :style=\"showAll ? { height: 'calc(100% - 298px)' } :{ height: 'calc(100% - 208px)' }\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">蜜罐告警列表</span></div>\n          <div style=\"width: 60%; margin-left: 10%;\">\n<!--            <attack-stage-text ref=\"atcAge\" @handleClick=\"handleAtcAgeClick\" :dataSource=\"7\" />-->\n            <attack-stage-text ref=\"atcAge\" :dataSource=\"7\" />\n          </div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-col :span=\"1.5\">\n                  <el-button\n                    class=\"btn1\"\n                    size=\"small\"\n                    @click=\"handleBlocking\"\n                  >批量阻断</el-button>\n                </el-col>\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  @click=\"handleExport\"\n                  v-hasPermi=\"['system:threadten:export']\"\n                >导出\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          ref=\"multipleTable\"\n          @row-click=\"handleRowClick\"\n          @selection-change=\"handleSelectionChange\"\n          :data=\"threatenWarnList\">\n          <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n<!--          <el-table-column type=\"index\" width=\"100\" label=\"序号\"/>-->\n          <el-table-column label=\"最近告警时间\" width=\"200\" prop=\"updateTime\"/>\n          <el-table-column label=\"告警名称\" prop=\"threatenName\" min-width=\"260\"/>\n          <el-table-column label=\"告警类型\" prop=\"threatenType\" width=\"150\"/>\n          <el-table-column label=\"告警等级\" prop=\"alarmLevel\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <dict-tag :options=\"dict.type.threaten_type\" :value=\"scope.row.alarmLevel\"/>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"源IP\" prop=\"srcIp\" width=\"180\">\n            <template slot-scope=\"scope\">\n              <div style=\"display: flex; align-items: center; justify-content: flex-start\">\n                <span>{{ scope.row.srcIp }}</span>\n                <img v-if=\"scope.row.isBlocking\" style=\"width: 24px;margin-left: 10px\" src=\"@/assets/images/block.png\" alt=\"\">\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"目标IP/应用\" width=\"150\" prop=\"destIp\">\n          </el-table-column>\n          <el-table-column label=\"处置人\" prop=\"disposer\" width=\"150\" :formatter=\"disposerFormatter\">\n          </el-table-column>\n          <el-table-column label=\"关联业务系统\" prop=\"businessApplicationList\" width=\"200\">\n            <template slot-scope=\"scope\">\n              <el-tooltip placement=\"bottom-end\" effect=\"light\"\n                          v-if=\"scope.row.businessApplications && scope.row.businessApplications.length > 0\">\n                <div slot=\"content\">\n                  <div v-for=\"(item,tagIndex) in scope.row.businessApplications\" :key=\"item.assetId\"\n                       class=\"overflow-tag\" v-if=\"tagIndex <= 5\">\n                    <el-tag type=\"primary\"><span>{{ item.assetName }}</span></el-tag>\n                  </div>\n                  <div v-if=\"scope.row.businessApplications.length > 5\">\n                    <el-tag type=\"primary\"><span>...</span></el-tag>\n                  </div>\n                </div>\n                <el-tag type=\"primary\" class=\"asset-tag\">\n                  <span>{{ handleApplicationTagShow(scope.row.businessApplications) }}</span></el-tag>\n              </el-tooltip>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"通报状态\" prop=\"flowState\" width=\"150\" :formatter=\"flowStateFormatter\"/>\n          <el-table-column label=\"处置状态\" prop=\"handleState\" width=\"150\" :formatter=\"handleStateFormatter\"/>\n          <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\"/>\n          <el-table-column label=\"发现次数\" prop=\"alarmNum\" width=\"150\"/>\n          <el-table-column label=\"同步状态\" prop=\"synchronizationStatus\" width=\"150\">\n            <template slot-scope=\"scope\">\n              <span v-if=\"scope.row.synchronizationStatus === '0'\">未同步</span>\n              <span v-else-if=\"scope.row.synchronizationStatus === '1'\">已同步</span>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"250\" fixed=\"right\" :show-overflow-tooltip=\"false\"\n                           class-name=\"small-padding fixed-width\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleDetail(scope.row)\"\n                v-hasPermi=\"['system:threadten:query']\"\n              >详情\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         @click=\"handleEdit(scope.row)\"\n                         v-hasPermi=\"['system:threadten:edit']\"\n              >编辑\n              </el-button>\n              <el-button v-if=\"scope.row.workId==null && !(scope.row.handleState === '3')\"\n                         size=\"mini\"\n                         type=\"text\"\n                         class=\"table-delBtn\"\n                         @click=\"handleDelete(scope.row)\"\n                         v-hasPermi=\"['system:threadten:remove']\"\n              >删除\n              </el-button>\n              <el-button\n                v-if=\"scope.row.workId==null && !(scope.row.handleState === '1' || scope.row.handleState === '3')\"\n                size=\"mini\"\n                type=\"text\"\n                @click=\"showHandle(scope.row)\"\n                v-hasPermi=\"['system:threadten:edit']\"\n              >处置\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                v-if=\"!(scope.row.handleState === '1' || scope.row.handleState === '3') && (scope.row.flowState == null || scope.row.flowState === '99')\"\n                @click=\"addOrUpdateFlowHandle(null,null,scope.row)\"\n              >创建通报\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n    <!-- 处置威胁情报对话框! -->\n    <el-dialog\n      title=\"快速处置\"\n      :visible.sync=\"showHandleDialog\"\n      width=\"600px\"\n      append-to-body\n    >\n      <el-form ref=\"handleStateForm\" :model=\"handleForm\" :rules=\"handleRules\" label-width=\"106px\">\n        <el-form-item label=\"处置状态\" prop=\"handleState\">\n          <el-select v-model=\"handleForm.handleState\" clearable placeholder=\"请选择处置状态\">\n            <el-option v-for=\"dict in dict.type.handle_state\"\n                       :key=\"dict.value\" :label=\"dict.label\"\n                       :value=\"parseInt(dict.value)\"></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"处置说明\" prop=\"handleDesc\">\n          <el-input type=\"textarea\" :rows=\"2\" v-model=\"handleForm.handleDesc\" placeholder=\"请输入处置说明\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHandleForm\">确 定</el-button>\n        <el-button @click=\"showHandleDialog = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n    <!-- 添加或修改威胁情报对话框! -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"openThrenten\"\n      width=\"80%\"\n      append-to-body\n      :before-close=\"handleClose\"\n    >\n      <el-row>\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"106px\" :disabled=\"!editable\">\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\"></el-divider>\n            <div class=\"my-title\">基本信息</div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"告警名称\" prop=\"threatenName\">\n              <el-input v-model=\"form.threatenName\" placeholder=\"请输入告警名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警等级\" prop=\"alarmLevel\">\n              <el-select v-model=\"form.alarmLevel\" placeholder=\"请选择告警等级\" clearable>\n                <el-option\n                  v-for=\"dict in dict.type.threaten_type\"\n                  :key=\"dict.value\"\n                  :label=\"dict.label\"\n                  :value=\"dict.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警类型\" prop=\"threatenType\">\n              <el-cascader v-model=\"form.threatenType\" :options=\"threatenDict\" clearable placeholder=\"请选择告警类型\"\n                           :props=\"{ label: 'dictLabel', value: 'dictValue' }\" style=\"width: 100%\">\n                <template slot-scope=\"{ node, data }\">\n                  <span>{{ data.dictLabel }}</span>\n                  <span v-if=\"!node.isLeaf\"> ({{ data.children.length }}) </span>\n                </template>\n              </el-cascader>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"原始报文\" prop=\"playload\">\n              <el-input v-model=\"form.playload\" :autosize=\"{minRows: 3, maxRows: 8}\" type=\"textarea\"\n                        placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"处置建议\" prop=\"handSuggest\">\n              <el-input v-model=\"form.handSuggest\" :autosize=\"{minRows: 3, maxRows: 3}\" type=\"textarea\"\n                        placeholder=\"请输入告警建议\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"告警时间\" prop=\"createTime\">\n              <el-date-picker\n                v-model=\"form.createTime\"\n                type=\"date\"\n                placeholder=\"选择告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"最近告警时间\" prop=\"updateTime\">\n              <el-date-picker\n                v-model=\"form.updateTime\"\n                type=\"date\"\n                placeholder=\"选择最近告警时间\"\n                format=\"yyyy 年 MM 月 dd 日\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"威胁标签\" prop=\"label\">\n              <DynamicTag v-model=\"form.label\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"关联设备\" prop=\"associaDevice\">\n              <el-input\n                v-model=\"form.associaDevice\"\n                placeholder=\"请输入关联设备\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" class=\"mb8\">\n            <el-divider direction=\"vertical\"></el-divider>\n            <div class=\"my-title\">攻击关系</div>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP\" prop=\"srcIp\">\n              <el-input\n                style=\"width: 50%\"\n                v-model=\"form.srcIp\"\n                placeholder=\"请输入源IP\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"源IP端口\" prop=\"srcPort\">\n              <el-input\n                style=\"width: 30%\"\n                v-model=\"form.srcPort\"\n                placeholder=\"请输入源IP端口\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP/应用\" prop=\"destIp\">\n              <el-input\n                style=\"width: 50%\"\n                v-model=\"form.destIp\"\n                placeholder=\"请输入目标IP\"\n              ></el-input>\n              <!--目标ip查询后有多个及显示资产数据，提交表单传(assetId： 资产id,deptId：部门id)-->\n              <el-select style=\"width: 50%;\" v-show=\"assetInfoList.length >= 2\" v-model=\"form.assetId\"\n                         placeholder=\"请确认疑似资产\">\n                <el-option v-for=\"item in assetInfoList\" :key=\"item.assetId\" :label=\"item.value\"\n                           :value=\"item.assetId\"></el-option>\n              </el-select>\n              <el-select style=\"width: 50%;\" v-show=\"false\" v-model=\"form.deptId\" placeholder=\"请选择资产\">\n                <el-option v-for=\"item in assetInfoList\" :label=\"item.value\" :value=\"item.deptId\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"目标IP端口\" prop=\"destPort\">\n              <el-input\n                style=\"width: 30%\"\n                v-model=\"form.destPort\"\n                placeholder=\"请输入目标IP端口\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col v-if=\"form.fileUrl!=null||editable\">\n            <el-col :span=\"24\" class=\"mb8\">\n              <el-divider direction=\"vertical\"></el-divider>\n              <div class=\"my-title\">文件上传</div>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传文件\" prop=\"fileUrl\">\n                <file-upload v-model=\"form.fileUrl\"\n                             :disUpload=\"!editable\"\n                             :limit=\"5\"\n                             :file-type=\"['doc', 'xls', 'ppt', 'txt', 'pdf', 'png', 'jpg', 'jpeg']\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-col>\n        </el-form>\n      </el-row>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button v-if=\"editable\" type=\"primary\" @click=\"submitForm\"\n        >确 定\n        </el-button\n        >\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      v-if=\"openDialog\"\n      :title=\"title\"\n      :visible.sync=\"openDialog\"\n      width=\"80%\"\n      class=\"my-dialog\"\n      append-to-body\n    >\n      <el-tabs type=\"border-card\" v-model=\"activeName\">\n        <el-tab-pane label=\"事件详情\" name=\"detail\">\n          <alarm-detail\n            v-if=\"openDialog\"\n            @openDetail=\"openDetail\"\n            :data-source=\"7\"\n            :asset-data=\"assetData\"\n          />\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.srcIp\" label=\"攻击IP关联事件\" name=\"attack\">\n          <attack-detail :detail-type=\"'attack'\" :host-ip=\"assetData.srcIp\" :current-asset-data=\"assetData\"/>\n        </el-tab-pane>\n        <el-tab-pane v-if=\"assetData.destIp\" label=\"受害IP关联事件\" name=\"suffer\">\n          <suffer-detail :detail-type=\"'suffer'\" :host-ip=\"assetData.destIp\" :current-asset-data=\"assetData\"/>\n        </el-tab-pane>\n      </el-tabs>\n    </el-dialog>\n\n    <el-dialog title=\"导入威胁告警\" :visible.sync=\"importDialog\" width=\"800px\" append-to-body>\n      <import-threaten @closeDialog=\"closeDialog\" v-if=\"importDialog\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看服务器资产\" :visible.sync=\"serverOpen\" width=\"80%\" append-to-body>\n      <server-add :asset-id=\"assetId\" @cancel=\"closeAssetDialog()\" :editable=\"editable\" v-if=\"serverOpen\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看安全设备资产\" :visible.sync=\"safeOpen\" width=\"80%\" append-to-body>\n      <safe-add :asset-id=\"assetId\" @cancel=\"closeAssetDialog()\" :editable=\"editable\" v-if=\"safeOpen\"/>\n    </el-dialog>\n\n    <el-dialog title=\"查看告警策略\" :visible.sync=\"viewStrategy\" width=\"400\" append-to-body>\n      <view-strategy v-if=\"viewStrategy\" @close=\"viewStrategy=false\"/>\n    </el-dialog>\n\n\n    <el-dialog title=\"告警策略配置\" :visible.sync=\"threatenConfigFlag\" width=\"800\" append-to-body>\n      <threaten-config-list v-if=\"threatenConfigFlag\" @close=\"threatenConfigFlag=false\"/>\n    </el-dialog>\n\n    <el-dialog title=\"批量阻断\" :visible.sync=\"blockingDialogVisible\" width=\"400\">\n      <el-form :model=\"blockingForm\" :rules=\"blockingRules\" ref=\"blockingForm\" class=\"blocking-form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断ip\" prop=\"block_ip\">\n              <span slot=\"label\">\n                阻断ip\n                <template>\n                  <el-tooltip placement=\"top\">\n                    <div slot=\"content\">默认加载选择的事件的源IP，多个则以“;”隔开</div>\n                    <i class=\"el-icon-info\"></i>\n                  </el-tooltip>\n                </template>\n              </span>\n              <el-input v-model=\"blockingForm.block_ip\" placeholder=\"请输入ip\">\n                <el-popover\n                  slot=\"suffix\"\n                  placement=\"bottom\"\n                  width=\"100\"\n                  trigger=\"hover\"\n                >\n                  <ul>\n                    <li v-for=\"(ip, index) in blockingIpList\" :key=\"index\">{{ ip }}</li>\n                  </ul>\n                  <i slot=\"reference\" class=\"el-icon-more\"></i>\n                </el-popover>\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"阻断时长\" prop=\"duration_time\">\n              <el-select v-model=\"blockingForm.duration_time\" placeholder=\"请选择阻断时长\">\n                <el-option\n                  v-for=\"item in blockingDuration\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-form-item label=\"备注\" prop=\"remarks\">\n            <el-input v-model=\"blockingForm.remarks\" type=\"textarea\" maxlength=\"500\" show-word-limit placeholder=\"请输入阻断描述\"></el-input>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"blockingSubmit\">确 定</el-button>\n        <el-button @click=\"blockingDialogVisible = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <FlowBox v-if=\"flowVisible\" ref=\"FlowBox\" @close=\"closeFlow\"/>\n    <flow-template-select :show.sync=\"flowTemplateSelectVisible\" @change=\"flowTemplateSelectChange\"/>\n\n    <publish-click-dialog\n      :publish-dialog-visible=\"publishDialogVisible\"\n      @updateVisible=\"(val) => { this.publishDialogVisible = val}\"\n      title=\"发布告警事件\"\n      width=\"30%\"/>\n  </div>\n</template>\n\n<script>\nimport {parseTime} from \"@/utils/ruoyi\";\nimport {getMulTypeDict} from \"@/api/system/dict/data\";\nimport {getDeptSystem} from \"@/api/monitor2/applicationAssets\";\nimport {getAlarm, delAlarm, listAlarm, addAlarm, updateAlarm,addBlockIp} from \"@/api/threaten/honeypotAlarm\";\nimport {getAssetInfoByIp} from \"@/api/safe/overview\";\nimport DynamicTag from \"../../../../components/DynamicTag\";\nimport AlarmDetail from \"../../../basis/securityWarn/alarmDetail\";\nimport importThreaten from \"@/views/basis/securityWarn/importThreaten.vue\"\nimport ThreatenConfigList from \"@/views/basis/securityWarn/threatenConfigList.vue\"\nimport ServerAdd from \"../../../hhlCode/component/application/adds/serverAdd\";\nimport SafeAdd from \"../../../hhlCode/component/application/adds/safeAdd\";\nimport ViewStrategy from \"../../../basis/securityWarn/viewStrategy\";\nimport PublishClickDialog from \"../../../basis/securityWarn/publishClickDialog\";\nimport FlowBox from \"../../../zeroCode/workFlow/components/FlowBox\";\nimport FlowTemplateSelect from \"../../../../components/FlowTemplateSelect\";\nimport AttackStage from \"../../../threat/overview/attackStage\";\nimport AttackViewList from \"./attackViewList\";\nimport SufferViewList from \"./sufferViewList\";\nimport attackDetail from \"./detail/index.vue\";\nimport sufferDetail from \"./detail/index.vue\";\nimport DeptSelect from '@/views/components/select/deptSelect.vue'\nimport {uniqueArr} from '@/utils'\nimport {FlowEngineInfo} from \"@/api/lowCode/FlowEngine\";\nimport {listUser} from \"@/api/system/user\";\nimport AttackStageText from '@/views/threat/overview/attackStageText.vue'\nimport {listDeviceConfig} from \"@/api/ffsafe/deviceConfig\";\n\nexport default {\n  name: \"eventList\",\n  components: {\n    AttackStageText,\n    DeptSelect,\n    SufferViewList,\n    AttackViewList,\n    AttackStage,\n    FlowTemplateSelect,\n    FlowBox,\n    PublishClickDialog,\n    attackDetail,\n    sufferDetail,\n    ThreatenConfigList, ViewStrategy, SafeAdd, ServerAdd, importThreaten, AlarmDetail, DynamicTag\n  },\n  dicts: ['threaten_type', 'attack_stage', 'attack_result','handle_state', 'synchronization_status'],\n  props: {\n    propsActiveName: {\n      type: String\n    },\n    propsQueryParams: {\n      type: Object,\n      default: function () {\n        return null\n      }\n    },\n    currentBtn: {\n      type: Number\n    }\n  },\n  data() {\n    let validateBlockIp = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('IP不能为空'));\n      }\n      // let pattern = /^((1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2})\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0)\\.(1[0-9]{2}|2[0-4][0-9]|25[0-5]|(\\d){1,2}|0))$/;\n      let pattern = /^\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\\s*;\\s*((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))*\\s*$/;\n      if (!pattern.test(value)) {\n        return callback(new Error('请输入正确的IP'));\n      }\n      return callback();\n    };\n    return {\n      deviceConfigList: [],\n      userList: [],\n      showHandleDialog: false,\n      handleForm: {\n        id: '',\n        handleDesc: '',\n        handleState: ''\n      },\n      handleRules: {\n        handleState: [\n          {required: true, message: '请选择处理状态', trigger: 'blur'},\n        ]\n      },\n      showAll: false,\n      threatenDict: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        handleState: '0',\n      },\n      deptOptions: [],\n      rangeTime: [],\n      loading: false,\n      threatenWarnList: [],\n      total: 0,\n      title: '',\n      openThrenten: false,\n      form: {},\n      rules: {\n        threatenName: [\n          {required: false, min: 0, max: 500, message: '告警名称不能超过500字符', trigger: 'blur'},\n          {required: true, message: '请输入告警名称', trigger: 'blur'},\n          {\n            required: true,\n            pattern: /^[^\\s]+/,\n            message: '不能以空格开头！',\n            trigger: 'blur'\n          }\n        ],\n        alarmLevel: [\n          {required: true, message: '请输入告警等级', trigger: 'blur'},\n        ],\n        threatenType: [\n          {required: true, message: '请输入告警类型', trigger: 'blur'},\n        ],\n        reason: [\n          {required: false, min: 0, max: 2000, message: '告警原因不能超过2000字符', trigger: 'blur'},\n          {required: true, message: '请输入告警原因', trigger: 'blur'},\n        ],\n        handSuggest: [\n          {required: false, min: 0, max: 2000, message: '告警建议不能超2000字符', trigger: 'blur'},\n          {required: false, message: '请输入告警建议', trigger: 'blur'},\n        ],\n        logTime: [\n          {required: true, message: '请输入日志时间', trigger: 'blur'},\n        ],\n        createTime: [\n          {required: true, message: '请输入告警时间', trigger: 'blur'},\n        ],\n        srcIp: [\n          {required: false, min: 0, max: 30, message: '源IP不能超过30字符', trigger: 'blur'},\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        srcPort: [\n          {required: false, min: 0, max: 11, message: '源IP端口不能超过11字符', trigger: 'blur'},\n          {required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '源IP端口不能为空或格式不正确', trigger: 'blur'},\n        ],\n        destIp: [\n          {required: false, min: 0, max: 30, message: '目标IP不能超过30字符', trigger: 'blur'},\n          {\n            required: true,\n            pattern: '^(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)(\\\\.(25[0-5]|2[0-4]\\\\d|[0-1]?\\\\d?\\\\d)){3}$',\n            message: \"IP地址不能为空或格式不正确\",\n            trigger: \"blur\"\n          },\n        ],\n        destPort: [\n          {required: false, min: 0, max: 11, message: '目标IP端口不能超过11字符', trigger: 'blur'},\n          {required: true, pattern: '^[0-9]*[1-9][0-9]*$', message: '目标IP端口不能为空或格式不正确', trigger: 'blur'},\n        ],\n        mateRule: [\n          {required: false, min: 0, max: 200, message: '分析规则不能超过200字符', trigger: 'blur'},\n        ],\n        associaDevice: [\n          {required: false, min: 0, max: 200, message: '关联设备不能超过200字符', trigger: 'blur'},\n        ],\n        attackType: [\n          {required: false, min: 0, max: 100, message: '攻击方式不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击方式', trigger: 'blur'},\n        ],\n        attackStage: [\n          {required: false, min: 0, max: 100, message: '攻击链阶段不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击链阶段', trigger: 'blur'},\n        ],\n        attackResult: [\n          {required: false, min: 0, max: 100, message: '攻击结果不能超过100字符', trigger: 'blur'},\n          {required: true, message: '请输入攻击结果', trigger: 'blur'},\n        ],\n      },\n      blockingForm: {},\n      blockingRules: {\n        block_ip: [\n          //可同时传多个，用\";\"隔开\n          { validator: validateBlockIp, trigger: 'blur' },\n        ],\n        duration_time: [\n          {required: true, message: '请选择阻断时长', trigger: 'blur'},\n        ],\n        remarks: [\n          {required: false, min: 0, max: 500, message: '备注不能超过500字符', trigger: 'blur'},\n        ]\n      },\n      blockingIpList: [],\n      blockingDialogVisible: false, // 批量阻断弹窗\n      editable: true,\n      assetInfoList: [],\n      openDialog: false,\n      assetData: {},\n      importDialog: false,\n      serverOpen: false,\n      assetId: null,\n      safeOpen: false,\n      threatenConfigFlag: false,\n      viewStrategy: false,\n      publishDialogVisible: false,\n      flowVisible: false,\n      flowTemplateSelectVisible: false,\n      flowStateOptions: [\n        {\n          label: '待审核',\n          value: 0\n        },\n        {\n          label: '待处置',\n          value: 1\n        },\n        {\n          label: \"待反馈审核\",\n          value: 2,\n        },\n        {\n          label: '待验证',\n          value: 3\n        },\n        {\n          label: '已完成',\n          value: 4\n        },\n        {\n          label: '待提交',\n          value: -1\n        },\n        {\n          label: '未分配',\n          value: 99\n        }\n      ],\n      handleStateOptions: [\n        {\n          label: '未处置',\n          value: '0'\n        },\n        {\n          label: '已处置',\n          value: '1'\n        },\n        {\n          label: '忽略',\n          value: '2'\n        },\n        {\n          label: '处置中',\n          value: '3'\n        }\n      ],\n      activeName: 'detail',\n      syncStateOptions: [\n        {\n          label: '未同步',\n          value: 0\n        },\n        {\n          label: '已同步',\n          value: 1\n        }\n      ],\n      blockingDuration: [\n        {\n          label: '30分钟',\n          value: '30m'\n        },\n        {\n          label: '24小时',\n          value: '24h'\n        },\n        {\n          label: '48小时',\n          value: '48h'\n        },\n        {\n          label: '7天',\n          value: '168h'\n        },\n        {\n          label: '永久',\n          value: '永久'\n        }\n      ],\n      multipleSelection: []\n    }\n  },\n  watch: {\n    // 监听目标ip\n    'form.destIp'(value, oldValue) {\n      var rg = /^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$/;\n      var reg = rg.test(value);\n      if (reg) {\n        // 根据ip获取资产数据\n        getAssetInfoByIp(value).then(response => {\n          if (response.data.length) {\n            let assetData = response.data;\n            assetData.forEach(item => item.value = item.assetName + '-' + item.assetTypeDesc);\n            if (value !== oldValue && oldValue) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            // 资产数据有多条显示下拉框，只有一条不显示\n            if (assetData.length === 1) {\n              this.form.assetId = assetData[0].assetId\n              this.form.deptId = assetData[0].deptId\n            }\n            if (assetData.length > 1 && !this.form.assetId) {\n              this.form.assetId = ''\n              this.form.deptId = ''\n            }\n            this.assetInfoList = assetData;\n          } else {\n            this.assetInfoList = [];\n            return this.$message.warning('未查询到资产数据');\n          }\n        })\n      } else {\n        this.assetInfoList = [];\n        this.form.assetId = '';\n        this.form.deptId = '';\n      }\n    },\n    propsActiveName() {\n      this.init()\n    },\n    propsQueryParams: {\n      handler(val) {\n        this.handlePropsQuery(val);\n      }\n    },\n    /*rangeTime(val) {\n      console.log(val)\n    },*/\n    'blockingForm.block_ip': {\n      handler(value) {\n        if (value) {\n          this.blockingIpList = value.split(';').map(ip => ip.trim()).filter(ip => ip);\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getDeviceConfigList();\n  },\n  mounted() {\n    if (!this.$route.query || Object.keys(this.$route.query).length < 1) {\n      this.init()\n    } else {\n      this.handlePropsQuery(this.$route.query);\n    }\n  },\n  methods: {\n    getDeviceConfigList(){\n      listDeviceConfig({queryAllData: true}).then(res => {\n        this.deviceConfigList = res.rows;\n      })\n    },\n    init() {\n      //this.resetQuery()\n      this.getThreatenDict()\n      this.handleQuery()\n      this.getDeptsData()\n      this.getUserList()\n    },\n    getUserList(){\n      listUser({pageNum:1,pageSize:1000}).then(res=>{\n        if (res.rows){\n          this.userList = res.rows\n        }\n      })\n    },\n    handleQuery() {\n      this.propsQueryParams.alarmLevel = this.queryParams.alarmLevel\n      this.$emit('update:currentBtn',this.queryParams.alarmLevel?parseInt(this.queryParams.alarmLevel) : null)\n      this.queryParams = {...this.queryParams,...this.propsQueryParams};\n      if (this.rangeTime != null) {\n        this.queryParams.startTime = parseTime(this.rangeTime[0]);\n        this.queryParams.endTime = parseTime(this.rangeTime[1]);\n      } else {\n        this.queryParams.startTime = null;\n        this.queryParams.endTime = null;\n      }\n      this.queryParams.pageNum = 1;\n      this.queryParams.pageSize = 10;\n\n      if(!this.queryParams.startTime){\n        this.queryParams.startTime = parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'); // 一周前，时间部分为 00:00:00\n      }\n      if(!this.queryParams.endTime){\n        this.queryParams.endTime = parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'); // 当前日期，时间部分为 23:59:59\n      }\n      this.rangeTime = [this.queryParams.startTime, this.queryParams.endTime];\n      this.total = 0;\n      this.getList();\n      this.$nextTick(() => {\n        const data = JSON.parse(JSON.stringify(this.queryParams))\n        if (data.threatenType != null) {\n          data.threatenType = data.threatenType.join('/');\n        }\n        this.$refs.atcAge.initAttackStage(data)\n      })\n    },\n    // 获取告警类型多级字典数据\n    getThreatenDict() {\n      getMulTypeDict({\n        dictType: 'threaten_alarm_type'\n      }).then(res => {\n        this.threatenDict = res.data;\n      })\n    },\n    // 获取部门数据\n    getDeptsData() {\n      getDeptSystem().then(res => this.deptOptions = res.data)\n    },\n    handleChange(val) {\n      // 获取所属部门最后id\n      if (val) {\n        this.queryParams.deptId = val[val.length - 1];\n      } else {\n        this.queryParams.deptId = '';\n      }\n    },\n    resetQuery() {\n      this.queryParams = {\n        threatenName: null,\n        threatenType: null,\n        alarmLevel: null,\n        srcIp: null,\n        destIp: null,\n        handleState: '0',\n        flowState: null,\n        updateTime: null,\n        pageNum: 1,\n        pageSize: 10\n      };\n      let atcAge = this.$refs.atcAge;\n      if (atcAge) {\n        atcAge.currentSelectedCard = null;\n      }\n      this.rangeTime = null;\n      this.handleQuery();\n    },\n    //新增威胁情报\n    handleAdd() {\n      this.openThrenten = true;\n      this.form = {};\n      this.editable = true;\n      this.title = \"新增威胁情报\";\n      this.$set(this.form, 'assetId', ''); // 解决el-select无法视图与数据的更新\n    },\n    // 导入功能\n    handleImport() {\n      this.importDialog = true;\n    },\n    handleExport() {\n      this.download(\n        \"/system/honeypotAlarm/export\",\n        {\n          ...this.queryParams,\n        },\n        `威胁告警_${new Date().getTime()}.xlsx`\n      );\n    },\n\n    // 获取列表数据查询\n    handleRowClick(row, column, event) {\n      // 获取告警详情单个单元格数据进行筛选\n      if (row && row.id) {\n        if (column.property) {\n          if (column.property === 'flowState') {\n            this.queryParams[column.property] = !row[column.property] ? 99 : Number(row[column.property]);\n            listAlarm({\n              [column.property]: !row[column.property] ? 99 : Number(row[column.property]),\n              pageNum: 1,\n              pageSize: 10\n            }).then(response => {\n              this.threatenWarnList = response.rows;\n              this.threatenWarnList.forEach(item => {\n                item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n                if (item.assetType == 'null-null') {\n                  item.assetType = null;\n                }\n              });\n              this.total = response.total;\n              this.loading = false;\n            });\n            return;\n          } else if (column.property === 'threatenType') {\n            this.queryParams[column.property] = row[column.property].split('/');\n          } else if (column.property === 'alarmLevel') {\n            this.queryParams[column.property] = row[column.property].toString();\n          } else {\n            this.queryParams[column.property] = row[column.property];\n          }\n          listAlarm({\n            [column.property]: row[column.property],\n            pageNum: 1,\n            pageSize: 10\n          }).then(response => {\n            this.threatenWarnList = response.rows;\n            this.threatenWarnList.forEach(item => {\n              item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n              if (item.assetType == 'null-null') {\n                item.assetType = null;\n              }\n            });\n            this.total = response.total;\n            this.loading = false;\n          });\n        }\n      }\n    },\n\n    // 多选\n    handleSelectionChange(val) {\n      this.multipleSelection = val;\n    },\n\n    flowStateFormatter(row, column, cellValue, index) {\n      let name = '未分配';\n      let match = this.flowStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    disposerFormatter(row, column, cellValue, index){\n      let name = '';\n      if (cellValue){\n        this.userList.forEach(e => {\n          if (e.userId == cellValue){\n            name = e.nickName\n          }\n        })\n        return name;\n      }\n      return name;\n    },\n\n\n    handleStateFormatter(row, column, cellValue, index) {\n      let name = '未处置';\n      let match = this.handleStateOptions.find(item => item.value == cellValue);\n      if (match) {\n        name = match.label;\n      }\n      return name;\n    },\n    handleDetail(row) {\n      this.assetData = {...row};\n      this.title = \"查看告警详情\";\n      this.openDetail(true);\n    },\n    showHandle(row) {\n      // 获取事件详情单个单元格数据进行筛选\n      if (row.handleState === '1' || row.handleState === '2' ) {\n        this.handleForm.handleState = parseInt(row.handleState);\n        this.handleForm.handleDesc = row.handleDesc;\n      }else {\n        this.handleForm = {\n          handleDesc: '',\n          handleState: ''\n        }\n      }\n      this.handleForm.id = row.id;\n      this.showHandleDialog = true;\n    },\n    handleEdit(row) {\n      getAlarm(row.id).then(res => {\n        this.form = {...res.data};\n        if (this.form.alarmLevel != null) {\n          this.form.alarmLevel = (this.form.alarmLevel).toString();\n        }\n        if (this.form.threatenType != null) {\n          this.form.threatenType = this.form.threatenType.split('/');\n        }\n        if (this.form.attackNum != null) {\n          this.form.attackNum = (this.form.attackNum).toString();\n        }\n        if (this.form.srcPort != null) {\n          this.form.srcPort = (this.form.srcPort).toString();\n        }\n        if (this.form.destPort != null) {\n          this.form.destPort = (this.form.destPort).toString();\n        }\n        this.title = \"修改威胁情报\";\n        this.openThrenten = true;\n      })\n    },\n    handleDelete(row) {\n      const ids = row.id;\n      const title = row.threatenName;\n      this.$modal.confirm('是否确认删除告警名称为【' + title + '】的数据项?').then(() => {\n        return delAlarm(ids);\n      }).then(() => {\n        this.$message.success(\"删除成功\");\n        this.getList();\n      }).catch(() => {\n\n      })\n    },\n    addOrUpdateFlowHandle(id, flowState, row) {\n      let data = {\n        id: id || '',\n        formType: 1,\n        opType: flowState ? 0 : '-1',\n        status: flowState,\n        row: row,\n        isWork: true\n      }\n      data.row.workType = '2';\n      data.row.eventType = 3;\n      data.originType = 'event';\n      this.currentFlowData = data;\n      this.loading = true;\n      this.getConfigKey(\"default.flowTemplateId\").then(res => {\n        let flowId = res.msg;\n        if (flowId) {\n          this.getFlowEngineInfo(flowId);\n        } else {\n          this.flowTemplateSelectVisible = true;\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getFlowEngineInfo(val) {\n      FlowEngineInfo(val).then(res => {\n        if (res.data && res.data.flowTemplateJson) {\n          let data = JSON.parse(res.data.flowTemplateJson);\n          if (!data[0].flowId) {\n            this.$message.error('该流程模板异常,请重新选择');\n          } else {\n            this.currentFlowData.flowId = data[0].flowId;\n            this.flowVisible = true;\n            this.$nextTick(() => {\n              this.$refs.FlowBox.init(this.currentFlowData);\n            });\n          }\n        }\n      }).finally(() => {\n        this.loading = false;\n      })\n    },\n    getList() {\n      this.loading = true;\n      let queryParams = {\n        ...this.queryParams\n      };\n      if (queryParams.threatenType != null) {\n        queryParams.threatenType = queryParams.threatenType.join('/');\n      }\n      //同步请求类型统计数据\n      this.$emit('getList',{...queryParams});\n      listAlarm(queryParams).then(response => {\n        this.threatenWarnList = response.rows;\n        this.threatenWarnList.forEach(item => {\n          item.assetType = item.assetClassDesc + '-' + item.assetTypeDesc;\n          if (item.assetType == 'null-null') {\n            item.assetType = null;\n          }\n          if (item.deptName) {\n            let deptNameArr = uniqueArr(item.deptName.split(','));\n            item.deptName = deptNameArr.join(',');\n          }\n        });\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleClose(done) {\n      done();\n      this.form = {};\n      this.$refs.form.resetFields();\n    },\n    submitHandleForm() {\n      this.$refs[\"handleStateForm\"].validate(valid => {\n        if (valid) {\n          updateAlarm(this.handleForm).then(res => {\n            this.$message.success(\"处置成功\");\n            this.handleForm = {};\n            this.showHandleDialog = false;\n            this.getList();\n          })\n        }\n      });\n    },\n    submitForm() {\n      if (this.form.threatenType != null) {\n        this.form.threatenType = this.form.threatenType.join('/');\n      }\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id == null) {\n            addAlarm(this.form).then(res => {\n              this.$message.success(\"新增成功\");\n              this.form = {};\n              this.openThrenten = false;\n              this.getList();\n            })\n          } else {\n            updateAlarm(this.form).then(res => {\n              this.$message.success(\"修改成功\");\n              this.form = {};\n              this.openThrenten = false;\n              this.getList();\n            })\n          }\n        }\n      })\n    },\n    cancel() {\n      this.openThrenten = false;\n      this.$refs.form.resetFields();\n    },\n    openDetail(val) {\n      this.openDialog = val;\n    },\n    closeDialog() {\n      this.importDialog = false;\n      this.handleQuery();\n    },\n    closeAssetDialog() {\n      this.serverOpen = false;\n      this.safeOpen = false;\n      this.networkOpen = false;\n    },\n    closeFlow(isrRefresh) {\n      this.flowVisible = false\n      if (isrRefresh) this.getList();\n    },\n    flowTemplateSelectChange(val) {\n      this.flowTemplateSelectVisible = false;\n      this.flowVisible = true;\n      this.currentFlowData.flowId = val;\n      this.$nextTick(() => {\n        this.$refs.FlowBox.init(this.currentFlowData)\n      })\n    },\n    handleAtcAgeClick(atcAge) {\n      this.queryParams.attackSeg = atcAge;\n      this.handleQuery();\n    },\n    handlePropsQuery(val) {\n      if (val && Object.keys(val).length > 0) {\n        if (val.attackSeg && this.$refs.atcAge) {\n          this.$refs.atcAge.currentSelectedCard = val.attackSeg;\n        }\n        this.queryParams = val;\n        if (val.startTime && val.endTime) {\n          this.rangeTime = [val.startTime, val.endTime];\n        }\n        if (val.handle == '1') {\n          this.queryParams.handleState = '1'\n        } else if (val.handle == '0') {\n          this.queryParams.handleState = '0'\n        }\n        if (val.datasource) {\n          this.queryParams.dataSource = parseInt(val.datasource);\n        }\n        this.getThreatenDict()\n        this.getDeptsData()\n        this.handleQuery()\n      }\n    },\n    handleApplicationTagShow(applicationList) {\n      if (!applicationList || applicationList.length < 1) {\n        return '';\n      }\n      let result = applicationList[0].assetName;\n      if (applicationList.length > 1) {\n        result += '...';\n      }\n      return result;\n    },\n\n    handleBlocking() {\n      if (this.multipleSelection.length < 1) return this.$message.warning('请选择要阻断的ip');\n      this.blockingDialogVisible = true;\n      let arr = this.multipleSelection.map(item => item.srcIp);\n      arr = Array.from(new Set(arr));\n      this.$set(this.blockingForm,'block_ip',arr.join(';'));\n    },\n    blockingSubmit() {\n      this.$refs[\"blockingForm\"].validate(valid => {\n        if (valid) {\n          addBlockIp(this.blockingForm).then(res => {\n            this.$message.success('添加成功');\n          }).finally(() => {\n            this.blockingDialogVisible = false;\n            this.$refs.multipleTable.clearSelection();\n            this.multipleSelection = [];\n          })\n        }\n      })\n    },\n  }\n}\n</script>\n\n<style scoped>\n.el-divider {\n  background: #0E94EA;\n}\n\n.el-divider--vertical {\n  display: inline-block;\n  width: 5px;\n  height: 2em;\n  margin: 0 8px 0 0;\n  vertical-align: middle;\n  position: relative;\n}\n\n.my-title {\n  display: inline-block;\n  vertical-align: center;\n}\n\n\n.asset-tag {\n  margin-left: 5px;\n}\n</style>\n<style lang=\"scss\" scoped>\n.asset-tag {\n  max-width: 100%;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  vertical-align: middle;\n}\n\n.el-tooltip__popper {\n  font-size: 12px;\n  max-width: 300px;\n}\n\n.overflow-tag:not(:first-child) {\n  margin-top: 5px;\n}\n.blocking-form {\n  ::v-deep .el-form-item__label {\n    float: none;\n  }\n}\n</style>\n"]}]}