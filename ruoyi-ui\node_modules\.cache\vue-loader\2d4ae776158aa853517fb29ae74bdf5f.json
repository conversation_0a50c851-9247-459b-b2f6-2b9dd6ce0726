{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorWeb.vue?vue&type=template&id=526ab65b&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\monitorWeb.vue", "mtime": 1755768894571}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751956544249}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}