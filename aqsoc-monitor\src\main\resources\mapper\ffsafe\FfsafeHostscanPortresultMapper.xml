<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeHostscanPortResultMapper">

    <resultMap type="FfsafeHostscanPortResult" id="FfsafeHostscanPortresultResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="hostIp"    column="host_ip"    />
        <result property="hostPort"    column="host_port"    />
        <result property="procotol"    column="procotol"    />
        <result property="serviceName"    column="service_name"    />
        <result property="product"    column="product"    />
        <result property="summaryId"    column="summary_id"    />
    </resultMap>

    <sql id="selectFfsafeHostscanPortresultVo">
        select id, task_id, host_ip, host_port, procotol, service_name, product, summary_id from ffsafe_hostscan_portresult
    </sql>

    <select id="selectFfsafeHostscanPortresultList" parameterType="FfsafeHostscanPortResult" resultMap="FfsafeHostscanPortresultResult">
        <include refid="selectFfsafeHostscanPortresultVo"/>
        <where>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="hostIp != null  and hostIp != ''"> and host_ip = #{hostIp}</if>
            <if test="hostPort != null "> and host_port = #{hostPort}</if>
            <if test="procotol != null  and procotol != ''"> and procotol = #{procotol}</if>
            <if test="serviceName != null  and serviceName != ''"> and service_name like concat('%', #{serviceName}, '%')</if>
            <if test="product != null  and product != ''"> and product = #{product}</if>
        </where>
    </select>

    <select id="selectFfsafeHostscanPortresultById" parameterType="Long" resultMap="FfsafeHostscanPortresultResult">
        <include refid="selectFfsafeHostscanPortresultVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeHostscanPortresultByIds" parameterType="Long" resultMap="FfsafeHostscanPortresultResult">
        <include refid="selectFfsafeHostscanPortresultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeHostscanPortresult" parameterType="FfsafeHostscanPortResult" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_hostscan_portresult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="hostIp != null">host_ip,</if>
            <if test="hostPort != null">host_port,</if>
            <if test="procotol != null">procotol,</if>
            <if test="serviceName != null">service_name,</if>
            <if test="product != null">product,</if>
            <if test="summaryId != null">summary_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="hostIp != null">#{hostIp},</if>
            <if test="hostPort != null">#{hostPort},</if>
            <if test="procotol != null">#{procotol},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="product != null">#{product},</if>
            <if test="summaryId != null">#{summaryId},</if>
        </trim>
    </insert>

    <update id="updateFfsafeHostscanPortresult" parameterType="FfsafeHostscanPortResult">
        update ffsafe_hostscan_portresult
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="hostIp != null">host_ip = #{hostIp},</if>
            <if test="hostPort != null">host_port = #{hostPort},</if>
            <if test="procotol != null">procotol = #{procotol},</if>
            <if test="serviceName != null">service_name = #{serviceName},</if>
            <if test="product != null">product = #{product},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeHostscanPortresultById" parameterType="Long">
        delete from ffsafe_hostscan_portresult where id = #{id}
    </delete>

    <delete id="deleteFfsafeHostscanPortresultByIds" parameterType="String">
        delete from ffsafe_hostscan_portresult where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>