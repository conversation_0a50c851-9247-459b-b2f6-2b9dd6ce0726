package com.ruoyi.monitor2.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.monitor2.domain.MonitorBssVulnResult;
import com.ruoyi.monitor2.domain.MonitorBssVulnResultExport;
import com.ruoyi.monitor2.mapper.MonitorBssVulnResultMapper;
import com.ruoyi.monitor2.service.IMonitorBssVulnResultService;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class MonitorBssVulnResultServiceImpl implements IMonitorBssVulnResultService {
    @Resource
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private MonitorBssVulnResultMapper monitorBssVulnResultMapper;

    /**
     * 查询基础服务扫描漏洞结果
     *
     * @param id 基础服务扫描漏洞结果主键
     * @return 基础服务扫描漏洞结果
     */
    @Override
    public MonitorBssVulnResult selectMonitorBssVulnResultById(Long id)
    {
        return monitorBssVulnResultMapper.selectMonitorBssVulnResultById(id);
    }

    /**
     * 批量查询基础服务扫描漏洞结果
     *
     * @param ids 基础服务扫描漏洞结果主键集合
     * @return 基础服务扫描漏洞结果集合
     */
    @Override
    public List<MonitorBssVulnResult> selectMonitorBssVulnResultByIds(Long[] ids)
    {
        return monitorBssVulnResultMapper.selectMonitorBssVulnResultByIds(ids);
    }

    /**
     * 查询基础服务扫描漏洞结果列表
     *
     * @param monitorBssVulnResult 基础服务扫描漏洞结果
     * @return 基础服务扫描漏洞结果
     */
    @Override
    public List<MonitorBssVulnResult> selectMonitorBssVulnResultList(MonitorBssVulnResult monitorBssVulnResult)
    {
        return monitorBssVulnResultMapper.selectMonitorBssVulnResultList(monitorBssVulnResult);
    }

    /**
     * 新增基础服务扫描漏洞结果
     *
     * @param monitorBssVulnResult 基础服务扫描漏洞结果
     * @return 结果
     */
    @Override
    public int insertMonitorBssVulnResult(MonitorBssVulnResult monitorBssVulnResult)
    {
        monitorBssVulnResult.setCreateTime(DateUtils.getNowDate());
        return monitorBssVulnResultMapper.insertMonitorBssVulnResult(monitorBssVulnResult);
    }


    /**
     * 新增基础服务扫描漏洞结果列表
     *
     * @param monitorBssVulnResultList 基础服务扫描漏洞结果列表
     * @return 结果
     */
    public int insertMonitorBssVulnResultList(List<MonitorBssVulnResult> monitorBssVulnResultList)
    {
        int ret = 0;
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
        try {
            MonitorBssVulnResultMapper bssVulnResultMapper = sqlSession.getMapper(MonitorBssVulnResultMapper.class);
            AtomicInteger count = new AtomicInteger(0);
            monitorBssVulnResultList.forEach(bssVulnResult -> {
                bssVulnResultMapper.insertMonitorBssVulnResult(bssVulnResult);
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            });
            // 提交数据
            sqlSession.commit();
            //sqlSession.rollback();
            ret = monitorBssVulnResultList.size();
        } catch (Exception e) {
            sqlSession.rollback();
            throw e;
        } finally {
            sqlSession.close();
        }

        return ret;
    }


    /**
     * 修改基础服务扫描漏洞结果
     *
     * @param monitorBssVulnResult 基础服务扫描漏洞结果
     * @return 结果
     */
    @Override
    public int updateMonitorBssVulnResult(MonitorBssVulnResult monitorBssVulnResult)
    {
        monitorBssVulnResult.setUpdateTime(DateUtils.getNowDate());
        return monitorBssVulnResultMapper.updateMonitorBssVulnResult(monitorBssVulnResult);
    }

    /**
     * 删除基础服务扫描漏洞结果信息
     *
     * @param id 基础服务扫描漏洞结果主键
     * @return 结果
     */
    @Override
    public int deleteMonitorBssVulnResultById(Long id)
    {
        return monitorBssVulnResultMapper.deleteMonitorBssVulnResultById(id);
    }

    /**
     * 批量删除基础服务扫描漏洞结果
     *
     * @param ids 需要删除的基础服务扫描漏洞结果主键
     * @return 结果
     */
    @Override
    public int deleteMonitorBssVulnResultByIds(Long[] ids)
    {
        return monitorBssVulnResultMapper.deleteMonitorBssVulnResultByIds(ids);
    }

    @Override
    public List<MonitorBssVulnResult> findImpactAssetList(MonitorBssVulnResult monitorBssVulnResult) {
        return monitorBssVulnResultMapper.findImpactAssetList(monitorBssVulnResult);
    }

    @Override
    public List<MonitorBssVulnResult> findAnalyseType(MonitorBssVulnResult monitorBssVulnResult) {
        return monitorBssVulnResultMapper.findAnalyseType(monitorBssVulnResult);
    }

    @Override
    public List<MonitorBssVulnResult> selectGapTypeAnalyse(MonitorBssVulnResult monitorBssVulnResult) {
        return monitorBssVulnResultMapper.selectGapTypeAnalyse(monitorBssVulnResult);
    }

    @Override
    public List<MonitorBssVulnResult> selectGapNumStatistics(MonitorBssVulnResult monitorBssVulnResult) {
        return monitorBssVulnResultMapper.selectGapNumStatistics(monitorBssVulnResult);
    }

    @Override
    public List<MonitorBssVulnResult> selectJobGapList(MonitorBssVulnResult monitorBssVulnResult) {
        return monitorBssVulnResultMapper.selectJobGapList(monitorBssVulnResult);
    }

    @Override
    public List<MonitorBssVulnResult> selectTargetNumList(MonitorBssVulnResult monitorBssVulnResult) {
        return monitorBssVulnResultMapper.selectTargetNumList(monitorBssVulnResult);
    }
    /**
     * 查询漏洞情报列表
     * @param monitorBssVulnResult
     * @return
     */
    @Override
    public List<MonitorBssVulnResult> selectGapIntelList(MonitorBssVulnResult monitorBssVulnResult) {
        return monitorBssVulnResultMapper.selectGapIntelList(monitorBssVulnResult);
    }

    @Override
    public List<MonitorBssVulnResult> findWebGapAsset(MonitorBssVulnResult monitorBssVulnResult) {
        return monitorBssVulnResultMapper.findWebGapAsset(monitorBssVulnResult);
    }
}
