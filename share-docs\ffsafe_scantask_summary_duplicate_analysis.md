# ffsafe_scantask_summary 表重复数据问题深度分析报告

## 📋 概述

本报告深入分析了 aqsoc-main 项目中 `ffsafe_scantask_summary` 表出现重复数据的根本原因，特别针对定时任务场景下的重复记录产生机制进行了详细调研。

## 🚨 问题现状

### 数据重复情况确认
```sql
-- 重复数据统计
SELECT task_id, task_type, COUNT(*) as count 
FROM ffsafe_scantask_summary 
GROUP BY task_id, task_type 
HAVING COUNT(*) > 1 
ORDER BY count DESC;

-- 结果显示：
-- task_id=641, task_type=2 有4条完全相同的记录
-- task_id=0, task_type=2 有4条重复记录  
-- 多个其他任务也存在2-3条重复记录
```

### 表结构缺陷
- **缺少唯一性约束**：表中只有主键 `id` 的索引
- **无防重复机制**：数据库层面无法阻止相同 `task_id + task_type` 的重复插入

## 🔍 重复数据产生的根本原因分析

### 1. **定时任务重复执行场景**

#### 1.1 每日定时任务的重复风险

**场景描述**：
- 定时任务设置为每天执行（period=1, cron="0 0 0 * * ?"）
- 每次执行都会创建新的扫描任务，获得新的 `task_id`
- **正常情况下不会产生重复**，因为每次都是新的 `task_id`

**但是存在以下异常情况**：

```java
// 位置：HostVulnScan.java:79
if (scantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary) > 0) {
    // 插入成功后的后续操作
    hostScanResultMonitorEvent.addHostScanTask(summaryParams);
    sysJob.setCurrentStatus(SysJob.PROCESS_RUNNING);
}
```

**问题**：如果插入成功但后续操作失败，可能导致：
1. 任务状态未正确更新
2. 下次定时执行时重复创建相同任务

#### 1.2 立即执行任务的重复风险

**场景描述**：
- 立即执行任务（period=0, cron="* * * * * ?"）
- 理论上执行一次后自动停止

**实际风险**：
```java
// 位置：AbstractQuartzJob.java:117
// 判断任务是否为立即执行
if (Objects.equals(sysJob.getPeriod(), 0) && sysJob.getCronExpression().equals("* * * * * ?")) {
    sysJob.setStatus(ScheduleConstants.Status.PAUSE.getValue());
    sysJob.setCurrentStatus(2);
    // 如果这里更新失败，任务可能继续执行
    SpringUtils.getBean(ISysJobService.class).changeStatus(sysJob);
}
```

### 2. **并发执行导致的重复插入**

#### 2.1 定时任务并发调度

**日志证据**：
```
Caused by: java.lang.Exception: 当前任务正在扫描中, 请忽重复调度.
at com.ruoyi.ffsafe.scantaskapi.event.HostVulnScan.scan(HostVulnScan.java:61)
```

**问题分析**：
1. **检查逻辑不够严格**：
```java
// 位置：HostVulnScan.java:64
if (sysJob.getCurrentStatus() == SysJob.PROCESS_RUNNING) {
    throw new Exception("当前任务正在扫描中, 请忽重复调度.");
}
```

2. **时间窗口问题**：
   - 检查状态 → 创建扫描任务 → 插入数据库 → 更新状态
   - 在这个时间窗口内，另一个线程可能通过状态检查

#### 2.2 系统重启恢复机制

**位置**：`ScanResultMonitorEvent.initHostScanTaskList()`

```java
// 系统启动时恢复未完成的任务
List<FfsafeScantaskSummary> ffsafeScantaskSummaryList = 
    scantaskSummaryService.selectFfsafeScantaskSummaryList(scantaskSummary);
for (FfsafeScantaskSummary summary : ffsafeScantaskSummaryList) {
    int taskStatus = summary.getTaskStatus().intValue();
    if ((taskStatus == SCHEDULING) || (taskStatus == RUNNING)) {
        // 重新添加到监控列表
        addHostScanTask(params);
    }
}
```

**风险**：如果系统异常重启，可能重复恢复已存在的任务记录

### 3. **事务边界问题**

#### 3.1 插入与后续操作不在同一事务

```java
// 插入操作
scantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary);

// 后续操作（不在同一事务中）
hostScanResultMonitorEvent.addHostScanTask(summaryParams);
sysJob.setCurrentStatus(SysJob.PROCESS_RUNNING);
```

**问题**：
- 插入成功但后续操作失败
- 任务状态不一致
- 重试时可能重复插入

#### 3.2 异常处理机制缺陷

```java
try {
    createHostScanTaskResult = scanTaskService.createHostScanTask(curHostScanTaskParam);
    if (createHostScanTaskResult != null) {
        // 插入数据库
        scantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary);
    }
} catch (Exception e) {
    // 异常处理，但可能导致重试
    log.warn("创建主机扫描任务失败: " + e.getMessage());
    throw e;
}
```

## 🎯 定时任务重复执行的具体场景

### 场景1：每日定时任务异常重试

**触发条件**：
1. 每日定时任务正常启动（cron="0 0 0 * * ?"）
2. 扫描任务创建成功，获得 task_id=X
3. 插入 ffsafe_scantask_summary 成功
4. 后续操作失败（网络异常、服务异常等）
5. 任务状态未正确更新为 RUNNING
6. 系统重试或下次调度时，使用相同的 task_id 再次插入

### 场景2：立即执行任务状态更新失败

**触发条件**：
1. 立即执行任务启动（period=0）
2. 扫描任务创建并插入数据库
3. 任务状态更新失败（AbstractQuartzJob.java:121）
4. 任务继续按 cron="* * * * * ?" 每秒执行
5. 每次执行都尝试插入相同的记录

### 场景3：并发调度竞态条件

**触发条件**：
1. 定时任务在预定时间启动
2. 同时有手动触发或其他调度
3. 两个线程同时通过状态检查
4. 都创建了扫描任务（可能获得相同或不同的 task_id）
5. 都尝试插入数据库记录

## 📊 数据库查询验证

### 查询重复记录的详细信息
```sql
-- 查看具体的重复记录
SELECT * FROM ffsafe_scantask_summary 
WHERE task_id = 641 AND task_type = 2 
ORDER BY id;

-- 结果：4条记录完全相同，只有 id 不同
-- 说明是多次插入了相同的数据
```

### 查询定时任务配置
```sql
-- 查看相关的定时任务配置
SELECT job_id, job_name, cron_expression, period, status, current_status, job_type 
FROM sys_job 
WHERE job_type IN (1, 2);

-- 发现大量任务使用 period=0（立即执行）
-- 这些任务的 current_status=2（已完成），但 status=1（启用状态）
```

## 🔧 问题解决方案建议

### 1. 数据库层面解决方案

```sql
-- 添加唯一性约束
ALTER TABLE ffsafe_scantask_summary 
ADD UNIQUE KEY uk_task_id_type (task_id, task_type);
```

### 2. 代码层面解决方案

#### 2.1 插入前检查机制
```java
// 在插入前检查是否已存在
FfsafeScantaskSummary existing = scantaskSummaryService
    .selectByTaskIdAndType(taskId, taskType);
if (existing == null) {
    scantaskSummaryService.insertFfsafeScantaskSummary(ffsafeScantaskSummary);
} else {
    // 更新现有记录
    scantaskSummaryService.updateFfsafeScantaskSummaryByTaskId(ffsafeScantaskSummary);
}
```

#### 2.2 事务边界优化
```java
@Transactional(rollbackFor = Exception.class)
public void createScanTaskWithSummary(ScanTaskParam param) {
    // 创建扫描任务
    CreateHostScanTaskResult result = scanTaskService.createHostScanTask(param);
    
    // 插入汇总记录
    scantaskSummaryService.insertFfsafeScantaskSummary(summary);
    
    // 更新任务状态
    sysJob.setCurrentStatus(SysJob.PROCESS_RUNNING);
    sysJobService.updateJob(sysJob);
    
    // 添加监控
    hostScanResultMonitorEvent.addHostScanTask(params);
}
```

## 📝 总结

**重复数据产生的主要原因**：
1. **数据库缺少唯一性约束** - 根本原因
2. **并发调度竞态条件** - 直接原因  
3. **事务边界不当** - 技术原因
4. **异常处理机制缺陷** - 触发原因

**定时任务场景下的特殊风险**：
- 每日定时任务理论上不会重复（task_id不同）
- 但异常情况下可能导致状态不一致，触发重试
- 立即执行任务如果状态更新失败，会持续重复执行

**建议优先级**：
1. **高优先级**：添加数据库唯一性约束
2. **中优先级**：优化事务边界和异常处理
3. **低优先级**：增强并发控制机制
