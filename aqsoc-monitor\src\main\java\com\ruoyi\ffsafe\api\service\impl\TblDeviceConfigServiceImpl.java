package com.ruoyi.ffsafe.api.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.ffsafe.api.domain.FfsafeApiConfig;
import com.ruoyi.ffsafe.api.domain.TblDeviceConfig;
import com.ruoyi.ffsafe.api.mapper.TblDeviceConfigMapper;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.monitor2.domain.NmapConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 设备接入配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Service
@Slf4j
public class TblDeviceConfigServiceImpl implements ITblDeviceConfigService
{
    @Autowired
    private TblDeviceConfigMapper tblDeviceConfigMapper;
    @Resource
    private INetworkDomainService networkDomainService;

    /**
     * 查询设备接入配置
     *
     * @param id 设备接入配置主键
     * @return 设备接入配置
     */
    @Override
    public TblDeviceConfig selectTblDeviceConfigById(Long id)
    {
        return tblDeviceConfigMapper.selectTblDeviceConfigById(id);
    }

    /**
     * 批量查询设备接入配置
     *
     * @param ids 设备接入配置主键集合
     * @return 设备接入配置集合
     */
    @Override
    public List<TblDeviceConfig> selectTblDeviceConfigByIds(Long[] ids)
    {
        return tblDeviceConfigMapper.selectTblDeviceConfigByIds(ids);
    }

    /**
     * 查询设备接入配置列表
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 设备接入配置
     */
    @Override
    public List<TblDeviceConfig> selectTblDeviceConfigList(TblDeviceConfig tblDeviceConfig)
    {
        return tblDeviceConfigMapper.selectTblDeviceConfigList(tblDeviceConfig);
    }

    /**
     * 新增设备接入配置
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 结果
     */
    @Override
    public int insertTblDeviceConfig(TblDeviceConfig tblDeviceConfig)
    {
        tblDeviceConfig.setCreateTime(DateUtils.getNowDate());
        tblDeviceConfig.setUpdateTime(tblDeviceConfig.getCreateTime());
        return tblDeviceConfigMapper.insertTblDeviceConfig(tblDeviceConfig);
    }

    /**
     * 修改设备接入配置
     *
     * @param tblDeviceConfig 设备接入配置
     * @return 结果
     */
    @Override
    public int updateTblDeviceConfig(TblDeviceConfig tblDeviceConfig)
    {
        tblDeviceConfig.setUpdateTime(DateUtils.getNowDate());
        return tblDeviceConfigMapper.updateTblDeviceConfig(tblDeviceConfig);
    }

    /**
     * 删除设备接入配置信息
     *
     * @param id 设备接入配置主键
     * @return 结果
     */
    @Override
    public int deleteTblDeviceConfigById(Long id)
    {
        return tblDeviceConfigMapper.deleteTblDeviceConfigById(id);
    }

    /**
     * 批量删除设备接入配置
     *
     * @param ids 需要删除的设备接入配置主键
     * @return 结果
     */
    @Override
    public int deleteTblDeviceConfigByIds(Long[] ids)
    {
        return tblDeviceConfigMapper.deleteTblDeviceConfigByIds(ids);
    }

    @Override
    public List<JSONObject> selectTargetDeviceConfig(String targets) {
        List<JSONObject> result = new ArrayList<>();
        try {
            List<TblDeviceConfig> list = selectTblDeviceConfigList(new TblDeviceConfig());
            if(CollUtil.isEmpty(list)){
                return result;
            }
            List<NetworkDomain> networkDomainList = networkDomainService.selectNetworkDomainList(new NetworkDomain());
            if(CollUtil.isEmpty(networkDomainList)){
                return result;
            }
            List<String> targetList = StrUtil.split(targets, ';');
            for (String target : targetList) {
                TblDeviceConfig curConfig = null;
                NetworkDomain matchDomain = networkDomainList.stream().filter(domain -> {
                    String iparea = domain.getIparea();
                    if (StrUtil.isBlank(iparea)) {
                        return false;
                    }
    //                return Ipv4Util.list(iparea, true).contains(target);
                    return IpUtils.isInRange(target,iparea);
                }).findFirst().orElse(null); //暂时只取一个
                if(matchDomain != null){
                    curConfig = list.stream().filter(config -> config.getId().equals(matchDomain.getDeviceConfigId())).findFirst().orElse(null);
                }
                JSONObject resultItem = new JSONObject();
                resultItem.put("target", target);
                resultItem.put("config", curConfig);
                result.add(resultItem);
            }
        } catch (Exception e) {
            log.error("查询设备配置失败: " + targets + "------" + e.getMessage());
        }

        return result;
    }

    @Override
    public TblDeviceConfig selectDeviceConfigOrDefault(Long deviceId) {
        TblDeviceConfig result = null;
        if(deviceId == null){
            result = tblDeviceConfigMapper.selectDefaultDeviceConfig();
        }else {
            result = selectTblDeviceConfigById(deviceId);
        }
        return result;
    }

    @Override
    public void updateLastTime(TblDeviceConfig update) {
        tblDeviceConfigMapper.updateTblDeviceConfig(update);
    }

    @Override
    public FfsafeApiConfig getFfsafeApiConfig(TblDeviceConfig deviceConfig) {
        JSONObject ffSafeParams = getDeviceConfigItem(deviceConfig, "ffsafe.api");
        if(ffSafeParams == null){
            throw new RuntimeException("找不到对应的参数配置");
        }
        JSONObject ffSafeParamsValue = ffSafeParams.getJSONObject("value");
        String ffUrl = ffSafeParamsValue.getString("url");
        Boolean enable = ffSafeParamsValue.getBoolean("enable");
        String ffToken = ffSafeParamsValue.getString("token");
        FfsafeApiConfig ffsafeApiConfig = new FfsafeApiConfig();
        ffsafeApiConfig.setEnable(enable);
        ffsafeApiConfig.setUrl(ffUrl);
        ffsafeApiConfig.setToken(ffToken);
        return ffsafeApiConfig;
    }

    @Override
    public NmapConfig getNmapConfig(TblDeviceConfig deviceConfig) {
        JSONObject nmapData = getDeviceConfigItem(deviceConfig, "nmap");
        if(nmapData == null){
            return null;
        }
        JSONObject nmapParams = nmapData.getJSONObject("value");
        NmapConfig nmapConfig = new NmapConfig();
        nmapConfig.setType(nmapParams.getInteger("type"));
        nmapConfig.setIp(nmapParams.getString("ip"));
        if(StrUtil.isBlank(nmapConfig.getIp())){
            nmapConfig.setIp(deviceConfig.getDeviceIp());
        }
        nmapConfig.setPort(nmapParams.getInteger("port"));
        if(StrUtil.isNotBlank(nmapParams.getString("url"))){
            nmapConfig.setUrl(nmapParams.getString("url"));
        }
        return nmapConfig;
    }

    private JSONObject getDeviceConfigItem(TblDeviceConfig deviceConfig,String key){
        String deviceParamsStr = deviceConfig.getDeviceParams();
        List<JSONObject> deviceParamsList = JSONUtil.toList(deviceParamsStr, JSONObject.class);
        return deviceParamsList.stream().filter(item -> item.getString("key").equals(key)).findFirst().orElse(null);
    }
}
