{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\leakyRecord.vue", "mtime": 1755768894571}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7YmF0Y2hHZW5lcmF0ZVJlcG9ydCwgZ2V0TGlzdFdpdGhEZXRhaWxzLCBnZXRSZXBvcnRMaXN0fSBmcm9tICJAL2FwaS9zYWZlL21vbml0b3IiOwppbXBvcnQgUXVlc3RSZXN1bHREZXRhaWxzIGZyb20gJy4uLy4uL3NhZmUvc2VydmVyL3F1ZXN0UmVzdWx0RGV0YWlscycKaW1wb3J0IExlYWtTY2FuRGlhbG9nIGZyb20gJy4uLy4uL3NhZmUvc2VydmVyL2NvbXBvbmVudHMvTGVha1NjYW5EaWFsb2cnCmltcG9ydCBGZkpvYlRhc2tzIGZyb20gJy4vZmZKb2JUYXNrcycKaW1wb3J0IEpvYkxvZyBmcm9tICcuLi8uLi9tb25pdG9yL2pvYi9sb2cnCmltcG9ydCB7dGFza0NyZWF0ZXJlcG9ydCwgdGFza0Rvd25SZXBvcnR9IGZyb20gIkAvYXBpL21vbml0b3IyL3dwcmVzdWx0IjsKaW1wb3J0IEZmSm9iVGFza3NEZXRhaWwgZnJvbSAiQC92aWV3cy9mcmFpbHR5L21vbml0b3IvZmZKb2JUYXNrc0RldGFpbC52dWUiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJob3N0TGVha3lSZWNvcmQiLAogIGNvbXBvbmVudHM6IHtGZkpvYlRhc2tzRGV0YWlsLCBKb2JMb2csIEZmSm9iVGFza3MsIExlYWtTY2FuRGlhbG9nLCBRdWVzdFJlc3VsdERldGFpbHMgfSwKICBkaWN0czogWydzeXNfam9iX2dyb3VwJywgJ3N5c19qb2Jfc3RhdHVzJ10sCiAgcHJvcHM6IHsKICAgIHRvUGFyYW1zOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogKCkgPT4ge30KICAgIH0sCiAgICBsaXN0VHlwZTogewogICAgICB0eXBlOiBOdW1iZXIsCiAgICAgIGRlZmF1bHQ6IDQKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBqb2JUeXBlOiB1bmRlZmluZWQsCiAgICAgIC8vIOaYr+WQpuaYvuekukNyb27ooajovr7lvI/lvLnlh7rlsYIKICAgICAgb3BlbkNyb246IGZhbHNlLAogICAgICAvLyDlsZXnpLrmnIDov5HkuIDmrKHov5DooYznu5PmnpwKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOS7u+WKoUlECiAgICAgIGpvYklkOiAnJywKICAgICAgdG90YWxTY2FuOiAwLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlrprml7bku7vliqHooajmoLzmlbDmja4KICAgICAgam9iTGlzdDogW10sCgogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuaYvuekuuivpue7huW8ueWHuuWxggogICAgICBvcGVuVmlldzogZmFsc2UsCiAgICAgIGRldGFpbERpYWxvZzogZmFsc2UsCiAgICAgIHRhc2tSb3c6IFtdLAogICAgICBlZGl0Rm9ybToge30sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICB9LAogICAgICBpc0Rpc2FibGVkOiBmYWxzZSwKICAgICAgLy8g5ZGo5pyf6L2s5o2i5paH5a2XCiAgICAgIGNyb25UZXh0OiAnJywKICAgICAgcm93czogW10sCiAgICAgIGdldExpc3RJbnRlcnZhbDogbnVsbCwKICAgICAgLy8g5oql5ZGK55Sf5oiQ6K6w5b2V55u45YWz5pWw5o2uCiAgICAgIHJlcG9ydFJlY29yZERpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICByZXBvcnRMb2FkaW5nOiBmYWxzZSwKICAgICAgcmVwb3J0TGlzdDogW10sCiAgICAgIHJlcG9ydFRvdGFsOiAwLAogICAgICByZXBvcnRRdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHRhc2tUeXBlOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgc2VsZWN0ZWRJZHM6IFtdLAogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICB0b1BhcmFtczogewogICAgICBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmKG5ld1ZhbCAmJiBuZXdWYWwuaWQpewogICAgICAgICAgdGhpcy5oYW5kbGVKb2JMb2coewogICAgICAgICAgICBqb2JJZDogbmV3VmFsLmlkCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB0aGlzLmdldExpc3RJbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgdGhpcy5sb29wR2V0TGlzdCgpCiAgICB9LCAxMDAwMCkKICB9LAogIGRlc3Ryb3llZCgpIHsKICAgIGlmKHRoaXMuZ2V0TGlzdEludGVydmFsKXsKICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmdldExpc3RJbnRlcnZhbCk7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i5Li75py65ryP5omr6K6w5b2V5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnRhc2tUeXBlID0gdGhpcy5saXN0VHlwZSA9PT0gNCA/IDIgOiAxOwogICAgICBnZXRMaXN0V2l0aERldGFpbHModGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5qb2JMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmn6Xor6Llrprml7bku7vliqHliJfooaggKi8KICAgIGxvb3BHZXRMaXN0KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnRhc2tUeXBlID0gdGhpcy5saXN0VHlwZSA9PT0gNCA/IDIgOiAxOwogICAgICBnZXRMaXN0V2l0aERldGFpbHModGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgY29uc3QgbmV3Sm9iTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgY29uc3Qgc2VsZWN0ZWRJZHMgPSBbLi4udGhpcy5zZWxlY3RlZElkc107IC8vIOS/neWtmOW9k+WJjemAieS4reeahElECgogICAgICAgIHRoaXMuam9iTGlzdCA9IG5ld0pvYkxpc3Q7CiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwoKICAgICAgICAvLyDlnKhET03mm7TmlrDlkI7mgaLlpI3pgInkuK3nirbmgIEKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAvLyDmn6Xmib7pnIDopoHph43mlrDpgInkuK3nmoTooYwKICAgICAgICAgIGNvbnN0IHJvd3NUb1NlbGVjdCA9IHRoaXMuam9iTGlzdC5maWx0ZXIocm93ID0+CiAgICAgICAgICAgIHNlbGVjdGVkSWRzLmluY2x1ZGVzKHJvdy5pZCkKICAgICAgICAgICk7CgogICAgICAgICAgLy8g6YeN5paw6YCJ5Lit5LmL5YmN6YCJ5Lit55qE6aG5CiAgICAgICAgICB0aGlzLiRyZWZzLm11bHRpcGxlVGFibGUuY2xlYXJTZWxlY3Rpb24oKTsKICAgICAgICAgIHJvd3NUb1NlbGVjdC5mb3JFYWNoKHJvdyA9PiB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMubXVsdGlwbGVUYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93LCB0cnVlKTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKCiAgICBnZXRSb3dLZXkocm93KSB7CiAgICAgIHJldHVybiByb3cuaWQ7ICAvLyDkvb/nlKggam9iSWQg5L2c5Li66KGM55qE5ZSv5LiA5qCH6K+GCiAgICB9LAoKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgICAgdGhpcy5yb3dzID0gc2VsZWN0aW9uOwogICAgICB0aGlzLnNlbGVjdGVkSWRzID0gWy4uLnRoaXMuaWRzXTsgLy8g5L+u5aSN77ya5Y6f5p2l5pivIHRoaXMuaWQKICAgIH0sCgogICAgLyoqIOeUn+aIkOaKpeWRiiAqLwogICAgaGFuZGxlQ3JlYXRlUmVwb3J0KHJvdykgewogICAgICB0YXNrQ3JlYXRlcmVwb3J0KHJvdykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSkKICAgIH0sCgogICAgLyoqIOaJuemHj+eUn+aIkOaKpeWRiiAqLwogICAgYmF0Y2hDcmVhdGVSZXBvcnQocm93KSB7CiAgICAgIC8vIOaJuemHj+eUn+aIkOaKpeWRigogICAgICBpZiAodGhpcy5yb3dzLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuivt+WFiOmAieaLqeimgeeUn+aIkOaKpeWRiueahOiusOW9lSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjb25zdCBqb2JJZHMgPSB0aGlzLnJvd3MubWFwKGl0ZW0gPT4gaXRlbS5pZCk7CiAgICAgIGJhdGNoR2VuZXJhdGVSZXBvcnQoewogICAgICAgIGlkczogam9iSWRzLAogICAgICAgIHRhc2tUeXBlOiB0aGlzLmxpc3RUeXBlID09PSA0ID8gMiA6IDEKICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaJuemHj+aKpeWRiueUn+aIkOS7u+WKoeW3suaPkOS6pCIpOwogICAgICB9KQogICAgfSwKCiAgICAvKiog5oql5ZGK55Sf5oiQ6K6w5b2VICovCiAgICBoYW5kbGVSZXBvcnRSZWNvcmQoKSB7CiAgICAgIHRoaXMucmVwb3J0UmVjb3JkRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMucmVwb3J0UXVlcnlQYXJhbXMucmVwb3J0VHlwZSA9IHRoaXMubGlzdFR5cGUgPT09IDQgPyAyIDogMTsKICAgICAgdGhpcy5nZXRSZXBvcnRMaXN0KCk7CiAgICB9LAoKICAgIC8qKiDojrflj5bmiqXlkYrnlJ/miJDorrDlvZXliJfooaggKi8KICAgIGdldFJlcG9ydExpc3QoKSB7CiAgICAgIHRoaXMucmVwb3J0TG9hZGluZyA9IHRydWU7CiAgICAgIGdldFJlcG9ydExpc3QodGhpcy5yZXBvcnRRdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5yZXBvcnRMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnJlcG9ydFRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5yZXBvcnRMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLnJlcG9ydExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAoKICAgIGRvd25SZXBvcnQocm93KSB7CiAgICAgIHRhc2tEb3duUmVwb3J0KHJvdykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgewogICAgICAgICAgd2luZG93Lm9wZW4ocmVzcG9uc2UubXNnLCAiX2JsYW5rIikKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8qKiDku7vliqHor6bnu4bkv6Hmga8gKi8KICAgIGhhbmRsZVZpZXcocm93KSB7CiAgICAgIHRoaXMudGFza1JvdyA9IFtdCiAgICAgIHRoaXMudGFza1Jvdy5wdXNoKHJvdykKICAgICAgdGhpcy5lZGl0Rm9ybSA9IHsuLi5yb3d9CiAgICAgIHRoaXMuZGV0YWlsRGlhbG9nID0gdHJ1ZTsKICAgICAgLyp0aGlzLmpvYlR5cGUgPSAyOwogICAgICB0aGlzLmpvYklkID0gcm93LmpvYklkOyovCiAgICB9LAogIH0KfTsK"}, {"version": 3, "sources": ["leakyRecord.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmNA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "leakyRecord.vue", "sourceRoot": "src/views/frailty/monitor", "sourcesContent": ["<template>\n  <div class=\"custom-container\">\n    <div class=\"custom-content-container-right\">\n      <div class=\"custom-content-search-box\">\n        <el-form\n          :model=\"queryParams\"\n          ref=\"queryForm\"\n          size=\"small\"\n          :inline=\"true\"\n          label-position=\"right\"\n          label-width=\"70px\"\n        >\n          <el-row :gutter=\"10\">\n            <el-col :span=\"6\">\n              <el-form-item label=\"任务名称\" prop=\"jobName\">\n                <el-input\n                  v-model=\"queryParams.jobName\"\n                  placeholder=\"请输入任务名称\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"6\">\n              <el-form-item label=\"扫描目标\" prop=\"scanTarget\">\n                <el-input\n                  v-model=\"queryParams.scanTarget\"\n                  placeholder=\"扫描目标\"\n                  clearable\n                  @keyup.enter.native=\"handleQuery\"\n                />\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"6\">\n              <el-form-item label=\"任务状态\" prop=\"status\">\n                <el-select v-model=\"queryParams.status\" placeholder=\"请选择任务状态\" clearable>\n                  <el-option\n                    v-for=\"dict in dict.type.sys_job_status\"\n                    :key=\"dict.value\"\n                    :label=\"dict.label\"\n                    :value=\"dict.value\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item class=\"custom-search-btn\">\n                <el-button class=\"btn1\" size=\"small\" @click=\"handleQuery\">查询</el-button>\n                <el-button class=\"btn2\" size=\"small\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n      </div>\n\n      <div class=\"custom-content-container\">\n        <div class=\"common-header\">\n          <div><span class=\"common-head-title\">{{ listType === 4 ? '主机' : 'Web' }}漏扫记录列表</span></div>\n          <div class=\"common-head-right\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"1.5\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"handleReportRecord\"\n                >报告生成记录\n                </el-button>\n              </el-col>\n              <el-col :span=\"1.5\">\n                <el-button\n                  class=\"btn1\"\n                  size=\"small\"\n                  :disabled=\"multiple\"\n                  @click=\"batchCreateReport\"\n                >批量生成报告\n                </el-button>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        <el-table\n          height=\"100%\"\n          v-loading=\"loading\"\n          :data=\"jobList\"\n          ref=\"multipleTable\"\n          :row-key=\"getRowKey\"\n          @selection-change=\"handleSelectionChange\">\n          <el-table-column type=\"selection\" width=\"55\" />\n          <el-table-column label=\"任务名称\" align=\"left\" prop=\"jobName\"/>\n          <el-table-column label=\"扫描目标\" align=\"left\" prop=\"scanTarget\" width=\"150px\" :show-overflow-tooltip=\"false\"/>\n          <el-table-column label=\"扫描状态\" align=\"left\" prop=\"taskStatus\">\n            <template slot-scope=\"scope\">\n              <el-tag v-if=\"scope.row.taskStatus === 1\">正在调度</el-tag>\n              <el-tag type=\"primary\" v-else-if=\"scope.row.taskStatus === 2\">运行中</el-tag>\n              <el-tag type=\"danger\" v-else-if=\"scope.row.taskStatus === 3\">任务异常</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.taskStatus === 4\">扫描完成</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column\n            label=\"扫描进度\"\n            prop=\"finishRate\"\n            width=\"120\"\n            align=\"left\"\n          >\n            <template slot-scope=\"scope\">\n              <el-progress :text-inside=\"true\" :stroke-width=\"18\" :percentage=\"scope.row.finishRate\"></el-progress>\n            </template>\n          </el-table-column>\n          <el-table-column v-if=\"listType === 4\" label=\"存活主机\" align=\"left\" prop=\"hostNum\"/>\n          <el-table-column v-if=\"listType === 4\" label=\"弱口令\" align=\"left\" prop=\"pwNum\"/>\n          <el-table-column label=\"可入侵漏洞\" align=\"left\" prop=\"pocRiskNum\"/>\n          <el-table-column label=\"高危漏洞\" align=\"left\" prop=\"highRiskNum\"/>\n          <el-table-column label=\"中危漏洞\" align=\"left\" prop=\"lowRiskNum\"/>\n          <el-table-column label=\"低危漏洞\" align=\"left\" prop=\"lowRiskNum\"/>\n          <el-table-column label=\"开始时间\" align=\"left\" prop=\"startTime\"/>\n          <el-table-column label=\"结束时间\" align=\"left\" prop=\"endTime\"/>\n          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\" :show-overflow-tooltip=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                @click=\"handleView(scope.row)\"\n                v-hasPermi=\"['monitor:ipschedule:query']\"\n              >详情\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                v-if=\"listType === 5 && scope.row.taskStatus === 2 && scope.row.reportStatus === null\"\n                @click=\"handleCreateReport(scope.row)\"\n              >生成报告\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                v-if=\"listType === 4 && scope.row.taskStatus === 4 && scope.row.reportStatus === null\"\n                @click=\"handleCreateReport(scope.row)\"\n              >生成报告\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                v-if=\"scope.row.reportStatus !== null && scope.row.reportStatus !== 2\"\n              >报告生成中\n              </el-button>\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                v-if=\"scope.row.reportStatus === 2\"\n                @click=\"downReport(scope.row)\"\n              >下载报告\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :total=\"total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getList\"\n        />\n      </div>\n    </div>\n\n    <el-dialog title=\"报告生成记录\" :visible.sync=\"reportRecordDialogVisible\" width=\"80%\" append-to-body>\n      <div class=\"custom-content-container\">\n        <el-table height=\"100%\" v-loading=\"reportLoading\" :data=\"reportList\" ref=\"reportTable\">\n          <el-table-column label=\"扫描目标\" align=\"left\" prop=\"scanTarget\" />\n          <el-table-column label=\"创建时间\" align=\"left\" prop=\"createTime\" />\n          <el-table-column label=\"生成时间\" align=\"left\" prop=\"generateTime\" />\n          <el-table-column label=\"生成状态\" align=\"left\" prop=\"reportStatus\">\n            <template slot-scope=\"scope\">\n              <el-tag type=\"primary\" v-if=\"scope.row.reportStatus === 0\">正在生成</el-tag>\n              <el-tag type=\"primary\" v-else-if=\"scope.row.reportStatus === 1\">正在生成</el-tag>\n              <el-tag type=\"success\" v-else-if=\"scope.row.reportStatus === 2\">已完成</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n            <template slot-scope=\"scope\">\n              <el-button\n                size=\"mini\"\n                type=\"text\"\n                :disabled=\"scope.row.reportStatus !== 2\"\n                @click=\"downReport(scope.row)\"\n              >下载\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"reportTotal>0\"\n          :total=\"reportTotal\"\n          :page.sync=\"reportQueryParams.pageNum\"\n          :limit.sync=\"reportQueryParams.pageSize\"\n          @pagination=\"getReportList\"\n        />\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      title=\"任务详情\"\n      class=\"detail-dialog\"\n      :visible.sync=\"detailDialog\"\n      width=\"70%\" append-to-body>\n      <ff-job-tasks-detail v-if=\"detailDialog\" :form=\"taskRow\" :job-row=\"editForm\" />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {batchGenerateReport, getListWithDetails, getReportList} from \"@/api/safe/monitor\";\nimport QuestResultDetails from '../../safe/server/questResultDetails'\nimport LeakScanDialog from '../../safe/server/components/LeakScanDialog'\nimport FfJobTasks from './ffJobTasks'\nimport JobLog from '../../monitor/job/log'\nimport {taskCreatereport, taskDownReport} from \"@/api/monitor2/wpresult\";\nimport FfJobTasksDetail from \"@/views/frailty/monitor/ffJobTasksDetail.vue\";\n\nexport default {\n  name: \"hostLeakyRecord\",\n  components: {FfJobTasksDetail, JobLog, FfJobTasks, LeakScanDialog, QuestResultDetails },\n  dicts: ['sys_job_group', 'sys_job_status'],\n  props: {\n    toParams: {\n      type: Object,\n      default: () => {}\n    },\n    listType: {\n      type: Number,\n      default: 4\n    }\n  },\n  data() {\n    return {\n      jobType: undefined,\n      // 是否显示Cron表达式弹出层\n      openCron: false,\n      // 展示最近一次运行结果\n      // 遮罩层\n      loading: true,\n      // 任务ID\n      jobId: '',\n      totalScan: 0,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 定时任务表格数据\n      jobList: [],\n\n      // 是否显示弹出层\n      open: false,\n      // 是否显示详细弹出层\n      openView: false,\n      detailDialog: false,\n      taskRow: [],\n      editForm: {},\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      isDisabled: false,\n      // 周期转换文字\n      cronText: '',\n      rows: [],\n      getListInterval: null,\n      // 报告生成记录相关数据\n      reportRecordDialogVisible: false,\n      reportLoading: false,\n      reportList: [],\n      reportTotal: 0,\n      reportQueryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        taskType: undefined\n      },\n      selectedIds: [],\n    };\n  },\n  watch: {\n    toParams: {\n      handler(newVal) {\n        if(newVal && newVal.id){\n          this.handleJobLog({\n            jobId: newVal.id\n          });\n        }\n      },\n      immediate: true\n    }\n  },\n  created() {\n    this.getList()\n    this.getListInterval = setInterval(() => {\n      this.loopGetList()\n    }, 10000)\n  },\n  destroyed() {\n    if(this.getListInterval){\n      clearInterval(this.getListInterval);\n    }\n  },\n  methods: {\n    /** 查询主机漏扫记录列表 */\n    getList() {\n      this.loading = true;\n      this.queryParams.taskType = this.listType === 4 ? 2 : 1;\n      getListWithDetails(this.queryParams).then(response => {\n        this.jobList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 查询定时任务列表 */\n    loopGetList() {\n      this.queryParams.taskType = this.listType === 4 ? 2 : 1;\n      getListWithDetails(this.queryParams).then(response => {\n        const newJobList = response.rows;\n        const selectedIds = [...this.selectedIds]; // 保存当前选中的ID\n\n        this.jobList = newJobList;\n        this.total = response.total;\n\n        // 在DOM更新后恢复选中状态\n        this.$nextTick(() => {\n          // 查找需要重新选中的行\n          const rowsToSelect = this.jobList.filter(row =>\n            selectedIds.includes(row.id)\n          );\n\n          // 重新选中之前选中的项\n          this.$refs.multipleTable.clearSelection();\n          rowsToSelect.forEach(row => {\n            this.$refs.multipleTable.toggleRowSelection(row, true);\n          });\n        });\n      });\n    },\n\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n\n    getRowKey(row) {\n      return row.id;  // 使用 jobId 作为行的唯一标识\n    },\n\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.single = selection.length != 1;\n      this.multiple = !selection.length;\n      this.rows = selection;\n      this.selectedIds = [...this.ids]; // 修复：原来是 this.id\n    },\n\n    /** 生成报告 */\n    handleCreateReport(row) {\n      taskCreatereport(row).then(response => {\n        this.getList()\n      })\n    },\n\n    /** 批量生成报告 */\n    batchCreateReport(row) {\n      // 批量生成报告\n      if (this.rows.length === 0) {\n        this.$modal.msgWarning(\"请先选择要生成报告的记录\");\n        return;\n      }\n      const jobIds = this.rows.map(item => item.id);\n      batchGenerateReport({\n        ids: jobIds,\n        taskType: this.listType === 4 ? 2 : 1\n      }).then(res => {\n        this.$modal.msgSuccess(\"批量报告生成任务已提交\");\n      })\n    },\n\n    /** 报告生成记录 */\n    handleReportRecord() {\n      this.reportRecordDialogVisible = true;\n      this.reportQueryParams.reportType = this.listType === 4 ? 2 : 1;\n      this.getReportList();\n    },\n\n    /** 获取报告生成记录列表 */\n    getReportList() {\n      this.reportLoading = true;\n      getReportList(this.reportQueryParams).then(response => {\n        this.reportList = response.rows;\n        this.reportTotal = response.total;\n        this.reportLoading = false;\n      }).catch(() => {\n        this.reportLoading = false;\n      });\n    },\n\n    downReport(row) {\n      taskDownReport(row).then(response => {\n        if (response.code === 200) {\n          window.open(response.msg, \"_blank\")\n        }\n      })\n    },\n\n    /** 任务详细信息 */\n    handleView(row) {\n      this.taskRow = []\n      this.taskRow.push(row)\n      this.editForm = {...row}\n      this.detailDialog = true;\n      /*this.jobType = 2;\n      this.jobId = row.jobId;*/\n    },\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"@/assets/styles/assetIndex.scss\";\n.detail-dialog {\n  ::v-deep .el-dialog__body {\n    padding: 0 20px 30px;\n    height: 80vh;\n    overflow-y: auto;\n    overflow-x: hidden;\n  }\n}\n\n::v-deep .el-table {\n  display: flex;\n  flex-direction: column;\n}\n\n::v-deep .el-table__body-wrapper {\n  overflow-y: auto;\n  flex: 1;\n}\n</style>\n"]}]}