{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasksDetail.vue?vue&type=style&index=1&id=0a074b3a&lang=scss&scoped=true", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasksDetail.vue", "mtime": 1755743179263}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751956516551}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751956543892}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751956526179}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751956513748}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmFzc2V0LXRhZ3sKICBtYXJnaW4tbGVmdDogNXB4OwogIG1heC13aWR0aDogMTAwJTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsKfQoub3ZlcmZsb3ctdGFnOm5vdCg6Zmlyc3QtY2hpbGQpewogIG1hcmdpbi10b3A6IDVweDsKfQoubXktZGVzY3JpcHRpb25zIC5maXhlZC13aWR0aC1sYWJlbCB7CiAgd2lkdGg6IDE0MHB4Owp9Cg=="}, {"version": 3, "sources": ["ffJobTasksDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ffJobTasksDetail.vue", "sourceRoot": "src/views/frailty/monitor", "sourcesContent": ["<template>\n  <div>\n    <el-row class=\"s_d_row\">\n      <el-col>\n        <span class=\"s_span_tag\">漏洞结果综述</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"15\" class=\"row-item\">\n      <el-col :span=\"12\">\n        <div ref=\"holeMap\" id=\"holeMap\" style=\"height: 300px\"></div>\n      </el-col>\n      <el-col :span=\"12\">\n        <div ref=\"pieMap\" id=\"pieMap\" style=\"height: 300px\"></div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"form\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\" border v-if=\"jobRow.jobType === 1\">\n            <el-table-column label=\"任务名称\">\n              <template slot-scope=\"scope\">\n                <span> {{ jobRow.jobName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"存活主机\" >\n              <template slot-scope=\"scope\">\n                <span>{{ total }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"ipv4\" label=\"弱口令\" >\n              <template slot-scope=\"scope\">\n                <span>{{ wpTotal }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"pocRiskNum\" label=\"可入侵漏洞\" />\n            <el-table-column prop=\"highRiskNum\" label=\"高风险漏洞\" />\n            <el-table-column prop=\"middleRiskNum\" label=\"中风险漏洞\" />\n            <el-table-column prop=\"lowRiskNum\" label=\"低风险漏洞\" />\n          </el-table>\n          <el-table :data=\"form\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\" border v-if=\"jobRow.jobType === 2\">\n            <el-table-column label=\"任务名称\">\n              <template slot-scope=\"scope\">\n                <span> {{ jobRow.jobName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"网站\" >\n              <template slot-scope=\"scope\">\n                <div v-html=\"jobRow.ipOver\" slot=\"content\"></div>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"highRiskNum\" label=\"高风险\" />\n            <el-table-column prop=\"middleRiskNum\" label=\"中风险\" />\n            <el-table-column prop=\"lowRiskNum\" label=\"低风险\" />\n            <el-table-column prop=\"infoRiskNum\" label=\"信息风险\" />\n            <el-table-column prop=\"startTime\" label=\"开始时间\" />\n            <el-table-column prop=\"endTime\" label=\"结束时间\" />\n          </el-table>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\">\n      <el-col>\n        <span class=\"s_span_tag\">任务参数</span>\n      </el-col>\n    </el-row>\n    <el-row style=\"height: 40px;line-height: 40px;margin-top: 20px;border: solid 1px #e7e3e361;\">\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">任务类型</span>\n          <span v-if=\"jobRow.jobType === 1\">基础服务漏洞扫描</span>\n          <span v-if=\"jobRow.jobType === 2\">基础Web漏洞扫描</span>\n        </div>\n      </el-col>\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">任务目标</span>\n          <span v-html=\"jobRow.ipShow\" slot=\"content\" class=\"truncate\" :title=\"jobRow.ipShow\"></span>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row style=\"height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;\">\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">任务状态</span>\n          <el-tag type=\"danger\" v-if=\"form[0].taskType === 2 && form[0].taskStatus === 3\">任务异常</el-tag>\n          <el-tag v-else-if=\"form[0].taskType === 2 && form[0].taskStatus === 1\">扫描中</el-tag>\n          <el-tag v-else-if=\"form[0].taskType === 2 && form[0].taskStatus === 2\">扫描中</el-tag>\n          <el-tag type=\"success\" v-else-if=\"form[0].taskType === 2 && form[0].taskStatus === 4\">已扫描</el-tag>\n          <el-tag type=\"danger\" v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 3\">任务异常</el-tag>\n          <el-tag type=\"danger\" v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 4\">任务终止</el-tag>\n          <el-tag v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 0\">扫描中</el-tag>\n          <el-tag v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 1\">扫描中</el-tag>\n          <el-tag type=\"success\" v-else-if=\"form[0].taskType === 1 && form[0].taskStatus === 2\">已扫描</el-tag>\n        </div>\n      </el-col>\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">开始时间</span>\n          <span>{{ form[0].startTime }}</span>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row style=\"height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;\">\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">探测参数</span>\n          <span>  快速/存活探测启用/全连接</span>\n        </div>\n      </el-col>\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">结束时间</span>\n          <span>{{ form[0].endTime }}</span>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row style=\"height: 40px;line-height: 40px;margin-top: 1px;border: solid 1px #e7e3e361;\">\n      <el-col :span=\"12\">\n        <div>\n          <span class=\"b_tag\">端口范围</span>\n          <span>常规端口</span>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">主机漏洞列表</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"jobTaskList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"index\" label=\"序号\" width=\"70\" />\n            <el-table-column prop=\"ip\" label=\"IP\" />\n            <el-table-column prop=\"systemName\" label=\"操作系统  \" />\n            <el-table-column prop=\"pocRiskNum\" label=\"可入侵漏洞\" />\n            <el-table-column prop=\"highRiskNum\" label=\"高风险\" />\n            <el-table-column prop=\"middleRiskNum\" label=\"中风险\" />\n            <el-table-column prop=\"lowRiskNum\" label=\"低风险\" />\n            <el-table-column prop=\"pwNum\" label=\"弱口令数量\" />\n            <el-table-column prop=\"portNum\" label=\"开放端口数量\" />\n          </el-table>\n          <pagination\n            v-show=\"total>0\"\n            :total=\"total\"\n            :page.sync=\"queryParams.pageNum\"\n            :limit.sync=\"queryParams.pageSize\"\n            @pagination=\"getIpList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\">\n      <el-col>\n        <span class=\"s_span_tag\">漏洞分布列表</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"scanList\" v-loading=\"loading\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"expand\" v-if=\"jobRow.jobType === 1\">\n              <template slot-scope=\"props\">\n                <el-descriptions :column=\"1\" border :label-style=\"ls\">\n                  <el-descriptions-item label=\"受影响IP\">{{ props.row.hostIp }}</el-descriptions-item>\n                  <el-descriptions-item label=\"受影响端口\">{{ props.row.hostPort }}</el-descriptions-item>\n                  <el-descriptions-item label=\"受影响版本信息\">{{ props.row.versionInfo }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞发布时间\">{{ props.row.publishDate }}</el-descriptions-item>\n                  <el-descriptions-item label=\"威胁类型\">{{ props.row.vulnType }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞简介\">\n                    {{ props.row.vulnInfo }}\n                  </el-descriptions-item>\n                  <el-descriptions-item label=\"解决方案\"><span v-html=\"props.row.vulnSolve\"></span></el-descriptions-item>\n                </el-descriptions>\n              </template>\n            </el-table-column>\n            <el-table-column type=\"index\" label=\"序号\" width=\"70\" />\n            <el-table-column prop=\"vulnName\" label=\"漏洞名称\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.riskLevel == 1\" style=\"color: #13ce66\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.riskLevel == 2\" style=\"color: #ffba00\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.riskLevel == 3\" style=\"color: #ff4949\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.riskLevel == 4\"  style=\"color: #ff4949\">{{ scope.row.vulnName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"vulnType\" label=\"漏洞类型\" />\n            <el-table-column prop=\"riskLevel\" label=\"风险级别\"  >\n              <template slot-scope=\"scope\">\n                <el-tag v-if=\"scope.row.riskLevel == 1\" type=\"success\"\n                >低风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.riskLevel == 2\" type=\"warning\"\n                >中风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.riskLevel == 3\" type=\"danger\"\n                >高风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.riskLevel == 4\" type=\"danger\">严重</el-tag>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"scanTotal>0\"\n            :total=\"scanTotal\"\n            :page.sync=\"queryScanParams.pageNum\"\n            :limit.sync=\"queryScanParams.pageSize\"\n            @pagination=\"getScanList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 2\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"scanList\" v-loading=\"loading\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"expand\">\n              <template slot-scope=\"props\">\n                <el-descriptions :column=\"1\" border :label-style=\"ls\">\n                  <el-descriptions-item label=\"漏洞描述\">{{ props.row.description }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞危害\">{{ props.row.impact }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞解决方案\">{{ props.row.recommendation }}</el-descriptions-item>\n                  <el-descriptions-item label=\"扫描目标\">{{ props.row.url }}</el-descriptions-item>\n                  <el-descriptions-item label=\"漏洞构造请求\">\n                    <span v-html=\"props.row.evidence\" :style=\"props.row.evidence.includes('Host') ? { whiteSpace: 'pre-wrap' } : { whiteSpace: 'normal' }\"></span>\n                  </el-descriptions-item>\n\n                </el-descriptions>\n              </template>\n            </el-table-column>\n            <el-table-column type=\"index\" label=\"序号\" width=\"70\" />\n            <el-table-column prop=\"vulnName\" label=\"漏洞名称\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.severity == 1\" style=\"color: #1890ff\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.severity == 2\" style=\"color: #13ce66\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.severity == 3\" style=\"color: #ffba00\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.severity == 4\"  style=\"color: #ff4949\">{{ scope.row.vulnName }}</span>\n                <span v-if=\"scope.row.severity == 0\"  style=\"color: #ff4949\">{{ scope.row.vulnName }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column prop=\"vulnType\" label=\"漏洞类型  \" />\n            <el-table-column prop=\"severity\" label=\"风险级别\"  >\n              <template slot-scope=\"scope\">\n                <el-tag v-if=\"scope.row.severity == 1\" type=\"primary\"\n                >信息风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.severity == 2\" type=\"success\"\n                >低风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.severity == 3\" type=\"warning\"\n                >中风险</el-tag\n                >\n                <el-tag v-if=\"scope.row.severity == 4\" type=\"danger\">高风险</el-tag>\n                <el-tag v-if=\"scope.row.severity == 0\" type=\"danger\">信息风险</el-tag>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination\n            v-show=\"scanTotal>0\"\n            :total=\"scanTotal\"\n            :page.sync=\"queryScanParams.pageNum\"\n            :limit.sync=\"queryScanParams.pageSize\"\n            @pagination=\"getScanList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">弱口令列表</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"wpResultList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"index\" label=\"序号\" width=\"70px\" />\n            <el-table-column prop=\"hostIp\" label=\"IP\" />\n            <el-table-column prop=\"serviceType\" label=\"服务类型  \" />\n            <el-table-column prop=\"hostPort\" label=\"端口\" />\n            <el-table-column prop=\"username\" label=\"用户名\" />\n            <el-table-column prop=\"weakPassword\" label=\"密码\" />\n          </el-table>\n          <pagination\n            v-show=\"wpTotal>0\"\n            :total=\"wpTotal\"\n            :page.sync=\"queryWpParams.pageNum\"\n            :limit.sync=\"queryWpParams.pageSize\"\n            @pagination=\"getWpresultList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">开放端口列表</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"portResultList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"index\" label=\"序号\" width=\"70px\" />\n            <el-table-column prop=\"hostIp\" label=\"IP\" />\n            <el-table-column prop=\"hostPort\" label=\"端口  \" />\n            <el-table-column prop=\"procotol\" label=\"传输协议\" />\n            <el-table-column prop=\"serviceName\" label=\"服务名称\" />\n            <el-table-column prop=\"product\" label=\"服务与版本\" />\n          </el-table>\n          <pagination\n            v-show=\"portTotal>0\"\n            :total=\"portTotal\"\n            :page.sync=\"queryPortParams.pageNum\"\n            :limit.sync=\"queryPortParams.pageSize\"\n            @pagination=\"getPortresultList\"\n          />\n        </div>\n      </el-col>\n    </el-row>\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 2\">\n      <el-col>\n        <span class=\"s_span_tag\">附录-漏洞等级风险说明</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 2\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          漏洞是与信息资产有关的弱点或安全隐患。漏洞本身并不对资产构成危害，但是在一定条件得到满是时，漏洞会被威胁加以利用来对信息资产造成危险!本报告的漏洞共分了以下4种漏洞风险等级。\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 2\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"levelList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column prop=\"label\" label=\"危险程度\" width=\"120px\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.label === '高'\" style=\"color: #ff4949\">高</span>\n                <span v-if=\"scope.row.label === '中'\" style=\"color: #ffba00\">中</span>\n                <span v-if=\"scope.row.label === '低'\" style=\"color: #13ce66\">低</span>\n                <span v-if=\"scope.row.label === '风险信息'\" style=\"color: #1890ff\">风险信息</span>\n              </template>\n            </el-table-column>>\n            <el-table-column prop=\"value\" label=\"危险程度说明\" />\n          </el-table>\n        </div>\n      </el-col>\n    </el-row>\n\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">附录1-漏洞等级风险说明</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          漏洞是与信息资产有关的弱点或安全隐患。漏洞本身并不对资产构成危害，但是在一定条件得到满是时，漏洞会被威胁加以利用来对信息资产造成危险!本报告的漏洞共分了以下4种漏洞风险等级。\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"hostLevelList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column prop=\"label\" label=\"危险程度\" width=\"160px\">\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.label === '可入侵漏洞(严重)'\" style=\"color: #ff4949\">可入侵漏洞(严重)</span>\n                <span v-if=\"scope.row.label === '高'\" style=\"color: #ff4949\">高</span>\n                <span v-if=\"scope.row.label === '中'\" style=\"color: #ffba00\">中</span>\n                <span v-if=\"scope.row.label === '低'\" style=\"color: #13ce66\">低</span>\n              </template>\n            </el-table-column>>\n            <el-table-column prop=\"value\" label=\"危险程度说明\" />\n          </el-table>\n        </div>\n      </el-col>\n    </el-row>\n\n    <el-row class=\"s_d_row\" v-if=\"jobRow.jobType === 1\">\n      <el-col>\n        <span class=\"s_span_tag\">附录2-主机漏洞加固策略</span>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          基于多年的安全整改经验，提供了以下5种安全加固整改策略，并对不同整改建议的有效的防护度、整改困难度作了评级参考，有效防护度越高则表示加固效果越好，整改困难度越高则表示整改方案实施越难。您可以根据实际的业务情况，参考以下表，选择加固整改策略。\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"10\" style=\"margin-top: 20px;\" v-if=\"jobRow.jobType === 1\">\n      <el-col :span=\"24\">\n        <div class=\"collapse-content-div\">\n          <el-table :data=\"hostStrategyList\" style=\"width: 100%\" :header-cell-style=\"headerCellStyle\">\n            <el-table-column type=\"index\" label=\"序号\" width=\"80px\" />\n            <el-table-column prop=\"value\" label=\"加固整改策略\">\n            </el-table-column>>\n            <el-table-column prop=\"label\" label=\"有效防护度\" align=\"center\"  width=\"130px\" />\n            <el-table-column prop=\"level\" label=\"整改困难度\" align=\"center\" width=\"130px\"/>\n          </el-table>\n        </div>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\n  import {\n    hostriskstat,\n    listIpPortresult,\n    listIpTaskresult,\n    listIpWpresult,\n    listVulnresult,\n    listWebresult,\n    middlehighlevelstat\n  } from '@/api/monitor2/wpresult'\n\n  export default {\n    name: 'ffJobTasksDetail',\n    props: {\n      form: {\n        type: Array,\n        default: null\n      },\n      jobRow: {\n        type: Object,\n        default: null\n      },\n      macAipList: {\n        type: Array,\n        default: null\n      }\n    },\n    data() {\n      return {\n        activeNames: ['1', '2', '3', '4'],\n        ls: {\n          'width': '130px',\n        },\n        collapseLabelSpan: 4,\n        collapseContentSpan: 8,\n        levelList: [\n          {\n            label: '高',\n            value: '攻击者可以远程操作系统文件、读写后台数据库、执行任意命令或进行远程拒绝服务攻击。'\n          },\n          {\n            label: '中',\n            value: '攻击者可以利用Web网站攻击其他用户，读取系统文件或后台数据库。'\n          },\n          {\n            label: '低',\n            value: '攻击者可以获取某些系统、网站、文件的信息或冒用身份。'\n          },\n          {\n            label: '风险信息',\n            value: '攻击者可以获取网站相关信息，可能是非敏感信息。'\n          }\n        ],\n        hostLevelList: [\n          {\n            label: '可入侵漏洞(严重)',\n            value: '有公开利用方法，已验证可远程执行任意命令或者代码，或对系统进行远程拒绝服务攻击，或可获取重要敏感效据。此部分漏洞基于漏洞原理验证识别，在漏洞名称中，会标识【原理扫描】。'\n          },\n          {\n            label: '高',\n            value: '攻击者可以远程执行任意命令或者代码，或对系统进行远程拒绝服务攻击，或可获取重要教感数据。此部分漏洞主要通过服务版本识别，可能会有一定的误报。'\n          },\n          {\n            label: '中',\n            value: '攻击者可以远程创建、修改、删除部分文件或数据，或对普通服务进行拒绝服务攻击。此部分温洞主要通过服务版本识别，可能会有一定的误报。'\n          },\n          {\n            label: '低',\n            value: '攻击者可以获取某些系统、服务的信息，或读取某些系统文件和教据。此部分漏洞主要通过服务版本识别，可能会有一定的误报。'\n          }\n        ],\n        hostStrategyList: [\n          {\n            label: '高',\n            level: '高',\n            value: '根据漏洞整改建议打补丁或者修改配置进行安全加固，加固前建议作好相关备份以使回退;建议所有 Windows 系统使用\"Windows Update\"进行更新。'\n          },\n          {\n            label: '高',\n            level: '低',\n            value: '若存在漏洞的应用服务平时不需要使用，建议关闭这些不必要的服务或应用。'\n          },\n          {\n            label: '中',\n            level: '低',\n            value: '若存在漏洞的应用服务不需要对外开放或者只对部分用户开放，建议在主机防火墙上进行访问控制，限定只有合法IP才能访问此应用。'\n          },\n          {\n            label: '低',\n            level: '低',\n            value: '若不便在主机防火墙上配置，建议在网络/出口防火墙上做白名单访问控制。'\n          },\n          {\n            label: '低',\n            level: '中',\n            value: '建议修改应用的banner信息，隐藏应用名称、版本号等信息，让攻击者无法识别目标系统，便之难以进行针对性的攻击入侵。'\n          }\n        ],\n        headerCellStyle: { 'font-weight': 'normal', color: '#979797' },\n        cellStyle: { 'font-weight': 'bold' },\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        queryWpParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        queryPortParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        queryScanParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        jobTaskList: [],\n        wpResultList: [],\n        scanList: [],\n        portResultList: [],\n        holeMapData: [],\n        xAsisArr: [],\n        pieMapData: [],\n        portTotal: 0,\n        wpTotal: 0,\n        total: 0,\n        scanTotal: 0,\n        loading: false\n      }\n    },\n    mounted() {\n      this.getIpList()\n      this.getWpresultList()\n      this.getPortresultList()\n      this.getScanList()\n      this.getMiddlehighlevelstat()\n    },\n    methods: {\n      getIpList() {\n        this.queryParams.taskId = this.form[0].taskId\n        if (this.jobRow.jobType === 1) {\n          listIpTaskresult(this.queryParams).then(response => {\n            this.jobTaskList = response.rows\n            this.total = response.total;\n            this.$nextTick(() => this.getHostriskstat())\n          }).catch(() => {\n          });\n        }\n      },\n      getWpresultList() {\n        this.queryWpParams.taskId = this.form[0].taskId\n        if (this.jobRow.jobType === 1) {\n          listIpWpresult(this.queryWpParams).then(response => {\n            this.wpResultList = response.rows\n            this.wpTotal = response.total;\n          }).catch(() => {\n          });\n        }\n      },\n      getPortresultList() {\n        this.queryPortParams.taskId = this.form[0].taskId\n        if (this.jobRow.jobType === 1) {\n          listIpPortresult(this.queryPortParams).then(response => {\n            this.portResultList = response.rows\n            this.portTotal = response.total;\n          }).catch(() => {\n          });\n        }\n      },\n      getHostriskstat() {\n        if (this.jobRow.jobType === 1) {\n          this.xAsisArr = []\n          this.holeMapData = []\n          if (this.total > 1) {\n            hostriskstat(this.form[0].taskId).then(response => {\n              response.data.forEach(e => {\n                if (e.max_risk_level === 1) {\n                  this.xAsisArr.push('低风险主机数')\n                  const low = {\n                    value: e.host_num,\n                    itemStyle: {\n                      color: '#13ce66'\n                    }\n                  }\n                  this.holeMapData.push(low)\n                }\n                if (e.max_risk_level === 2) {\n                  this.xAsisArr.push('中风险主机数')\n                  const middle = {\n                    value: e.host_num,\n                    itemStyle: {\n                      color: '#ffba00'\n                    }\n                  }\n                  this.holeMapData.push(middle)\n                }\n                if (e.max_risk_level === 3) {\n                  this.xAsisArr.push('高风险主机数')\n                  const high = {\n                    value: e.host_num,\n                    itemStyle: {\n                      color: '#ff4949'\n                    }\n                  }\n                  this.holeMapData.push(high)\n                }\n                if (e.max_risk_level === 4) {\n                  this.xAsisArr.push('可入侵主机数')\n                  const poc = {\n                    value: e.host_num,\n                    itemStyle: {\n                      color: '#1890FF'\n                    }\n                  }\n                  this.holeMapData.push(poc)\n                }\n              })\n              this.$nextTick(() => this.barMapInit())\n            })\n\n          } else {\n            this.xAsisArr.push('可入侵漏洞')\n            const poc = {\n              value: this.form[0].pocRiskNum,\n              itemStyle: {\n                color: '#1890FF'\n              }\n            }\n            this.holeMapData.push(poc)\n\n            this.xAsisArr.push('高风险')\n            const high = {\n              value: this.form[0].highRiskNum,\n              itemStyle: {\n                color: '#ff4949'\n              }\n            }\n            this.holeMapData.push(high)\n            this.xAsisArr.push('中风险')\n            const middle = {\n              value: this.form[0].middleRiskNum,\n              itemStyle: {\n                color: '#ffba00'\n              }\n            }\n            this.holeMapData.push(middle)\n            this.xAsisArr.push('低风险')\n            const low = {\n              value: this.form[0].lowRiskNum,\n              itemStyle: {\n                color: '#13ce66'\n              }\n            }\n            this.holeMapData.push(low)\n            this.barMapInit()\n          }\n        }\n      },\n      getMiddlehighlevelstat() {\n        if (this.jobRow.jobType !== 1) {\n          this.pieMapData =[]\n          middlehighlevelstat(this.form[0].taskId).then(response => {\n            this.pieMapData = response.data\n            this.$nextTick(() => this.pieMapInit())\n          })\n          this.xAsisArr = []\n          this.holeMapData = []\n          this.xAsisArr.push('高风险')\n          const high = {\n            value: this.form[0].highRiskNum,\n            itemStyle: {\n              color: '#ff4949'\n            }\n          }\n          this.holeMapData.push(high)\n          this.xAsisArr.push('中风险')\n          const middle = {\n            value: this.form[0].middleRiskNum,\n            itemStyle: {\n              color: '#ffba00'\n            }\n          }\n          this.holeMapData.push(middle)\n          this.xAsisArr.push('低风险')\n          const low = {\n            value: this.form[0].lowRiskNum,\n            itemStyle: {\n              color: '#13ce66'\n            }\n          }\n          this.holeMapData.push(low)\n          this.xAsisArr.push('风险信息')\n          const poc = {\n            value: this.form[0].infoRiskNum,\n            itemStyle: {\n              color: '#1890FF'\n            }\n          }\n          this.holeMapData.push(poc)\n          this.barMapInit()\n        }\n      },\n      getScanList() {\n        this.xAsisArr = []\n        this.holeMapData = []\n        this.pieMapData = []\n        this.loading = true;\n        this.queryScanParams.taskId = this.form[0].taskId\n        if (this.jobRow.jobType === 1) {\n          listVulnresult(this.queryScanParams).then(response => {\n            this.scanList = response.rows\n            this.scanTotal = response.total;\n            this.loading = false;\n          }).catch(() => {\n            this.loading = false;\n          });\n          const highRisk = {\n            name : '高风险',\n            value: this.form[0].highRiskNum || 0\n          }\n          const middleRisk = {\n            name : '中风险',\n            value: this.form[0].middleRiskNum || 0\n          }\n          const lowRisk = {\n            name : '低风险',\n            value: this.form[0].lowRiskNum || 0\n          }\n          const pocRisk = {\n            name : '可入侵漏洞',\n            value: this.form[0].pocRiskNum || 0\n          }\n          this.pieMapData.push(highRisk)\n          this.pieMapData.push(middleRisk)\n          this.pieMapData.push(lowRisk)\n          this.pieMapData.push(pocRisk)\n          this.pieMapInit()\n\n\n        } else {\n          listWebresult(this.queryScanParams).then(response => {\n            this.scanList = response.rows\n            this.scanList.forEach(e => {\n              e.evidence = this.decodeBase64(e.evidence)\n            })\n            this.scanTotal = response.total;\n            this.loading = false;\n          }).catch(() => {\n            this.loading = false;\n          });\n        }\n\n      },\n      decodeBase64(base64String) {\n        return decodeURIComponent(escape(window.atob(base64String)));\n      },\n      pieMapInit() {\n        const pieMapChar = this.$echarts.init(this.$refs.pieMap)\n        let title = ''\n        if (this.jobRow.jobType === 1) {\n          title =  '风险漏洞比例'\n        } else {\n          title =  '高中风险漏洞类型比例'\n        }\n        pieMapChar.setOption({\n          title: {\n            text: title,\n            left: 'center'\n          },\n          tooltip: {\n            trigger: 'item'\n          },\n          legend: {\n            orient: 'vertical',\n            x: 'right',\n            y: 'center'\n          },\n          color: ['#ff4949','#ffba00','#13ce66','#1890FF','#ea7ccc','#fc8452','#3ba272','#73c0de','#fac858','#91cc75','#ee6666','#9a60b4'],\n          series: [\n            {\n              name: title,\n              type: 'pie',\n              radius: ['40%', '70%'],\n              avoidLabelOverlap: false,\n              itemStyle: {\n                borderRadius: 10,\n                borderColor: '#fff',\n                borderWidth: 2\n              },\n              label: {\n                show: false,\n                position: 'center'\n              },\n              labelLine: {\n                show: false\n              },\n              data: this.pieMapData\n            }\n          ]\n        })\n      },\n      barMapInit() {\n        const holeMapChar = this.$echarts.init(this.$refs.holeMap)\n        let title = ''\n        if (this.jobRow.jobType === 1) {\n          title =  '风险主机数'\n        } else {\n          title =  '漏洞风险级别'\n        }\n        holeMapChar.setOption({\n          title: {\n            text:title,\n            left: 'center'\n          },\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          legend: {\n            orient: 'vertical',\n            x: 'right',\n            y: 'center'\n          },\n          color: ['#1890FF'],\n          grid: {\n            top: 30,\n            left: 20,\n            right: 20,\n            bottom: 10,\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            data: this.xAsisArr\n          },\n          yAxis: {\n            type: 'value',\n            show: false,\n          },\n          series: [\n            {\n              type: 'bar',\n              data: this.holeMapData,\n              barWidth: '30%'\n            }\n          ]\n        })\n      }\n    }\n  }\n</script>\n\n<style scoped>\n  .back_card {\n    background-color: #f3f3f3;\n  }\n\n  .s_d_row {\n    height: 50px;\n    line-height: 50px;\n    color: #1890ff;\n    background-color: #f3f3f3;\n    margin-top: 10px;\n    border-bottom: solid 2px #1890ff;\n  }\n  .s_span_tag {\n    margin-left: 10px;\n    font-weight: 600;\n  }\n  ::v-deep .el-card__body {\n    padding: 15px 20px;\n  }\n  .b_tag {\n    display: inline-block;\n    padding: 0px 20px;\n    font-weight: 600;\n    width: 150px;\n    background-color: #f9f7f7;\n    margin-right: 10px;\n  }\n  .truncate {\n    display: inline-flex;\n    width: 70%; /* 定义容器宽度 */\n    white-space: nowrap; /* 不换行 */\n    overflow: hidden; /* 超出部分隐藏 */\n    text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */\n    text-align: center; /* 文本居中 */\n  }\n</style>\n\n<style lang=\"scss\" scoped>\n  .asset-tag{\n    margin-left: 5px;\n    max-width: 100%;\n    overflow: hidden;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    vertical-align: middle;\n  }\n  .overflow-tag:not(:first-child){\n    margin-top: 5px;\n  }\n  .my-descriptions .fixed-width-label {\n    width: 140px;\n  }\n</style>\n"]}]}