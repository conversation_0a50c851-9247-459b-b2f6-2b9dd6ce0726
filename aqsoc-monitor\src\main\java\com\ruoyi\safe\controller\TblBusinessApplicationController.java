package com.ruoyi.safe.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mchange.lang.LongUtils;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.CollectionUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.dimension.domain.TblApplicationLink;
import com.ruoyi.dimension.domain.TblAssetCompanyEmp;
import com.ruoyi.dimension.mapper.TblAssetCompanyEmpMapper;
import com.ruoyi.dimension.service.ITblApplicationLinkService;
import com.ruoyi.monitor2.service.ITblResidentPersonnelService;
import com.ruoyi.monitor2.vo.TblResidentPersonnelVo;
import com.ruoyi.safe.aspectj.AssetAction;
import com.ruoyi.safe.aspectj.AssetDataHandle;
import com.ruoyi.safe.countByDict.service.ICountByDictService;
import com.ruoyi.safe.domain.*;
import com.ruoyi.safe.domain.dto.QueryDeptApplicationCountDto;
import com.ruoyi.safe.domain.dto.TblBusinessApplicationDto;
import com.ruoyi.safe.mapper.TblBusinessApplicationMapper;
import com.ruoyi.safe.mapper.TblMapperMapper;
import com.ruoyi.safe.service.*;
import com.ruoyi.safe.vo.HardWareEV;
import com.ruoyi.safe.vo.countDict.CountDictTypeVO;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.service.*;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 业务应用系统Controller
 *
 * <AUTHOR>
 * @date 2022-11-10
 */
@RestController
@RequestMapping("/safe/application")
public class TblBusinessApplicationController extends BaseController
{
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;
    @Autowired
    private TblBusinessApplicationMapper tblBusinessApplicationMapper;
    @Autowired
    private ITblFunctionApplicationService tblFunctionApplicationService;
    @Autowired
    private TblApplicationServerController tblApplicationServerController;
    @Autowired
    private TblApplicationSafeController tblApplicationSafeController;
    @Autowired
    private TblApplicationNetwareController tblApplicationNetwareController;
    @Autowired
    private ITblApplicationServerService tblApplicationServerService;
    @Autowired
    private ITblApplicationSafeService tblApplicationSafeService;
    @Autowired
    private ITblApplicationNetwareService tblApplicationNetwareService;
    @Autowired
    private ITblDeployService tblDeployService;
    @Autowired
    private Snowflake snowflake;
    @Autowired
    private ITblMapperService tblMapperService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private INetworkDomainService networkDomainService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ICountByDictService countByDictService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private ITblEquipmentInfoService tblEquipmentInfoService;
    @Autowired
    private ITblApplicationLinkService tblApplicationLinkService;
    @Autowired
    private TblAssetCompanyEmpMapper tblAssetCompanyEmpMapper;

    @Autowired
    private ITblResidentPersonnelService tblResidentPersonnelService;

    @Autowired
    private IAssetVulnerabilityStatsService assetVulnerabilityStatsService;

    @Autowired
    private TblMapperMapper tblMapperMapper;


    private static final String DOMAIN_NAME_REGEX =
            "^(?=^.{3,255}$)(http(s)?:\\/\\/)?(www\\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\\d+)*(\\/\\w+\\.\\w+)*$";

    /**
     * 查询业务应用系统列表
     */
    //@PreAuthorize("@ss.hasPermi('safe:application:list')")
//    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @GetMapping("/list")
    public TableDataInfo list(TblBusinessApplication tblBusinessApplication)
    {
        /*// 当角色权限仅自己时只能看到自己创建的业务应用
        SysUser sysUser = getLoginUser().getUser();
        List<SysRole> sysUserRoles = sysUser.getRoles();
        if (CollUtil.isNotEmpty(sysUserRoles)){
            List<SysRole> sysRoleList = sysUserRoles.stream().filter(sysRole -> "5".equals(sysRole.getDataScope())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(sysRoleList)) {
                Map<String, Object> params = tblBusinessApplication.getParams();
                params.put("dataScope", StringUtils.format(" AND {}.user_id = {} ", 'a', getUserId()));
            }
        }*/
        startPage();
        List<TblBusinessApplication> list = tblBusinessApplicationService.selectTblBusinessApplicationList(tblBusinessApplication);
        if (CollUtil.isNotEmpty(list)){
            // 查询业务系统资产填报自定义已配置的字段
            List<JSONObject> assetFieldsItemList = tblBusinessApplicationService.selectAssetFieldsItemList("1");
            if (CollUtil.isNotEmpty(assetFieldsItemList)){
                // 拿到基本信息 备案信息 测评信息 拓扑结构信息 其他基本信息 用户规模 业务描述配置字段
                List<JSONObject> basicInformationList = assetFieldsItemList.stream()
                        .filter(jsonObject -> "基本信息".equals(jsonObject.getString("formName")) ||
                                "备案信息".equals(jsonObject.getString("formName")) ||
                                "测评信息".equals(jsonObject.getString("formName")) ||
                                "拓扑结构信息".equals(jsonObject.getString("formName")) ||
                                "其他基本信息".equals(jsonObject.getString("formName")) ||
                                "用户规模".equals(jsonObject.getString("formName")) ||
                                "业务描述".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 拿到外部连接信息信息配置字段
                List<JSONObject> externalConnectionInformation = assetFieldsItemList.stream().filter(jsonObject -> "外部连接信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 拿到运营维护情况
                List<JSONObject> operationMaintenanceInformation = assetFieldsItemList.stream().filter(jsonObject -> "运营维护情况".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 拿到其他基本信息
                //List<JSONObject> otherBasicInformation = assetFieldsItemList.stream().filter(jsonObject -> "其他基本信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 拿到用户规模
                //List<JSONObject> userScale = assetFieldsItemList.stream().filter(jsonObject -> "用户规模".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 拿到业务描述
                //List<JSONObject> businessDescription = assetFieldsItemList.stream().filter(jsonObject -> "业务描述".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 拿到功能模块
                List<JSONObject> functionModule = assetFieldsItemList.stream().filter(jsonObject -> "功能模块".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 拿到所安装服务器环境
                List<JSONObject> installedServerEnvironment = assetFieldsItemList.stream().filter(jsonObject -> "所安装服务器环境".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 所安装数据库环境
                List<JSONObject> installedDatabaseEnvironment = assetFieldsItemList.stream().filter(jsonObject -> "所安装数据库环境".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 关联网络设备
                List<JSONObject> relatedNetworkDevices = assetFieldsItemList.stream().filter(jsonObject -> "关联网络设备".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                // 关联安全设备
                List<JSONObject> relatedSecurityDevices = assetFieldsItemList.stream().filter(jsonObject -> "关联安全设备".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                for (int i = 0; i < list.size(); i++) {
                    TblBusinessApplication businessApplication = list.get(i);
                    // 定义分母
                    int denominator = assetFieldsItemList.size();
                    // 初始化分子
                    AtomicReference<Integer> numerator = new AtomicReference<>(0);
                    TblApplicationServer tblApplicationServer = new TblApplicationServer();
                    tblApplicationServer.setAssetId(businessApplication.getAssetId());
                    tblApplicationServer.setType("0");
                    List<TblApplicationServer> applicationServerList = tblApplicationServerService.selectTblApplicationServerList(tblApplicationServer);
                    // 统计基本信息 备案信息 测评信息 拓扑结构 其他基本信息 用户规模 业务描述配信息的完整度
                    if (CollUtil.isNotEmpty(basicInformationList)){
                        basicInformationList.forEach(basicInformationInfo -> {
                            if (StringUtils.isNotBlank(basicInformationInfo.getString("fieldKey"))){
                                String fieldKey = basicInformationInfo.getString("fieldKey");
                                if ("associationServer".equals(fieldKey)){
                                    if (CollUtil.isNotEmpty(applicationServerList)){
                                        numerator.getAndSet(numerator.get() + 1);
                                    }
                                }else if ("otherSystemNotes".equals(fieldKey)){
                                    if (StrUtil.isNotBlank(businessApplication.getOtherSystemNotes())){
                                        numerator.getAndSet(numerator.get() + 1);
                                    }else {
                                        if (businessApplication.getSystemType() != null && businessApplication.getSystemType() != 12){
                                            numerator.getAndSet(numerator.get() + 1);
                                        }
                                    }
                                } else if ("vendor".equals(fieldKey)) {
                                    if (StrUtil.isNotBlank(businessApplication.getVendors())){
                                        numerator.getAndSet(numerator.get() + 1);
                                    }
                                } else {
                                    //获取businessApplication变量名为fieldKey的值
                                    Object fieldValue = ReflectUtil.getFieldValue(businessApplication, fieldKey);
                                    if (fieldValue != null && !"".equals(fieldValue)){
                                        numerator.getAndSet(numerator.get() + 1);
                                    }else {
                                        //System.out.println(fieldKey);
                                    }
                                }
                            }
                        });
                    }
                    // 统计外部连接完整度
                   if (CollUtil.isNotEmpty(externalConnectionInformation)){
                        //拿到当前业务系统的外部连接信息
                        TblApplicationLink tblApplicationLink = new TblApplicationLink();
                        tblApplicationLink.setAssetId(businessApplication.getAssetId());
                        List<TblApplicationLink> tblApplicationLinks = tblApplicationLinkService.selectTblApplicationLinkList(tblApplicationLink);
                        externalConnectionInformation.forEach(externalConnectionInfo -> {
                            if (StringUtils.isNotBlank(externalConnectionInfo.getString("fieldKey"))){
                                String fieldKey = externalConnectionInfo.getString("fieldKey");
                                if (CollUtil.isNotEmpty(tblApplicationLinks)){
                                    AtomicReference<Integer> count = new AtomicReference<>(0);
                                    for (TblApplicationLink tblApplicationLinkInfo : tblApplicationLinks){
                                        Object fieldValue = ReflectUtil.getFieldValue(tblApplicationLinkInfo, fieldKey);
                                        // 但凡里面有一个为空
                                        if (fieldValue == null || "".equals(fieldValue)){
                                            count.set(0);
                                            break;
                                        } else {
                                            count.set(1);
                                        }
                                    }
                                    numerator.getAndSet(numerator.get() + count.get());
                                }
                            }
                        });
                    }
                    // 统计运营维护情况完整度
                    if (CollUtil.isNotEmpty(operationMaintenanceInformation)){
                        List<TblAssetCompanyEmp> tblAssetCompanyEmps = tblAssetCompanyEmpMapper.selectTblAssetCompanyEmpByAssetId(businessApplication.getAssetId());
                        if (StringUtils.isNotEmpty(tblAssetCompanyEmps)) {
                            String[] eidsArray = tblAssetCompanyEmps.stream().map(tblAssetCompanyEmpsInfo -> {
                                return tblAssetCompanyEmpsInfo.getEid().toString();
                            }).toArray(String[]::new);
                            List<TblResidentPersonnelVo> tblResidentPersonnelVos = tblResidentPersonnelService.selectTblResidentPersonnelByFIds(eidsArray);
                            operationMaintenanceInformation.forEach(operationMaintenanceInfo -> {
                                if (StringUtils.isNotBlank(operationMaintenanceInfo.getString("fieldKey"))){
                                    String fieldKey = operationMaintenanceInfo.getString("fieldKey");
                                    // 如果fieldKey中的值为stationTime的时候，则赋值为fStationStart
                                    if ("stationTime".equals(fieldKey)){
                                        fieldKey = "fStationStart";
                                    }
                                    if ("F_be_stationed".equals(fieldKey)){
                                        fieldKey = "fBeStationed";
                                    }
                                    if ("F_mobile_phone".equals(fieldKey)){
                                        fieldKey = "fMobilePhone";
                                    }
                                    if ("F_name".equals(fieldKey)){
                                        fieldKey = "fName";
                                    }
                                    if (CollUtil.isNotEmpty(tblResidentPersonnelVos)){
                                        AtomicReference<Integer> count = new AtomicReference<>(0);
                                        String finalFieldKey = fieldKey;
                                        for (TblResidentPersonnelVo tblResidentPersonnelInfo : tblResidentPersonnelVos){
                                            Object fieldValue = ReflectUtil.getFieldValue(tblResidentPersonnelInfo, finalFieldKey);
                                            if (fieldValue == null || "".equals(fieldValue)){
                                                count.set(0);
                                                break;
                                            }else  {
                                                count.set(1);
                                            }
                                        }
                                        numerator.getAndSet(numerator.get() + count.get());
                                    }
                                }
                            });
                        }
                    }
                    //统计功能模块完整度
                    if (CollUtil.isNotEmpty(functionModule)){
                        List<TblMapper> tblMapperList = tblMapperMapper.selectTblMapperByApplicationId(businessApplication.getAssetId());
                        functionModule.forEach(functionModuleInfo -> {
                            if (StringUtils.isNotBlank(functionModuleInfo.getString("fieldKey"))){
                                String fieldKey = functionModuleInfo.getString("fieldKey");
                                if (CollUtil.isNotEmpty(tblMapperList)){
                                    AtomicReference<Integer> count = new AtomicReference<>(0);
                                    for (TblMapper tblMapperInfo : tblMapperList){
                                        Object fieldValue = ReflectUtil.getFieldValue(tblMapperInfo, fieldKey);
                                        if (fieldValue == null || "".equals(fieldValue)){
                                            count.set(0);
                                            break;
                                        } else {
                                            count.set(1);
                                        }
                                    }
                                    numerator.getAndSet(numerator.get() + count.get());
                                }
                            }
                        });
                    }
                    //统计所安装服务器完整度
                    if (CollUtil.isNotEmpty(installedServerEnvironment)){
                        installedServerEnvironment.forEach(installedServerEnvironmentInfo -> {
                            if (StringUtils.isNotBlank(installedServerEnvironmentInfo.getString("fieldKey"))){
                                String fieldKey = installedServerEnvironmentInfo.getString("fieldKey");
                                if (CollUtil.isNotEmpty(applicationServerList)){
                                    AtomicReference<Integer> count = new AtomicReference<>(0);
                                    handleDataValidation(applicationServerList,fieldKey,count,businessApplication);
                                    numerator.getAndSet(numerator.get() + count.get());
                                }
                            }
                        });
                    }
                    //统计所安装数据库环境完整度
                    if (CollUtil.isNotEmpty(installedDatabaseEnvironment)){
                        tblApplicationServer.setType("1");
                        List<TblApplicationServer> applicationServerLists = tblApplicationServerService.selectTblApplicationServerList(tblApplicationServer);
                        installedDatabaseEnvironment.forEach(installedDatabaseEnvironmentInfo -> {
                            if (StringUtils.isNotBlank(installedDatabaseEnvironmentInfo.getString("fieldKey"))){
                                String fieldKey = installedDatabaseEnvironmentInfo.getString("fieldKey");
                                if (CollUtil.isNotEmpty(applicationServerLists)){
                                    AtomicReference<Integer> count = new AtomicReference<>(0);
                                    handleDataValidation(applicationServerLists,fieldKey,count,businessApplication);
                                    numerator.getAndSet(numerator.get() + count.get());
                                }
                            }
                        });
                    }
                    //统计关联网络设备完整度
                    if (CollUtil.isNotEmpty(relatedNetworkDevices)){
                        TblApplicationNetware tblApplicationNetware = new TblApplicationNetware();
                        tblApplicationNetware.setAssetId(businessApplication.getAssetId());
                        List<TblApplicationNetware> applicationNetwareList = tblApplicationNetwareService.selectTblApplicationNetwareList(tblApplicationNetware);
                        relatedNetworkDevices.forEach(relatedNetworkDevicesInfo -> {
                            if (StringUtils.isNotBlank(relatedNetworkDevicesInfo.getString("fieldKey"))){
                                String fieldKey = relatedNetworkDevicesInfo.getString("fieldKey");
                                if (CollUtil.isNotEmpty(applicationNetwareList)){
                                    AtomicReference<Integer> count = new AtomicReference<>(0);
                                    for (TblApplicationNetware applicationNetwareInfo : applicationNetwareList){
                                        TblNetworkDevices network = applicationNetwareInfo.getNetwork();
                                        if (network != null){
                                            Object fieldValue = ReflectUtil.getFieldValue(network, fieldKey);
                                            if (fieldValue == null || "".equals(fieldValue)){
                                                count.set(0);
                                                break;
                                            } else {
                                                count.set(1);
                                            }
                                        }
                                    }
                                    numerator.getAndSet(numerator.get() + count.get());
                                }
                            }
                        });
                    }
                    //统计关联安全设备完整度
                    if (CollUtil.isNotEmpty(relatedSecurityDevices)){
                        TblApplicationSafe tblApplicationSafe = new TblApplicationSafe();
                        tblApplicationSafe.setAssetId(businessApplication.getAssetId());
                        List<TblApplicationSafe> applicationSafeList = tblApplicationSafeService.selectTblApplicationSafeList(tblApplicationSafe);
                        relatedSecurityDevices.forEach(relatedSecurityDevicesInfo -> {
                            if (StringUtils.isNotBlank(relatedSecurityDevicesInfo.getString("fieldKey"))){
                                String fieldKey = relatedSecurityDevicesInfo.getString("fieldKey");
                                if (CollUtil.isNotEmpty(applicationSafeList)){
                                    AtomicReference<Integer> count = new AtomicReference<>(0);
                                    for (TblApplicationSafe applicationSafeInfo : applicationSafeList){
                                        TblSafety safety = applicationSafeInfo.getSafe();
                                        if (safety != null){
                                            Object fieldValue = ReflectUtil.getFieldValue(safety, fieldKey);
                                            if (fieldValue == null || "".equals(fieldValue)){
                                                count.set(0);
                                                break;
                                            } else {
                                                count.set(1);
                                            }
                                        }
                                    }
                                    numerator.getAndSet(numerator.get() + count.get());
                                }
                            }
                        });
                    }
                    double completionRate = ((double) numerator.get() / denominator) * 100;
                    BigDecimal bd = new BigDecimal(completionRate).setScale(2, RoundingMode.DOWN);
                    businessApplication.setCompletenessStr(bd.toString());
                    businessApplication.setCompleteness(bd);
                }
            }
        }

        // 为业务系统列表添加漏洞统计信息
        assetVulnerabilityStatsService.batchEnrichBusinessApplicationsWithStats(list);

        return getDataTable(list);
    }

    public void handleDataValidation(List<TblApplicationServer> applicationServerList,String fieldKey,AtomicReference<Integer> count,TblBusinessApplication businessApplication){
        for (TblApplicationServer applicationServerInfo : applicationServerList){
            TblServer serverInfo = applicationServerInfo.getServer();
            if ("assetName".equals(fieldKey)){
                // 获取资产名称
                if (serverInfo != null){
                    if (StrUtil.isBlank(serverInfo.getAssetName())){
                        count.set(0);
                        break;
                    }else {
                        count.set(1);
                    }
                }
            }else if ("deployComponent".equals(fieldKey)){
                // 获取部署组件
                if (serverInfo != null){
                    TblDeploy tblDeploy = new TblDeploy();
                    tblDeploy.setAssetId(serverInfo.getAssetId());
                    List<TblDeploy> deployList = tblDeployService.selectPcServerDeployList(tblDeploy);
                    if (CollUtil.isNotEmpty(deployList)){
                        count.set(1);
                    }else {
                        count.set(0);
                        break;
                    }
                }
            }else if ("deployFunc".equals(fieldKey)){
                // 部署功能
                if (serverInfo != null) {
                    TblDeploy tblDeploy = new TblDeploy();
                    tblDeploy.setApplicationId(businessApplication.getAssetId());
                    tblDeploy.setAssetId(serverInfo.getAssetId());
                    List<TblDeploy> tblDeploys = tblDeployService.selectTblDeployList(tblDeploy);
                    if (CollUtil.isNotEmpty(tblDeploys)){
                        count.set(1);
                    }else {
                        count.set(0);
                        break;
                    }
                }
            }else if ("ip".equals(fieldKey)){
                if (serverInfo != null){
                    if (StrUtil.isBlank(serverInfo.getIp())){
                        count.set(0);
                        break;
                    }else {
                        count.set(1);
                    }
                }
            }else if ("isVirtual".equals(fieldKey)){
                if (serverInfo != null){
                    if (StrUtil.isBlank(serverInfo.getIsVirtual())){
                        count.set(0);
                        break;
                    }else {
                        count.set(1);
                    }
                }
            }else if ("optsystem".equals(fieldKey)){
                if (serverInfo != null){
                    if (serverInfo.getOptsystem() == null){
                        count.set(0);
                        break;
                    }else {
                        count.set(1);
                    }
                }
            }else if ("port".equals(fieldKey)){
                if (StrUtil.isBlank(applicationServerInfo.getPort())){
                    count.set(0);
                    break;
                }else {
                    count.set(1);
                }
            }else if ("dbsystem".equals(fieldKey)){
                if (serverInfo != null){
                    if (serverInfo.getDbsystem() == null){
                        count.set(0);
                        break;
                    }else {
                        count.set(1);
                    }
                }
            }
        }
    }


//    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @PreAuthorize("@ss.hasPermi('safe:business:search')")
    @GetMapping("/getPageList")
    public TableDataInfo getPageList(TblBusinessApplication tblBusinessApplication) {
        startPage();
        List<TblBusinessApplication> list = tblBusinessApplicationService.getPageList(tblBusinessApplication);
        return getDataTable(list);
    }


    /**
     * 导出业务应用系统列表
     *  @PreAuthorize("@ss.hasPermi('safe:application:export')")
     */
    @Log(title = "业务应用系统", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( TblBusinessApplication tblBusinessApplication, HttpServletResponse response)
    {
        List<TblBusinessApplication> list = tblBusinessApplicationService.getPageList(tblBusinessApplication);
        TblApplicationServer tblApplicationServer = new TblApplicationServer();
        tblApplicationServer.setType("0");
        List<TblApplicationServer> tblApplicationServers = tblApplicationServerService.selectTblApplicationServerList(tblApplicationServer);
        if (CollectionUtils.isNotEmpty(list)){
            list.forEach(item -> {
                if (StrUtil.isNotBlank(item.getManagerName())){
                    String[] split = item.getManagerName().split(",");
                    item.setManagerName(split[0]);
                }
                if (CollUtil.isNotEmpty(tblApplicationServers)){
                    List<TblApplicationServer> collect = tblApplicationServers.stream()
                            .filter(applicationServer -> Objects.equals(applicationServer.getAssetId(), item.getAssetId())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)){
                        List<String> stringList = collect.stream().map(TblApplicationServer::getServer).collect(Collectors.toList()).stream().map(TblServer::getIp).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(stringList)){
                            item.setIp(String.join(",", stringList));
                        }
                    }
                }
            });
        }
        ExcelUtil<TblBusinessApplication> util = new ExcelUtil<TblBusinessApplication>(TblBusinessApplication.class);
        util.exportExcel(response, list, "业务应用系统数据");
    }

    /**
     * 获取业务应用系统详细信息
     */
   /* @PreAuthorize("@ss.hasPermi('safe:application:query')")
    @GetMapping(value = "/{assetId}")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId)
    {
        return AjaxResult.success(tblBusinessApplicationService.selectTblBusinessApplicationByAssetId(assetId));
    }*/

    /**
     * 新增业务应用系统
     */
    @PreAuthorize("@ss.hasPermi('safe:application:add')")
    @Log(title = "业务应用系统", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblBusinessApplication tblBusinessApplication)
    {
        return toAjax(tblBusinessApplicationService.insertTblBusinessApplication(tblBusinessApplication));
    }

    @PreAuthorize("@ss.hasPermi('safe:application:add')")
    @Log(title = "业务应用提交审核", businessType = BusinessType.UPDATE)
    @PostMapping("/apply")
    public AjaxResult apply(@RequestBody TblBusinessApplication business)
    {
        TblBusinessApplication app = tblBusinessApplicationService.selectTblBusinessApplicationByAssetId(business.getAssetId());
        String checkOn = app.getCheckOn();
        if (checkOn==null || checkOn.equals("new")) {
            TblBusinessApplication application = new TblBusinessApplication();
            application.setAssetId(business.getAssetId());
            application.setAssetName(app.getAssetName());
            application.setRemark(business.getRemark());
            application.setCreateBy(getNickName());
            return toAjax(tblBusinessApplicationService.applyApplication(application));
        }else{
            return AjaxResult.error("请不要重复提交审核！");
        }
    }

    @PreAuthorize("@ss.hasPermi('safe:application:check')")
    @Log(title = "审核业务应用系统", businessType = BusinessType.UPDATE)
    @PostMapping("/check")
    public AjaxResult check(@RequestBody TblApplicationHistory tblApplicationHistory)
    {
        tblApplicationHistory.setCreateBy(getNickName());
        return toAjax(tblBusinessApplicationService.checkApplication(tblApplicationHistory));
    }
    /**
     * 新增业务基本信息
     */
    @PreAuthorize("@ss.hasPermi('safe:application:add')")
    @Log(title = "业务应用系统", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping("/businessInfo")
    public AjaxResult addBusinessInfo(@RequestBody TblBusinessApplication tblBusinessApplication) {
            Long[] moduleIds = tblBusinessApplication.getDelList();
            if (CollectionUtils.isNotEmpty(moduleIds)) {
                tblMapperService.deleteTblMapperBymoduleIds(moduleIds);
            }
            List<TblMapper> tblMapperList = tblBusinessApplication.getTblMapperList();
            int rows = 0;
            if (ObjectUtils.isEmpty(tblBusinessApplication.getAssetId())) {
                rows += tblBusinessApplicationService.insertTblBusinessApplication(tblBusinessApplication);
            } else {
                rows += tblBusinessApplicationService.updateTblBusinessApplicationInfo(tblBusinessApplication);
            }
            if (CollectionUtils.isNotEmpty(tblMapperList)) {
                for (TblMapper tblMapper : tblMapperList) {
                    if (tblMapper.getModuleId() == null) {
                        tblMapper.setModuleId(snowflake.nextId());
                        tblMapper.setApplicationId(tblBusinessApplication.getAssetId());
                        tblMapperService.insertTblMapper(tblMapper);
                    } else {
                        tblMapperService.updateTblMapper(tblMapper);
                    }
                }
            }
        return toAjax(rows);
    }

    /**
     * 修改业务应用系统
     */
    @PreAuthorize("@ss.hasPermi('safe:application:edit')")
    @Log(title = "业务应用系统", businessType = BusinessType.UPDATE)
    @AssetDataHandle(action = AssetAction.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblBusinessApplication tblBusinessApplication)
    {
        return toAjax(tblBusinessApplicationService.updateTblBusinessApplication(tblBusinessApplication));
    }

    /**
     * 删除业务应用系统
     */
    @PreAuthorize("@ss.hasPermi('safe:application:remove')")
    @Log(title = "业务应用系统", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetIds}")
    public AjaxResult remove(@PathVariable Long[] assetIds)
    {
        return toAjax(tblBusinessApplicationService.deleteTblBusinessApplicationByAssetIds(assetIds));
    }

    /**
     * 新增业务应用系统返回资产ID
     */
    @PreAuthorize("@ss.hasPermi('safe:application:add')")
    @Log(title = "业务应用系统", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping("/add/id")
    public AjaxResult addReturnId(@RequestBody TblBusinessApplication tblBusinessApplication)
    {
        return AjaxResult.success(tblBusinessApplicationService.addTblBusinessApplication(tblBusinessApplication));
    }

    /**
     * 根据域名搜索应用
     */
    //@PreAuthorize("@ss.hasAnyPermi('safe:domain:list')")
    @GetMapping("domain/{domainId}")
    public TableDataInfo listByDomain(@PathVariable Long domainId){
        return getDataTable(tblBusinessApplicationService.selectBusinessApplicationByDomainId(domainId));
    }
    /**
     * 根据域名添加应用
     */
    //@PreAuthorize("@ss.hasAnyPermi('safe:domain:list')")
    @PostMapping("domain/addApplication")
    public AjaxResult addByDomain(@RequestBody List<TblDomainAsset> tblDomainAssets){
        for (TblDomainAsset tblDomainAsset : tblDomainAssets) {
//            删除原有的
            tblBusinessApplicationService.deleteBusinessApplicationDomainRel(tblDomainAsset);
        }
        return toAjax(tblBusinessApplicationService.addBusinessApplicationDomainRel(tblDomainAssets));
    }


    /**
     * 根据 应用 与 服务器 获取功能
     */
    @GetMapping("/getFunctionByAssetIdServerId/{assetId}/{serverId}")
    public TableDataInfo getFunctionByAssetIdServerId(@PathVariable("assetId")Long assetId,@PathVariable("serverId") Long serverId){
       return getDataTable(tblBusinessApplicationService.getFunctionByAssetIdServerId(assetId,serverId));
    } /**
     * 根据 删除关联
     */
    @DeleteMapping("/delApplicationServerFunctionRel/{assetId}")
    public AjaxResult delApplicationServerFunctionRel(@PathVariable("assetId")List<Long> assetId){
       return toAjax(tblBusinessApplicationService.delApplicationServerFunctionRel(assetId));
    }
    /**
     * 根据 添加关联
     */
    @PostMapping("/addApplicationServerFunctionRel")
    public AjaxResult addApplicationServerFunctionRel(@RequestBody TblDeploy appFunctionRel){
        return toAjax(tblBusinessApplicationService.addApplicationServerFunctionRel(appFunctionRel));
    }
    /**
     * 根据 添加关联
     */
    @PostMapping("/addApplicationServerFunctionRels")
    public AjaxResult addApplicationServerFunctionRels(@RequestBody List<TblDeploy> appFunctionRels){
        int row = 0;
        if(ObjectUtils.isNotEmpty(appFunctionRels)){
            for (TblDeploy appFunctionRel : appFunctionRels) {
                row+= tblBusinessApplicationService.addApplicationServerFunctionRel(appFunctionRel);
            }
        }
        return toAjax(row);
    }
    @PostMapping("/saveApplicationHardwareEVs")
    public AjaxResult saveApplicationHardwareEVs(@RequestBody HardWareEV hardWareEV){
        TblDeploy deploy = new TblDeploy();
        if(ObjectUtils.isNotEmpty(hardWareEV.getServerEV())){
            if(ObjectUtils.isNotEmpty(hardWareEV.getServerEV().getList())){
                tblApplicationServerController.adds(hardWareEV.getServerEV().getList());
            }
            if(ObjectUtils.isNotEmpty(hardWareEV.getServerEV().getDelList())){
                for (Long aLong : hardWareEV.getServerEV().getDelList()) {//删除部署关系
                    TblApplicationServer tblApplicationServer = tblApplicationServerService.selectTblApplicationServerById(aLong);
                    deploy.setApplicationId(tblApplicationServer.getAssetId());
                    deploy.setAssetId(tblApplicationServer.getServerId());
                    tblDeployService.deleteTblDeployByDeploy(deploy);
                }
                tblApplicationServerController.remove(hardWareEV.getServerEV().getDelList().toArray(new Long[0]));
            }
        }
        if(ObjectUtils.isNotEmpty(hardWareEV.getDateEV())){
            if(ObjectUtils.isNotEmpty(hardWareEV.getDateEV().getList()))
                tblApplicationServerController.adds(hardWareEV.getDateEV().getList());
            if(ObjectUtils.isNotEmpty(hardWareEV.getDateEV().getDelList())){
                for (Long aLong : hardWareEV.getDateEV().getDelList()) {//删除部署关系
                    TblApplicationServer tblApplicationServer = tblApplicationServerService.selectTblApplicationServerById(aLong);
                    deploy.setApplicationId(tblApplicationServer.getAssetId());
                    deploy.setAssetId(tblApplicationServer.getServerId());
                    tblDeployService.deleteTblDeployByDeploy(deploy);
                }
                tblApplicationServerController.remove(hardWareEV.getDateEV().getDelList().toArray(new Long[0]));
            }
        }if(ObjectUtils.isNotEmpty(hardWareEV.getNetworkEV())){
            if(ObjectUtils.isNotEmpty(hardWareEV.getNetworkEV().getList()))
                tblApplicationNetwareController.adds(hardWareEV.getNetworkEV().getList());
            if(ObjectUtils.isNotEmpty(hardWareEV.getNetworkEV().getDelList()))
                tblApplicationNetwareController.remove(hardWareEV.getNetworkEV().getDelList().toArray(new Long[0]));
        }if(ObjectUtils.isNotEmpty(hardWareEV.getSafeEV())){
            if(ObjectUtils.isNotEmpty(hardWareEV.getSafeEV().getList()))
                tblApplicationSafeController.adds(hardWareEV.getSafeEV().getList());
            if(ObjectUtils.isNotEmpty(hardWareEV.getSafeEV().getDelList()))
                tblApplicationSafeController.remove(hardWareEV.getSafeEV().getDelList().toArray(new Long[0]));
        }

        return AjaxResult.success();

    }

    /**
     * 粘贴 服务器组件
     * <AUTHOR>
     * @Description //
     * @Date 2023/9/18 9:29
     * @param copyAssetId
     * @param pasteAssetId
     * @return java.lang.String
     **/
    @GetMapping("/pasteServerComponents/{copyAssetId}/{pasteAssetId}/{applicationId}")
    public AjaxResult pasteServerComponents(@PathVariable("copyAssetId") Long copyAssetId, @PathVariable("pasteAssetId") Long pasteAssetId,
                                            @PathVariable("applicationId") Long applicationId) {
        tblBusinessApplicationService.pasteServerComponents(copyAssetId, pasteAssetId, applicationId);
        return AjaxResult.success();
    }
    @GetMapping("/nameList")
    public AjaxResult getApplicationNameList(TblBusinessApplication tblBusinessApplication){
        List<TblBusinessApplication> list = tblBusinessApplicationMapper.getApplicationNameList(tblBusinessApplication);
        return AjaxResult.success(list);
    }

    @GetMapping("/getApplicationList")
    public TableDataInfo getApplicationList(TblBusinessApplication tblBusinessApplication){
        startPage();
        List<TblBusinessApplication> list = tblBusinessApplicationMapper.getApplicationList(tblBusinessApplication);
        return getDataTable(list);
    }

    /**
     * 根据条件查询应用系统
     * @param tblBusinessApplicationDto
     * @return
     */
    @GetMapping("/getApplicationListByCondition")
    public AjaxResult getApplicationListByCondition(TblBusinessApplicationDto tblBusinessApplicationDto){
        if(tblBusinessApplicationDto.getEventType() != null && tblBusinessApplicationDto.getEventType() == 2){
            String ipv4 = tblBusinessApplicationDto.getIpv4();
            if(StrUtil.isNotBlank(ipv4)){
                ipv4 = StrUtil.subAfter(tblBusinessApplicationDto.getIpv4(),"//",false);
                ipv4 = StrUtil.subBefore(ipv4,"/",false);
            }
            tblBusinessApplicationDto.setIpv4(ipv4);
        }
        List<TblBusinessApplication> list = tblBusinessApplicationService.getApplicationListByCondition(tblBusinessApplicationDto);
        return AjaxResult.success(list);
    }

    @GetMapping("/getDetails/{assetId}")
    public AjaxResult getDetails(@PathVariable String assetId){
        JSONObject data = tblBusinessApplicationService.getDetails(assetId);
        return AjaxResult.success(data);
    }

    @GetMapping("/getDepts")
    public AjaxResult getDepts(){
        QueryDeptApplicationCountDto queryCountDto = new QueryDeptApplicationCountDto();
        SysDept sysDept = new SysDept();
        // 资产录入员只能看到自己创建的业务应用
        Long userId = getUserId();
        /*Long deptId = getDeptId();
        if (deptId != 100) {
            sysDept.setDeptId(deptId);
        }*/
        sysDept.setDeptId(getDeptId());
        queryCountDto.setSysDept(sysDept);
        /*List<Long> roles = sysRoleService.selectRoleListByUserId(userId);
        if (roles.stream().anyMatch(role -> role == 103)) {
            Map<String, Object> params = queryCountDto.getParams();
            params.put("dataScope", StringUtils.format(" AND {}.user_id = {} ", 'a', userId));
            params.put("userId",userId);
        }*/
        return AjaxResult.success(tblBusinessApplicationService.getDepts(queryCountDto));
    }

    public static boolean isValidDomainName(String domainName) {
        return domainName.matches(DOMAIN_NAME_REGEX);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TblBusinessApplicationTemlate> util = new ExcelUtil<TblBusinessApplicationTemlate>(TblBusinessApplicationTemlate.class);
        List<TblBusinessApplicationTemlate> list = new ArrayList<>();
        TblBusinessApplicationTemlate temlate = new TblBusinessApplicationTemlate();
        temlate.setManager("例如：李/***********,多个责任人以英文的‘,’进行分割");
        temlate.setUrl("例如：http://***********:8080/");
        temlate.setIp("例如：连续IP地址段格式为“***********-100”，多个IP以英文的‘,’进行分割");
        temlate.setIsOpenNetwork("例如：是/否");
        temlate.setHwIsTrueShutDown("例如：是/否");
        list.add(temlate);
        util.exportExcel(response,list,"业务系统模板");
    }

    @PostMapping("/importTemplateToJiangTong")
    public void importTemplateToJiangTong(HttpServletResponse response)
    {
        ExcelUtil<TblBusinessApplicationTemlateToJiangTong> util = new ExcelUtil<TblBusinessApplicationTemlateToJiangTong>(TblBusinessApplicationTemlateToJiangTong.class);
        List<TblBusinessApplicationTemlateToJiangTong> list = new ArrayList<>();
        TblBusinessApplicationTemlateToJiangTong temlate = new TblBusinessApplicationTemlateToJiangTong();
        temlate.setAssetName("例如：XXX名称");
        temlate.setSysBusinessState("例如：xxx业务描述");
        temlate.setUrl("例如：http://www.xxx.com");
        temlate.setDomainUrl("例如：http://www.xxx.com");
        temlate.setIsOpenNetwork("是/否");
        temlate.setServerIpStr("例如：************,多个ip以英文”,“分割");
        temlate.setDomainName("例如：xxx办公网");
        temlate.setProtectGrade("一级/二级/三级/四级/未做等保");
        temlate.setConstruct("例如：部建");
        temlate.setVendorName("例如：XXX合作企业名称,多个合作企业以英文的‘,’进行分割");
        temlate.setUodTime("例如：2020-01-01");
        temlate.setAccessibleNetwork("例如：办公网/内网/外网");
        temlate.setDeptName("例如：XXX部门名称");
        temlate.setManager("例如：李/***********,多个责任人以英文的‘,’进行分割");
        temlate.setDevelopmentLanguage("例如：java");
        temlate.setTechnical("B/S架构/BS架构");
        temlate.setSystemType("例如：web应用");
        temlate.setVersionNumber("例如：1.1.1");
        temlate.setProtectionDescription("例如：XXX防护描述");
        temlate.setHwIsTrueShutDown("是/否");
        temlate.setRemark("例如：XXX业务描述");
        list.add(temlate);
        util.exportExcel(response,list,"业务系统模板");
    }

    private void checkField(String value, String fieldName,int index) throws Exception {
        if (StrUtil.isBlank(value)) {
            throw new Exception("导入失败:第"+index+"行,"+ fieldName + "不能为空");
        }
    }

    @PostMapping("/importData")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TblBusinessApplicationTemlate> util = new ExcelUtil<TblBusinessApplicationTemlate>(TblBusinessApplicationTemlate.class);
        List<TblBusinessApplicationTemlate> list = util.importExcel(file.getInputStream());
        if (CollUtil.isEmpty(list)){
            return AjaxResult.error("导入数据不能为空！");
        }
        try {
            SysDept queryDept = new SysDept();
            List<SysDept> queryDeptList = deptService.selectDeptList(queryDept);
            SysDictData dictData = new SysDictData();
            dictData.setDictType("system_type");
            List<SysDictData> sysDictData = dictDataService.selectDictDataList(dictData);
            List<NetworkDomain> networkDomains = networkDomainService.selectNetworkDomainList(new NetworkDomain());
            List<SysUser> sysUsers = sysUserService.selectUserList(new SysUser());
            List<TblBusinessApplication> businessApplicationList = new ArrayList<>();
            int index = 1;
            for (TblBusinessApplicationTemlate tblBusinessApplicationTemlate : list) {
                checkField(tblBusinessApplicationTemlate.getDeptName(), "部门", index);
                checkField(tblBusinessApplicationTemlate.getAssetName(), "系统名称", index);
                checkField(tblBusinessApplicationTemlate.getUrl(), "登录地址", index);
                checkField(tblBusinessApplicationTemlate.getIp(), "IP地址", index);
                checkField(tblBusinessApplicationTemlate.getManager(), "责任人", index);
                TblBusinessApplication tblBusinessApplication = new TblBusinessApplication();
                tblBusinessApplication.setIpd(tblBusinessApplicationTemlate.getIp());
                BeanUtil.copyPropertiesIgnoreNull(tblBusinessApplicationTemlate, tblBusinessApplication);
                //校验域名
                if (StrUtil.isNotBlank(tblBusinessApplication.getDomainUrl())){
                    boolean validDomainName = isValidDomainName(tblBusinessApplication.getDomainUrl());
                    if (!validDomainName){
                        throw new Exception("导入失败:第"+index+"行"+tblBusinessApplication.getDomainUrl()+"域名不合法");
                    }
                }
                if (CollUtil.isNotEmpty(queryDeptList)){
                    List<SysDept> collect = queryDeptList.stream().filter(dept -> dept.getDeptName()
                            .equals(tblBusinessApplication.getDeptName())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)){
                        tblBusinessApplication.setDeptId(collect.get(0).getDeptId());
                    }else {
                        throw new Exception("导入失败:第"+index +"行"+tblBusinessApplication.getDeptName()+"部门不存在");
                    }
                }
                if (CollUtil.isNotEmpty(sysDictData) && StringUtils.isNotEmpty(tblBusinessApplicationTemlate.getSystemType())){
                    List<SysDictData> collect = sysDictData.stream().filter(dept -> dept.getDictLabel()
                            .equals(tblBusinessApplicationTemlate.getSystemType())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)){
                        tblBusinessApplication.setSystemType(Long.valueOf(collect.get(0).getDictValue()));
                    }else {
                        throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getSystemType()+"系统类型不存在");
                    }
                }
                if (CollUtil.isNotEmpty(networkDomains) && StringUtils.isNotEmpty(tblBusinessApplicationTemlate.getDomainName())){
                    if (networkDomains.stream().anyMatch(domain -> domain.getDomainName().equals(tblBusinessApplicationTemlate.getDomainName()))) {
                        int finalIndex = index;
                        NetworkDomain networkDomain = networkDomains.stream()
                                .filter(domain -> domain.getDomainName().equals(tblBusinessApplicationTemlate.getDomainName()))
                                .findFirst()
                                .orElseThrow(() -> new NoSuchElementException("导入失败:第"+ finalIndex +"行"+tblBusinessApplicationTemlate.getDomainName()+"网络区域不存在"));
                        tblBusinessApplication.setDomainId(networkDomain.getDomainId());
                    }else {
                        throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getDomainName()+"网络区域不存在");
                    }
                }
                if (CollUtil.isNotEmpty(sysUsers)){
                    String manager = "";
                    //截取责任人
                    String[] split = tblBusinessApplicationTemlate.getManager().split(",");
                    for (int i = 0; i < split.length; i++) {
                        String[] strings = split[i].split("/");
                        if (strings.length < 2){
                            throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getManager()+"责任人格式错误");
                        }else {
                            List<SysUser> collect = sysUsers.stream().filter(dept -> dept.getNickName()
                                    .equals(strings[0]) && dept.getPhonenumber().equals(strings[1])).collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(collect)){
                                if (manager.isEmpty()){
                                    manager = collect.get(0).getUserId().toString();
                                }else {
                                    manager = manager + "," + collect.get(0).getUserId();
                                }
                            }else {
                                throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getManager()+"责任人不存在");
                            }
                        }
                    }
                    tblBusinessApplication.setManager(manager);
                }
                if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getHwIsTrueShutDown()) && tblBusinessApplicationTemlate.getHwIsTrueShutDown().equals("是")){
                    tblBusinessApplication.setHwIsTrueShutDown("1");
                }else if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getHwIsTrueShutDown()) && tblBusinessApplicationTemlate.getHwIsTrueShutDown().equals("否")){
                    tblBusinessApplication.setHwIsTrueShutDown("0");
                }else {
                    throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getHwIsTrueShutDown()+"HW时期是否可关停格式错误");
                }
                if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getIsOpenNetwork()) && tblBusinessApplicationTemlate.getIsOpenNetwork().equals("是")){
                    tblBusinessApplication.setIsOpenNetwork("1");
                } else if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getIsOpenNetwork()) && tblBusinessApplicationTemlate.getIsOpenNetwork().equals("否")) {
                    tblBusinessApplication.setIsOpenNetwork("0");
                }else {
                    throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getIsOpenNetwork()+"是否开放网络格式错误");
                }
                businessApplicationList.add(tblBusinessApplication);
                index++;
            }
            businessApplicationList.forEach(tblBusinessApplication -> {
                tblBusinessApplication.setAssetId(snowflake.nextId());
                tblBusinessApplication.setCreateBy(getUsername());
                if (tblBusinessApplication.getUserId() == null) {
                    tblBusinessApplication.setUserId(getUserId());
                }
                if (tblBusinessApplication.getDeptId() == null) {
                    tblBusinessApplication.setDeptId(getDeptId());
                }
                tblBusinessApplicationService.insertTblBusinessApplication(tblBusinessApplication);
            });
        } catch(Exception e) {
            logger.error("导入数据出错:" + e.getMessage());
            throw new ServiceException("数据导入错误:" + e.getMessage());
        }
        return AjaxResult.success("导入成功");
    }

    @Autowired
    private ITblServerService tblServerService;
    @Autowired
    private ITblVendorService tblVendorService;
    @PostMapping("/importDataToJiangTong")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult importDataToJiangTong(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TblBusinessApplicationTemlateToJiangTong> util = new ExcelUtil<TblBusinessApplicationTemlateToJiangTong>(TblBusinessApplicationTemlateToJiangTong.class);
        List<TblBusinessApplicationTemlateToJiangTong> list = util.importExcel(file.getInputStream());
        if (CollUtil.isEmpty(list)){
            return AjaxResult.error("导入数据不能为空！");
        }
        try {
            SysDept queryDept = new SysDept();
            List<SysDept> queryDeptList = deptService.selectDeptList(queryDept);
            SysDictData dictData = new SysDictData();
            dictData.setDictType("system_type");
            List<SysDictData> sysDictData = dictDataService.selectDictDataList(dictData);
            dictData.setDictType("construct_type");
            List<SysDictData> constructType = dictDataService.selectDictDataList(dictData);
            dictData.setDictType("app_technical");
            List<SysDictData> appTechnicalDictData = dictDataService.selectDictDataList(dictData);
            List<NetworkDomain> networkDomains = networkDomainService.selectNetworkDomainList(new NetworkDomain());
            List<SysUser> sysUsers = sysUserService.selectUserList(new SysUser());
            List<TblVendor> vendorList = tblVendorService.selectTblVendorList(new TblVendor());
            List<TblServer> serverList = tblServerService.selectTblServerList2(new TblServer());
            int index = 1;
            for (TblBusinessApplicationTemlateToJiangTong tblBusinessApplicationTemlate : list) {
                TblBusinessApplication tblBusinessApplication = new TblBusinessApplication();
                BeanUtil.copyPropertiesIgnoreNull(tblBusinessApplicationTemlate, tblBusinessApplication);
                if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getUodTime())){
                    tblBusinessApplication.setUodTime(DateUtil.parse(tblBusinessApplicationTemlate.getUodTime()));
                }
                if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getConstruct())){
                    if (CollUtil.isNotEmpty(constructType)){
                        SysDictData oredElse = constructType.stream().filter(construct -> construct.getDictLabel().equals(tblBusinessApplicationTemlate.getConstruct())).findFirst().orElse(null);
                        if (oredElse != null) {
                            tblBusinessApplication.setConstruct(oredElse.getDictValue());
                        } else {
                            throw new Exception("导入失败:第" + index + "行" + tblBusinessApplication.getConstruct() + "建设模式不存在");
                        }
                    }else {
                        throw new Exception("导入失败:没有查到建设方式字典");
                    }
                }
                //合作单位
                if(StrUtil.isNotBlank(tblBusinessApplication.getVendorName())){
                    String[] split = tblBusinessApplication.getVendorName().split(",");
                    List<String> vendors = new ArrayList<>();
                    if (CollUtil.isNotEmpty(vendorList)){
                        for (String vendorName : split) {
                            TblVendor tblVendor = vendorList.stream().filter(vendor -> StrUtil.isNotBlank(vendor.getVendorName()) && vendor.getVendorName().equals(vendorName)).findFirst().orElse(null);
                            if (tblVendor != null) {
                                vendors.add(tblVendor.getId().toString());
                            } else {
                                throw new Exception("导入失败:第" + index + "行" + tblBusinessApplication.getVendorName() + "合作单位不存在");
                            }
                        }
                        //将vendors转换为以逗号分隔的字符串
                        tblBusinessApplication.setVendors(String.join(",", vendors));
                    }else {
                        throw new Exception("导入失败:暂无合作单位");
                    }
                }
                //校验域名
                if (StrUtil.isNotBlank(tblBusinessApplication.getDomainUrl())){
                    boolean validDomainName = isValidDomainName(tblBusinessApplication.getDomainUrl());
                    if (!validDomainName){
                        throw new Exception("导入失败:第"+index+"行"+tblBusinessApplication.getDomainUrl()+"域名不合法");
                    }
                }
                if (CollUtil.isNotEmpty(queryDeptList)){
                    List<SysDept> collect = queryDeptList.stream().filter(dept -> dept.getDeptName()
                            .equals(tblBusinessApplication.getDeptName())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)){
                        tblBusinessApplication.setDeptId(collect.get(0).getDeptId());
                    }else {
                        throw new Exception("导入失败:第"+index +"行"+tblBusinessApplication.getDeptName()+"部门不存在");
                    }
                }
                if (CollUtil.isNotEmpty(sysDictData) && StringUtils.isNotEmpty(tblBusinessApplicationTemlate.getSystemType())){
                    List<SysDictData> collect = sysDictData.stream().filter(dept -> dept.getDictLabel()
                            .equals(tblBusinessApplicationTemlate.getSystemType())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)){
                        tblBusinessApplication.setSystemType(Long.valueOf(collect.get(0).getDictValue()));
                    }else {
                        throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getSystemType()+"系统类型不存在");
                    }
                }
                if (CollUtil.isNotEmpty(appTechnicalDictData) && StringUtils.isNotEmpty(tblBusinessApplicationTemlate.getTechnical())){
                    List<SysDictData> collect = appTechnicalDictData.stream().filter(dept -> dept.getDictLabel()
                            .equals(tblBusinessApplicationTemlate.getTechnical())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)){
                        tblBusinessApplication.setTechnical(collect.get(0).getDictValue());
                    }else {
                        throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getTechnical()+"技术架构不存在");
                    }
                }
                if (CollUtil.isNotEmpty(networkDomains) && StringUtils.isNotEmpty(tblBusinessApplicationTemlate.getDomainName())){
                    if (networkDomains.stream().anyMatch(domain -> domain.getDomainName().equals(tblBusinessApplicationTemlate.getDomainName()))) {
                        int finalIndex = index;
                        NetworkDomain networkDomain = networkDomains.stream()
                                .filter(domain -> domain.getDomainName().equals(tblBusinessApplicationTemlate.getDomainName()))
                                .findFirst()
                                .orElseThrow(() -> new NoSuchElementException("导入失败:第"+ finalIndex +"行"+tblBusinessApplicationTemlate.getDomainName()+"网络区域不存在"));
                        tblBusinessApplication.setDomainId(networkDomain.getDomainId());
                    }else {
                        throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getDomainName()+"网络区域不存在");
                    }
                }
                if (CollUtil.isNotEmpty(sysUsers)){
                    String manager = "";
                    if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getManager())){
                        String[] split = tblBusinessApplicationTemlate.getManager().split(",");
                        for (int i = 0; i < split.length; i++) {
                            String[] strings = split[i].split("/");
                            if (strings.length < 2){
                                throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getManager()+"责任人格式错误");
                            }else {
                                List<SysUser> collect = sysUsers.stream().filter(dept -> dept.getNickName()
                                        .equals(strings[0]) && dept.getPhonenumber().equals(strings[1])).collect(Collectors.toList());
                                if (CollUtil.isNotEmpty(collect)){
                                    if (manager.isEmpty()){
                                        manager = collect.get(0).getUserId().toString();
                                    }else {
                                        manager = manager + "," + collect.get(0).getUserId();
                                    }
                                }else {
                                    throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getManager()+"责任人不存在");
                                }
                            }
                        }
                        tblBusinessApplication.setManager(manager);
                    }
                }
                if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getHwIsTrueShutDown()) && tblBusinessApplicationTemlate.getHwIsTrueShutDown().equals("是")){
                    tblBusinessApplication.setHwIsTrueShutDown("1");
                }else if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getHwIsTrueShutDown()) && tblBusinessApplicationTemlate.getHwIsTrueShutDown().equals("否")){
                    tblBusinessApplication.setHwIsTrueShutDown("0");
                }else {
                    throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getHwIsTrueShutDown()+"HW时期是否可关停格式错误");
                }
                if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getIsOpenNetwork()) && tblBusinessApplicationTemlate.getIsOpenNetwork().equals("是")){
                    tblBusinessApplication.setIsOpenNetwork("1");
                } else if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getIsOpenNetwork()) && tblBusinessApplicationTemlate.getIsOpenNetwork().equals("否")) {
                    tblBusinessApplication.setIsOpenNetwork("0");
                }else {
                    throw new Exception("导入失败:第"+index +"行"+tblBusinessApplicationTemlate.getIsOpenNetwork()+"是否开放网络格式错误");
                }
                if (StrUtil.isNotBlank(tblBusinessApplicationTemlate.getServerIpStr())){
                    //截取服务器ip
                    String[] split = tblBusinessApplicationTemlate.getServerIpStr().split(",");
                    List<Long> serverIds = new ArrayList<>();
                    List<TblApplicationServer> serverListInsert = new ArrayList<>();
                    for (int i = 0; i < split.length; i++) {
                        String string = split[i];
                        if (CollUtil.isNotEmpty(serverList)){
                            List<TblServer> collect = serverList.stream().filter(server ->StrUtil.isNotBlank(server.getIp()) && server.getIp().equals(string)).collect(Collectors.toList());
                            if (CollUtil.isEmpty(collect)){
                                throw new Exception("导入失败:第"+index +"行"+string+"服务器ip不存在");
                            }else  {
                                serverIds.add(collect.get(0).getAssetId());
                                TblApplicationServer tblApplicationServer = new TblApplicationServer();
                                tblApplicationServer.setServerId(collect.get(0).getAssetId());
                                tblApplicationServer.setType("0");
                                serverListInsert.add(tblApplicationServer);
                            }
                        }
                    }
                    tblBusinessApplication.setAssociationServer(serverIds);
                    tblBusinessApplication.setServerIps(CollUtil.toList(split));
                    tblBusinessApplication.setApplicationServers(serverListInsert);
                }
                tblBusinessApplication.setAssetId(snowflake.nextId());
                tblBusinessApplication.setCreateBy(getUsername());
                if (tblBusinessApplication.getUserId() == null) {
                    tblBusinessApplication.setUserId(getUserId());
                }
                if (tblBusinessApplication.getDeptId() == null) {
                    tblBusinessApplication.setDeptId(getDeptId());
                }
                tblBusinessApplicationService.insertTblBusinessApplication(tblBusinessApplication);
                if (CollUtil.isNotEmpty(tblBusinessApplication.getApplicationServers())){
                    Long assetId = tblBusinessApplication.getAssetId();
                    tblBusinessApplication.getApplicationServers().forEach(tblApplicationServer -> {
                        tblApplicationServer.setId(snowflake.nextId());
                        tblApplicationServer.setAssetId(assetId);
                        tblApplicationServerService.insertTblApplicationServer(tblApplicationServer);
                    });
                }
                index++;
            }
            /*businessApplicationList.forEach(tblBusinessApplication -> {
                tblBusinessApplication.setAssetId(snowflake.nextId());
                tblBusinessApplication.setCreateBy(getUsername());
                if (tblBusinessApplication.getUserId() == null) {
                    tblBusinessApplication.setUserId(getUserId());
                }
                if (tblBusinessApplication.getDeptId() == null) {
                    tblBusinessApplication.setDeptId(getDeptId());
                }
                tblBusinessApplicationService.insertTblBusinessApplication(tblBusinessApplication);
                if (CollUtil.isNotEmpty(tblBusinessApplication.getApplicationServers())){
                    Long assetId = tblBusinessApplication.getAssetId();
                    tblBusinessApplication.getApplicationServers().forEach(tblApplicationServer -> {
                        tblApplicationServer.setId(snowflake.nextId());
                        tblApplicationServer.setAssetId(assetId);
                        tblApplicationServerService.insertTblApplicationServer(tblApplicationServer);
                    });
                }
            });*/
        } catch(Exception e) {
            logger.error("导入数据出错:" + e.getMessage());
            throw new ServiceException("数据导入错误:" + e.getMessage());
        }
        return AjaxResult.success("导入成功");
    }

    /**
     * 查询业务应用系统所有数据
     */
    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @GetMapping("/selectAll")
    public AjaxResult selectAll(TblBusinessApplication tblBusinessApplication)
    {
        List<TblBusinessApplication> list = tblBusinessApplicationService.selectTblBusinessApplicationList(tblBusinessApplication);
        return AjaxResult.success(list);
    }

    /**
     * 根据不同的字典类型统计业务系统数量
     * @param dictType
     * @return
     */
    @GetMapping("/getAppCountByDict")
    public AjaxResult getAppCountByDict(String dictType) {
        CountDictTypeVO countByDict = countByDictService.getCountByDict(dictType,"application");
        return AjaxResult.success(countByDict);
    }


    /**
     * 部门+业务系统树
     * @return
     */
    @GetMapping("/deptApplicationTree")
    public AjaxResult deptApplicationTree(){
        return AjaxResult.success(tblBusinessApplicationService.deptApplicationTree());
    }

    /**
     * 获取等保系统数量占比
     * @return
     */
    @GetMapping("/getProtectGradeCountRatio")
    public AjaxResult getProtectGradeCountRatio(TblBusinessApplication tblBusinessApplication){
        return AjaxResult.success(tblBusinessApplicationService.getProtectGradeCountRatio(tblBusinessApplication));
    }

    @GetMapping("/getAuditConfig")
    public TableDataInfo getAuditConfig(){
        startPage();
        SysConfig config = new SysConfig();
        config.setConfigKey("sys.application.isAudit");
        List<SysConfig> list = configService.selectConfigList(config);
        return getDataTable(list);
    }

    @GetMapping(value = "/{assetId}")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId)
    {
        return AjaxResult.success(tblBusinessApplicationService.getApplicationComplete(assetId));
    }

}
