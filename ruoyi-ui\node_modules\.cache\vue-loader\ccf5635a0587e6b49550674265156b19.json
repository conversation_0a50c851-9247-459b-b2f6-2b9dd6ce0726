{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasks.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasks.vue", "mtime": 1755743179262}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RTY2FudGFza1N1bW1hcnksIHRhc2tDcmVhdGVyZXBvcnQsIHRhc2tEb3duUmVwb3J0IH0gZnJvbSAnLi4vLi4vLi4vYXBpL21vbml0b3IyL3dwcmVzdWx0JwppbXBvcnQgRmZKb2JUYXNrc0RldGFpbCBmcm9tICcuL2ZmSm9iVGFza3NEZXRhaWwnCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ2ZmSm9iVGFza3MnLAogIGNvbXBvbmVudHM6IHsgRmZKb2JUYXNrc0RldGFpbCB9LAogIHByb3BzOiB7CiAgICBqb2JSb3c6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiB7fQogICAgfSwKICAgIGpvYklkOiB7CiAgICAgIHR5cGU6IFtTdHJpbmcsTnVtYmVyXSwKICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICB9LAogICAgam9iVHlwZTp7CiAgICAgIHR5cGU6TnVtYmVyLAogICAgICBkZWZhdWx0OnVuZGVmaW5lZCwKICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvL+S4i+aLieahhuaJgOmAieS4reeahOaJq+aPj+WunuS+i2lkCiAgICAgIGV4cGxvcmVKb2JJZDogJycsCiAgICAgIC8v5Lu75Yqh6K+m57uG5L+h5oGvCiAgICAgIGpvYkRldGFpbDp7fSwKICAgICAgLy/ku7vliqHliJfooagKICAgICAgam9iVGFza0xpc3Q6IFtdLAogICAgICBxdWVyeVBhcmFtczp7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgIH0sCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgZGV0YWlsRGlhbG9nOiBmYWxzZSwKICAgICAgcHJvY2Vzc1N0YXR1czogdW5kZWZpbmVkLAogICAgICBwcm9jZXNzSWQ6ICcnLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgZXhwbG9yZURpc2FibGVkOiBmYWxzZSwKICAgICAgdGFza1JvdzogW10sCiAgICAgIHRpbWVyOiBudWxsLAogICAgICByZXBvcnRUeXBlOiAnJwogICAgICAvLyBqb2JUeXBlOiAndGFuSHVvJwogICAgfQogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdGhpcy50aW1lciA9IHNldEludGVydmFsKCgpID0+IHsKICAgICAgX3RoaXMuZ2V0TGlzdCgpCiAgICB9LCAxMDAwMCk7CiAgfSwKICBiZWZvcmVEZXN0cm95KCkgewogICAgLy8g5riF6Zmk5a6a5pe25ZmoCiAgICBpZiAodGhpcy50aW1lcikgewogICAgICBjbGVhckludGVydmFsKHRoaXMudGltZXIpOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy50YXNrVHlwZSA9IHRoaXMuam9iVHlwZQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmpvYklkID0gdGhpcy5qb2JJZAogICAgICBsaXN0U2NhbnRhc2tTdW1tYXJ5KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuam9iVGFza0xpc3QgPSByZXNwb25zZS5yb3dzCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGNyZWF0ZVJlcG9ydChyb3cpIHsKICAgICAgdGFza0NyZWF0ZXJlcG9ydChyb3cpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICAgIH0pCiAgICB9LAogICAgZG93blJlcG9ydChyb3cpIHsKICAgICAgdGFza0Rvd25SZXBvcnQocm93KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICB3aW5kb3cub3BlbihyZXNwb25zZS5tc2csICJfYmxhbmsiKQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICB0YXNrRGV0YWlsKHJvdykgewogICAgICB0aGlzLnRhc2tSb3cgPSBbXQogICAgICB0aGlzLnRhc2tSb3cucHVzaChyb3cpCiAgICAgIHRoaXMuZGV0YWlsRGlhbG9nID0gdHJ1ZQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["ffJobTasks.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ffJobTasks.vue", "sourceRoot": "src/views/frailty/monitor", "sourcesContent": ["<template>\n    <div style=\"padding: 0 15px\">\n      <div style=\"margin-bottom: 10px;\">\n        <h3 style=\"font-weight: bold\">扫描任务名称：{{ jobRow.jobName}}</h3>\n      </div>\n      <el-table height=\"100%\" v-loading=\"loading\" :data=\"jobTaskList\">\n        <el-table-column\n          label=\"任务名称\"\n        >\n          <template slot-scope=\"scope\">\n            <span> {{ jobRow.jobName }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"扫描目标\"\n        >\n          <template slot-scope=\"scope\">\n            <el-tooltip v-if=\"jobRow.jobType !== 3\" class=\"item\" placement=\"top\">\n              <div v-html=\"jobRow.ipOver\" slot=\"content\"></div>\n              <div class=\"oneLine\">\n                {{ jobRow.ipShow }}\n              </div>\n            </el-tooltip>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"执行周期\"\n          prop=\"username\"\n          width=\"120\"\n        >\n          <template slot-scope=\"scope\">\n            <span> {{ jobRow.cronTransfer }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"最近扫描状态\"\n          prop=\"taskStatus\">\n          <template slot-scope=\"scope\">\n            <el-tag type=\"danger\" v-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 3\">任务异常</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 1\">扫描中</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 2\">扫描中</el-tag>\n            <el-tag type=\"success\" v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 4\">已扫描</el-tag>\n            <el-tag type=\"danger\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 3\">任务异常</el-tag>\n            <el-tag type=\"danger\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 4\">任务终止</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 0\">扫描中</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 1\">扫描中</el-tag>\n            <el-tag type=\"success\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 2\">已扫描</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"扫描进度\"\n          prop=\"finishRate\"\n          width=\"120\"\n        >\n          <template slot-scope=\"scope\">\n            <el-progress :text-inside=\"true\" :stroke-width=\"18\" :percentage=\"scope.row.finishRate\"></el-progress>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"执行时间\"\n          prop=\"endTime\"\n          width=\"160\"\n        >\n          <template slot-scope=\"scope\">\n            {{ parseTime(scope.row.endTime, \"{y}-{m}-{d} {h}:{i}:{s}\") }}\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"操作\"\n          width=\"160\"\n          fixed=\"right\"\n          class-name=\"small-padding fixed-width\"\n          :show-overflow-tooltip=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              @click=\"taskDetail(scope.row)\"\n            >详情\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 2 && scope.row.reportStatus === null\"\n              @click=\"createReport(scope.row)\"\n            >生成报告\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 4 && scope.row.reportStatus === null\"\n              @click=\"createReport(scope.row)\"\n            >生成报告\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.reportStatus !== null && scope.row.reportStatus !== 2\"\n            >报告生成中\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.reportStatus === 2\"\n              @click=\"downReport(scope.row)\"\n            >下载报告\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n      <el-dialog title=\"任务详情\" :visible.sync=\"detailDialog\" width=\"70%\" append-to-body>\n        <ff-job-tasks-detail v-if=\"detailDialog\" :form=\"taskRow\" :job-row=\"jobRow\" />\n        <!--<detail-info v-if=\"detailDialog\" :host-ip=\"hostIp\" :detail-type=\"detailType\" :dept-name=\"deptName\" :is-asset=\"isAsset\" :current-asset-data=\"currentAssetData\" />-->\n      </el-dialog>\n    </div>\n</template>\n\n<script>\n  import { listScantaskSummary, taskCreatereport, taskDownReport } from '../../../api/monitor2/wpresult'\n  import FfJobTasksDetail from './ffJobTasksDetail'\n\n  export default {\n    name: 'ffJobTasks',\n    components: { FfJobTasksDetail },\n    props: {\n      jobRow: {\n        type: Object,\n        default: {}\n      },\n      jobId: {\n        type: [String,Number],\n        required: true,\n      },\n      jobType:{\n        type:Number,\n        default:undefined,\n      }\n    },\n    data() {\n      return {\n        //下拉框所选中的扫描实例id\n        exploreJobId: '',\n        //任务详细信息\n        jobDetail:{},\n        //任务列表\n        jobTaskList: [],\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n        },\n        // 总条数\n        total: 0,\n        detailDialog: false,\n        processStatus: undefined,\n        processId: '',\n        loading: false,\n        exploreDisabled: false,\n        taskRow: [],\n        timer: null,\n        reportType: ''\n        // jobType: 'tanHuo'\n      }\n    },\n    created() {\n      this.getList()\n      var _this = this;\n      this.timer = setInterval(() => {\n        _this.getList()\n      }, 10000);\n    },\n    beforeDestroy() {\n      // 清除定时器\n      if (this.timer) {\n        clearInterval(this.timer);\n      }\n    },\n    methods: {\n      getList() {\n        this.loading = true;\n        this.queryParams.taskType = this.jobType\n        this.queryParams.jobId = this.jobId\n        listScantaskSummary(this.queryParams).then(response => {\n          this.jobTaskList = response.rows\n          this.total = response.total;\n          this.loading = false;\n        }).catch(() => {\n          this.loading = false;\n        });\n      },\n      createReport(row) {\n        taskCreatereport(row).then(response => {\n          this.getList()\n        })\n      },\n      downReport(row) {\n        taskDownReport(row).then(response => {\n          if (response.code === 200) {\n            window.open(response.msg, \"_blank\")\n          }\n        })\n      },\n      taskDetail(row) {\n        this.taskRow = []\n        this.taskRow.push(row)\n        this.detailDialog = true\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .el-dialog__body {\n  padding: 0 20px 30px;\n  height: 80vh;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n</style>\n"]}]}