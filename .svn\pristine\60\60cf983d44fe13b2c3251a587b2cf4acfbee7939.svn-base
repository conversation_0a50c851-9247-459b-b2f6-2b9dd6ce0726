<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.safe.mapper.MonitorAssetMapper">

    <resultMap type="com.ruoyi.safe.domain.MonitorAsset" id="MonitorAssetResult">
        <result property="pid"    column="pid"    />
        <result property="name"    column="name"    />
        <result property="osVer"    column="os_ver"    />
        <result property="cpe"    column="cpe"    />
        <result property="domainId"    column="domain_id"    />
        <result property="ip"    column="ip"    />
        <result property="mac"    column="mac"    />
        <result property="memo"    column="memo"    />
        <result property="state"    column="state"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <association property="assetVo"  column="ip" javaType="com.ruoyi.safe.domain.AssetIpVo" resultMap="assetVoResult" />
    </resultMap>
    <resultMap id="assetVoResult" type="com.ruoyi.safe.domain.AssetIpVo">
        <id     property="id"    column="id" />
        <result property="mac" column="zcmac" />
        <result property="assetId"  column="asset_id" />
        <result property="assetName"  column="asset_name" />
        <result property="assetCode"  column="asset_code" />
        <result property="assetClass"  column="asset_class" />
        <result property="assetType"  column="asset_type" />
        <result property="assetTypeDesc"    column="asset_type_desc" />
        <result property="assetClassDesc"    column="asset_class_desc"   />
    </resultMap>

    <sql id="selectMonitorAssetVo">
        select pid, name, os_ver, cpe, domain_id, ip, mac, memo, state, create_time, update_time from monitor_server
    </sql>

    <select id="selectMonitorServerListNew" parameterType="com.ruoyi.safe.domain.MonitorAsset" resultMap="MonitorAssetResult">
        SELECT tmp.* FROM (
        SELECT  a.domain_id, a.name, a.os_ver, a.cpe, a.ip, a.mac, a.state,a.pid,a.memo,a.job_id, a.update_time
        ,b.id,b.mac as zcmac,b.asset_vlan as zcnet,b.default_gateway,b.asset_id
        ,c.asset_name,c.asset_class,c.asset_class_desc,c.asset_type, c.asset_type_desc
        FROM monitor_server a
        left join tbl_network_ip_mac b on a.ip = b.ipv4 and a.domain_id =b.domain_id and main_ip='1'
        left join tbl_asset_overview c on b.asset_id =c.asset_id
        ) tmp
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="ip != null  and ip != ''"> and ip like concat('%', #{ip}, '%')</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="id != null  and id != ''"> and id = #{id}</if>
            <if test="assetId != null  and assetId != ''"> and assetId = #{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and asset_name like concat('%', #{assetName}, '%')</if>
            <if test="domainId != null"> and domain_id = #{domainId}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="assetClass != null"> and asset_class = #{assetClass}</if>
            <if test="jobId != null"> and job_id = #{jobId}</if>
        </where>
        order by asset_id desc
    </select>


    <select id="selectMonitorAssetList" parameterType="com.ruoyi.safe.domain.MonitorAsset" resultMap="MonitorAssetResult">
        <include refid="selectMonitorAssetVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="osVer != null  and osVer != ''"> and os_ver = #{osVer}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="mac != null  and mac != ''"> and mac = #{mac}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="jobId != null  and jobId != ''"> and job_id = #{jobId}</if>
        </where>
        ORDER BY state ASC,pid DESC
    </select>

    <select id="selectMonitorAssetByPid" parameterType="java.lang.String" resultMap="MonitorAssetResult">
        <include refid="selectMonitorAssetVo"/>
        where pid = #{pid}
    </select>

    <insert id="insertMonitorAsset" parameterType="com.ruoyi.safe.domain.MonitorAsset">
        insert into monitor_server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid != null">pid,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="osVer != null">os_ver,</if>
            <if test="cpe != null and cpe != ''">cpe,</if>
            <if test="domainId != null">domain_id,</if>
            <if test="ip != null">ip,</if>
            <if test="mac != null">mac,</if>
            <if test="memo != null">memo,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="jobId != null">job_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pid != null">#{pid},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="osVer != null">#{osVer},</if>
            <if test="cpe != null and cpe != ''">#{cpe},</if>
            <if test="domainId != null">#{domainId},</if>
            <if test="ip != null">#{ip},</if>
            <if test="mac != null">#{mac},</if>
            <if test="memo != null">#{memo},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="jobId != null">#{jobId},</if>
        </trim>
    </insert>

    <update id="updateMonitorAsset" parameterType="com.ruoyi.safe.domain.MonitorAsset">
        update monitor_server
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="osVer != null">os_ver = #{osVer},</if>
            <if test="cpe != null and cpe != ''">cpe = #{cpe},</if>
            <if test="domainId != null">domain_id = #{domainId},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="mac != null">mac = #{mac},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="jobId != null">job_id = #{jobId}</if>
        </trim>
        where pid = #{pid}
    </update>

    <delete id="deleteMonitorAssetByJobId" parameterType="java.lang.String">
        delete ms, msp from monitor_server as ms left join monitor_server_port as msp on ms.pid = msp.server_pid where ms.job_id = #{jobId}
    </delete>

    <delete id="deleteMonitorAssetByPid" parameterType="java.lang.String">
        delete from monitor_server where pid = #{pid}
    </delete>

    <delete id="deleteMonitorAssetByPids" parameterType="java.lang.String">
        delete from monitor_server where pid in
        <foreach item="pid" collection="array" open="(" separator="," close=")">
            #{pid}
        </foreach>
    </delete>

    <select id="selectMonitorServerByIp" parameterType="hashmap" resultMap="MonitorAssetResult">
        <include refid="selectMonitorAssetVo"/>
        where domain_id = #{domainId} and ip = #{ip}
    </select>

    <delete id="clearMonitorServer">
        truncate table monitor_server
    </delete>

</mapper>
