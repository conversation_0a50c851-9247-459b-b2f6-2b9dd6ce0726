# aqsoc-main 项目 ffsafe_scantask_summary 表状态管理机制深度分析

## 概述

本文档深入分析 aqsoc-main 项目中 `ffsafe_scantask_summary` 表的状态管理机制，重点关注状态值为 4 的业务场景、触发条件和完整流程链路。

## 1. 表结构分析

### 1.1 核心字段

```sql
-- 表结构关键字段
task_id         int          -- 非凡任务ID
task_type       int          -- 任务类型：2=主机漏扫，1=Web漏扫  
task_status     int          -- 任务状态
finish_rate     int          -- 任务进度：0-100
start_time      datetime     -- 开始时间
end_time        datetime     -- 结束时间
```

### 1.2 当前数据分布

```sql
-- 状态分布统计（截至分析时间）
task_status=1: 8条记录   (task_type=2, 主机漏扫调度中)
task_status=2: 293条记录 (task_type=1, Web漏扫完成)  
task_status=3: 59条记录  (task_type=2, 主机漏扫异常)
task_status=4: 452条记录 (task_type=2, 主机漏扫完成)
```

## 2. 状态常量定义

### 2.1 主机漏扫状态常量
```java
// 文件：ScanResultMonitorEvent.java
public static final int SCHEDULING = 1;  // 正在调度
public static final int RUNNING = 2;     // 任务运行中  
public static final int EXCPTION = 3;    // 任务异常
public static final int FINISH = 4;      // 扫描完成
```

### 2.2 Web漏扫状态常量
```java
// Web漏扫使用不同的状态值
public static final int WEBTASK_SCHEDULING = 0;  // 正在调度
public static final int WEBTASK_RUNNING = 1;     // 任务运行中
public static final int WEBTASK_FINISH = 2;      // 扫描完成
public static final int WEBTASK_FAIL = 4;        // 任务失败
public static final int WEBTASK_STOP = 5;        // 任务中止
```

## 3. 状态 4 的业务含义

### 3.1 核心定义
- **状态值**：4
- **业务含义**：主机漏扫任务完成
- **适用范围**：仅限主机漏扫任务（task_type=2）
- **终态特征**：任务生命周期的最终状态

### 3.2 关键特征
1. **专属性**：只有主机漏扫任务才会有状态4
2. **完整性**：通常伴随 `finish_rate=100`
3. **终态性**：达到状态4后任务不再变更
4. **验证性**：作为报告生成的必要条件

## 4. 状态 4 的触发条件

### 4.1 主要触发路径

#### 4.1.1 定时监控触发（最高频率）
```java
// 文件：ScanResultMonitorEvent.java
// 方法：dealFfsafeScanTaskSummary()
if ((ffsafeScantaskSummary.getTaskStatus() == FINISH) ||
    (ffsafeScantaskSummary.getTaskStatus() == EXCPTION)) {
    // 任务完成处理逻辑
}
```

**触发条件**：
- ScanResultMonitorEvent 定时轮询第三方扫描系统
- 第三方系统返回任务状态为 FINISH(4)
- 系统自动同步状态到本地数据库

#### 4.1.2 数据转换触发
```java
// 文件：HostScanTaskSummaryResult.java
public FfsafeScantaskSummary toFfsafeScantaskSummary() {
    // 直接将第三方状态映射到本地状态
    ffsafeScantaskSummary.setTaskStatus(taskSummaryList.get(0).status);
}
```

### 4.2 触发优先级排序

1. **最高频率**：定时监控轮询（每分钟级别）
2. **中等频率**：任务详情处理完成后同步
3. **较低频率**：手动或其他业务流程触发

## 5. 状态变更完整流程链路

### 5.1 状态流转图

```
[任务创建] → [状态1:调度中] → [状态2:运行中] → [状态4:完成] 
                                    ↓
                              [状态3:异常]
```

### 5.2 详细流程步骤

#### 步骤1：状态检测
```java
// ScanResultMonitorEvent.dealFfsafeScanTaskSummary()
HostScanTaskSummaryResult hostScanTaskSummaryResult = getHostScanTaskSummary(taskId);
FfsafeScantaskSummary ffsafeScantaskSummary = parseTaskSummary(hostScanTaskSummaryResult);
```

#### 步骤2：状态判断
```java
if ((ffsafeScantaskSummary.getTaskStatus() == FINISH) ||
    (ffsafeScantaskSummary.getTaskStatus() == EXCPTION)) {
    // 进入完成处理流程
}
```

#### 步骤3：详情获取
```java
taskDetailParam.setTaskId(ffsafeScantaskSummary.getTaskId());
HostScanTaskDetailResult taskDetailResult = scanTaskService.getTaskDetail(taskDetailParam);
```

#### 步骤4：数据入库
```java
boolean bRet = taskDetailResultService.dealTaskDetailResult(taskId, 
    hostScanTaskSummaryResult, taskDetailResult, sysJob);
```

#### 步骤5：任务清理
```java
sysJob.setCurrentStatus(SysJob.PROCESS_FINISHED);
updateSysJob(sysJob);
removeHostScanTask(taskId); // 从监控列表移除
```

## 6. 数据库更新机制

### 6.1 更新方法
```java
// 文件：TaskDetailResultServiceImpl.java
ffsafeScantaskSummaryService.updateFfsafeScantaskSummaryByTaskId(ffsafeScantaskSummary);
```

### 6.2 SQL 实现
```xml
<!-- 文件：FfsafeScantaskSummaryMapper.xml -->
<update id="updateFfsafeScantaskSummaryByTaskId" parameterType="FfsafeScantaskSummary">
    update ffsafe_scantask_summary
    <trim prefix="SET" suffixOverrides=",">
        <if test="taskStatus != null">task_status = #{taskStatus},</if>
        <if test="finishRate != null">finish_rate = #{finishRate},</if>
        <if test="endTime != null">end_time = #{endTime},</if>
        <!-- 其他字段... -->
    </trim>
    where task_id = #{taskId} and task_type = #{taskType}
</update>
```

### 6.3 更新特点
- **联合条件**：使用 task_id + task_type 作为更新条件
- **动态更新**：只更新非空字段
- **原子操作**：单条SQL完成状态更新

## 7. 业务验证机制

### 7.1 报告生成验证
```java
// 文件：FfsafeScantaskSummaryServiceImpl.java
// 主机漏扫任务必须满足：task_status=4 且 finish_rate=100
if (taskType.equals(2)) {
    isTaskCompleted = (taskStatus != null && taskStatus.equals(4)) && isFinishRate;
    taskTypeDesc = "主机漏扫任务";
    requiredStatus = "4(已完成)";
}
```

### 7.2 验证规则
1. **状态验证**：task_status 必须等于 4
2. **进度验证**：finish_rate 必须等于 100
3. **类型验证**：task_type 必须等于 2（主机漏扫）

## 8. 异常处理机制

### 8.1 失败重试
```java
// 失败计数和重试机制
if (nRet == 0) {
    int failCount = incrementHostScanTaskFailCount(taskId);
    if (isHostScanTaskMaxFailuresReached(taskId)) {
        removeHostScanTask(taskId); // 达到最大失败次数，移除任务
    }
}
```

### 8.2 异常场景
1. **网络异常**：无法连接第三方扫描系统
2. **数据异常**：返回数据格式错误
3. **业务异常**：状态不一致或数据缺失

## 9. 关键代码位置汇总

| 功能模块 | 文件路径 | 关键方法/类 |
|---------|----------|------------|
| 状态常量定义 | `ScanResultMonitorEvent.java` | 常量定义 |
| 状态检测逻辑 | `ScanResultMonitorEvent.java` | `dealFfsafeScanTaskSummary()` |
| 数据转换 | `HostScanTaskSummaryResult.java` | `toFfsafeScantaskSummary()` |
| 数据库更新 | `TaskDetailResultServiceImpl.java` | `dealTaskSummaryResult()` |
| SQL映射 | `FfsafeScantaskSummaryMapper.xml` | `updateFfsafeScantaskSummaryByTaskId` |
| 业务验证 | `FfsafeScantaskSummaryServiceImpl.java` | `selectAndValidateTaskSummariesByIds()` |

## 10. 总结

状态 4 在 ffsafe_scantask_summary 表中具有重要的业务意义：

1. **唯一性**：专属于主机漏扫任务的完成状态
2. **关键性**：作为报告生成和业务验证的核心条件  
3. **稳定性**：通过多层验证确保数据一致性
4. **可靠性**：具备完善的异常处理和重试机制

该状态管理机制确保了扫描任务的完整生命周期管理，为后续的报告生成、数据分析等业务功能提供了可靠的数据基础。
