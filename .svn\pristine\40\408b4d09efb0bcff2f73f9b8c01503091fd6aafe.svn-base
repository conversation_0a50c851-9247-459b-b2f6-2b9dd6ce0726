<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.threaten.mapper.TblThreatenAlarmMapper">

    <resultMap type="TblThreatenAlarm" id="TblThreatenAlarmBaseResult" autoMapping="true">
        <result property="id"    column="id"    />
        <result property="threatenName"    column="threaten_name"    />
        <result property="threatenType"    column="threaten_type"    />
        <result property="label"    column="label"    />
        <result property="lossState"    column="loss_state"    />
        <result property="handSuggest"    column="hand_suggest"    />
        <result property="srcIp"    column="src_ip"    />
        <result property="srcPort"    column="src_port"    />
        <result property="procotol"    column="procotol"    />
        <result property="destIp"    column="dest_ip"    />
        <result property="destPort"    column="dest_port"    />
        <result property="associaDevice"    column="associa_device"    />
        <result property="hitIntelligence"    column="hit_intelligence"    />
        <result property="handleState"    column="handle_status"    />
        <result property="flowState"    column="flow_state"    />
        <result property="alarmLevel"    column="alarm_level"    />
        <result property="alarmNum"    column="alarm_num"    />
        <result property="dataSource"    column="data_source"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="handleType"    column="handle_type"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="assetName"    column="asset_name"    />
        <result property="assetTypeDesc"    column="asset_type_desc" />
        <result property="assetClassDesc"    column="asset_class_desc" />
        <result property="assetId"    column="asset_id" />
        <result property="prodId" column="prod_id" />
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="attackSeg" column="attack_seg" />
        <result property="attackIp" column="attack_ip" />
        <result property="victimIp" column="victim_ip" />
        <result property="handleState" column="handle_state" />
        <result property="handleDesc" column="handle_desc" />
        <result property="workOrderId" column="work_order_id" />
        <result property="disposer" column="disposer" />
        <result property="synchronizationStatus" column="synchronization_status" />
        <result property="typeSegStr" column="type_seg_str" />
        <result property="deviceConfigId" column="device_config_id" />
    </resultMap>

    <resultMap type="TblThreatenAlarm" id="TblThreatenAlarmResult" autoMapping="true">
        <result property="id"    column="id"    />
        <result property="threatenName"    column="threaten_name"    />
        <result property="threatenType"    column="threaten_type"    />
        <result property="label"    column="label"    />
        <result property="lossState"    column="loss_state"    />
        <result property="handSuggest"    column="hand_suggest"    />
        <result property="srcIp"    column="src_ip"    />
        <result property="srcPort"    column="src_port"    />
        <result property="procotol"    column="procotol"    />
        <result property="destIp"    column="dest_ip"    />
        <result property="destPort"    column="dest_port"    />
        <result property="associaDevice"    column="associa_device"    />
        <result property="hitIntelligence"    column="hit_intelligence"    />
        <result property="handleState"    column="handle_status"    />
        <result property="flowState"    column="flow_state"    />
        <result property="alarmLevel"    column="alarm_level"    />
        <result property="alarmNum"    column="alarm_num"    />
        <result property="dataSource"    column="data_source"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="handleType"    column="handle_type"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="assetName"    column="asset_name"    />
        <result property="assetTypeDesc"    column="asset_type_desc" />
        <result property="assetClassDesc"    column="asset_class_desc" />
        <result property="assetId"    column="asset_id" />
        <result property="prodId" column="prod_id" />
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="attackSeg" column="attack_seg" />
        <result property="attackIp" column="attack_ip" />
        <result property="victimIp" column="victim_ip" />
        <result property="handleState" column="handle_state" />
        <result property="handleDesc" column="handle_desc" />
        <result property="workOrderId" column="work_order_id" />
        <result property="disposer" column="disposer" />
        <result property="synchronizationStatus" column="synchronization_status" />
        <result property="typeSegStr" column="type_seg_str" />
        <result property="deviceConfigId" column="device_config_id" />
        <collection property="businessApplications" column="query_asset_id" select="com.ruoyi.safe.mapper.TblBusinessApplicationMapper.selectListByServerId" />
    </resultMap>

    <resultMap id="FfsafeHoneypotAttackdetailResult" type="com.ruoyi.threaten.domain.FfsafeHoneypotAttackdetail">
        <result property="id" column="id" />
        <result property="srcIp" column="src_ip" />
        <result property="srcPort" column="src_port" />
        <result property="destIp" column="dest_ip" />
        <result property="destPort" column="dest_port" />
        <result property="procotol" column="procotol" />
        <result property="eventName" column="event_name" />
        <result property="eventType" column="event_type" />
        <result property="attackNum" column="attack_num" />
        <result property="eventLevel" column="event_level" />
        <result property="content" column="content" />
        <result property="filename" column="filename" />
        <result property="other" column="other" />
        <result property="type" column="type" />
        <result property="startTime" column="start_time" />
        <result property="endTime" column="end_time" />
        <result property="md5" column="md5" />
        <result property="sha256" column="sha256" />
        <result property="attackTag" column="attack_tag" />
        <result property="timeline" column="timeline" />
        <collection property="businessApplications" column="query_asset_id" select="com.ruoyi.safe.mapper.TblBusinessApplicationMapper.selectListByServerId" />
    </resultMap>

    <sql id="selectTblThreatenAlarmVo">
        select id, threaten_name, threaten_type, alarm_level, src_port,src_ip,dest_ip, data_source, alarm_num, create_time, update_time,file_url,asset_id,reason,device_config_id from tbl_threaten_alarm
    </sql>

    <sql id="selectTblThreatenAlarmStateVo">
        select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,ta.dept_id,ta.device_config_id from tbl_threaten_alarm ta
    </sql>

    <select id="getTblThreatenAlarmList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        SELECT ta.* FROM tbl_threaten_alarm ta LEFT JOIN tbl_work_order wo ON FIND_IN_SET(ta.id, wo.event_ids)
        <where>
            (work_type = '3' or work_type is null) and (flow_state != '4' or flow_state is null)
            <if test="threatenName != null  and threatenName != ''"> and threaten_name = #{threatenName}</if>
            <if test="threatenType != null  and threatenType != ''"> and threaten_type = #{threatenType}</if>
            <if test="srcIp != null  and srcIp != ''"> and src_ip = #{srcIp}</if>
            <if test="destIp != null  and destIp != ''"> and dest_ip = #{destIp}</if>
            <if test="destPort != null "> and dest_port = #{destPort}</if>
            <if test="dataSource != null "> and data_source = #{dataSource}</if>
            <if test="id != null "> and ta.id = #{id}</if>
            <if test="startCreateTime!=null">
                and ta.create_time >= #{startCreateTime}
            </if>
            <if test="strategy != null "> and ta.strategy like concat('%', #{strategy}, '%')</if>
            <if test="deviceConfigId != null"> and ta.device_config_id = #{deviceConfigId}</if>
        </where>
    </select>

    <select id="selectTblThreatenAlarmList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        SELECT
        ta.id,
        ta.threaten_name,
        ta.threaten_type,
        tyd.id AS threatenId,
        ta.label,
        ta.loss_state,
        ta.hand_suggest,
        ta.src_ip,
        ta.src_port,
        ta.dest_ip,
        ta.dest_port,
        ta.hit_intelligence,
        ta.alarm_level,
        ta.associa_device,
        ta.create_by,
        ta.create_time,
        ta.update_by,
        ta.update_time,
        ta.handle_state,
        ta.handle_desc,
        ta.data_source,
        ta.alarm_num,
        ta.file_url,
        ta.work_order_id,
        ta.disposer,
        ta.synchronization_status,
        ta.device_config_id
        FROM
        tbl_threaten_alarm ta
        LEFT JOIN threaten_typeseg_dict tyd ON tyd.threaten_type = ta.threaten_type
        <where>
            <if test="ids != null and ids.size() > 0">
                and ta.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
               and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and ta.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and ta.dest_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="limitStartTime != null"> and ta.update_time >= #{limitStartTime}</if>
            <if test="limitEndTime != null"> and ta.update_time &lt;= #{limitEndTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="attackSeg != null and attackSeg != ''">
                and tyd.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                    ta.src_ip in
                    <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                        #{ipv4Item}
                    </foreach>
                    OR
                    ta.dest_ip in
                    <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                        #{ipv4Item}
                    </foreach>
                )
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
            <if test="deviceConfigId != null">
                and ta.device_config_id = #{deviceConfigId}
            </if>
        </where>
        ORDER BY
        ta.update_time DESC
    </select>

    <select id="selectTblThreatenAlarmStateList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select ta.id,wo.id as workId,ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port, ta.dest_ip, ta.dest_port,
               ta.hit_intelligence, ta.alarm_level, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.device_config_id,
               wo.flow_state,wh.handle_state,ta.data_source,ta.alarm_num,ta.file_url,ta.work_order_id,tao.asset_name,tao.asset_type_desc,tao.asset_class_desc,tao.asset_id
        from tbl_threaten_alarm ta
        left join tbl_asset_overview tao on tao.asset_id = (select asset_id from tbl_network_ip_mac where ipv4 = ta.dest_ip GROUP BY ipv4)
        left join tbl_work_order wo on FIND_IN_SET(ta.id,wo.event_ids) AND wo.work_type = '3'
        left join tbl_work_history wh on wh.work_id=wo.id and wo.flow_state = '4' and wh.id=(select twh.id from tbl_work_history twh where twh.work_id=wo.id order by twh.create_time desc limit 1)
        <where>
            <if test="threatenName != null  and threatenName != ''"> and threaten_name = #{threatenName}</if>
            <if test="threatenType != null  and threatenType != ''"> and threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and src_ip = #{srcIp}</if>
            <if test="srcPort != null "> and src_port = #{srcPort}</if>
            <if test="procotol != null "> and procotol = #{procotol}</if>
            <if test="destIp != null  and destIp != ''"> and dest_ip = #{destIp}</if>
            <if test="destPort != null "> and dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and alarm_level = #{alarmLevel}</if>
            <if test="deviceConfigId != null"> and ta.device_config_id = #{deviceConfigId}</if>
        </where>
    </select>


    <select id="selectTblThreatenAlarmById" parameterType="Long" resultMap="TblThreatenAlarmResult">
        select ta.id, ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.src_ip, ta.src_port,
               ta.dest_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level,data_source, alarm_num, ta.associa_device,
               ta.create_by, ta.create_time, ta.update_by, ta.update_time,ta.file_url,ta.asset_id,ta.reason,ta.strategy,
               ta.dept_id ,ta.work_order_id,ta.device_config_id,t2.dept_name,group_concat(nim.asset_id) query_asset_id
        from tbl_threaten_alarm ta
        left join sys_dept t2 on t2.dept_id=ta.dept_id
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = ta.src_ip OR nim.ipv4 = ta.dest_ip)
        where ta.id = #{id}
    </select>

    <select id="selectTblThreatenAlarmByIds" parameterType="Long" resultMap="TblThreatenAlarmResult">
        <include refid="selectTblThreatenAlarmVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertTblThreatenAlarm" parameterType="TblThreatenAlarm" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_threaten_alarm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="threatenName != null">threaten_name,</if>
            <if test="threatenType != null">threaten_type,</if>
            <if test="label != null">label,</if>
            <if test="lossState != null">loss_state,</if>
            <if test="handSuggest != null">hand_suggest,</if>
            <if test="srcIp != null">src_ip,</if>
            <if test="srcPort != null">src_port,</if>
            <if test="destIp != null">dest_ip,</if>
            <if test="destPort != null">dest_port,</if>
            <if test="hitIntelligence != null">hit_intelligence,</if>
            <if test="alarmLevel != null">alarm_level,</if>
            <if test="associaDevice != null">associa_device,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="dataSource != null">data_source,</if>
            <if test="alarmNum != null">alarm_num,</if>
            <if test="assetId != null">asset_id,</if>
            <if test="reason != null">reason,</if>
            <if test="strategy != null">strategy,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="disposer != null">disposer,</if>
            <if test="synchronizationStatus != null">synchronization_status,</if>
            <if test="dataId != null">data_id,</if>
            <if test="deviceConfigId != null">device_config_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="threatenName != null">#{threatenName},</if>
            <if test="threatenType != null">#{threatenType},</if>
            <if test="label != null">#{label},</if>
            <if test="lossState != null">#{lossState},</if>
            <if test="handSuggest != null">#{handSuggest},</if>
            <if test="srcIp != null">#{srcIp},</if>
            <if test="srcPort != null">#{srcPort},</if>
            <if test="destIp != null">#{destIp},</if>
            <if test="destPort != null">#{destPort},</if>
            <if test="hitIntelligence != null">#{hitIntelligence},</if>
            <if test="alarmLevel != null">#{alarmLevel},</if>
            <if test="associaDevice != null">#{associaDevice},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="alarmNum != null">#{alarmNum},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="reason != null">#{reason},</if>
            <if test="strategy != null">#{strategy},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="disposer != null">#{disposer},</if>
            <if test="synchronizationStatus != null">#{synchronizationStatus},</if>
            <if test="dataId != null">#{dataId},</if>
            <if test="deviceConfigId != null">#{deviceConfigId},</if>
        </trim>
    </insert>

    <insert id="insertTblThreatenAlarmList" parameterType="list" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_threaten_alarm (threaten_name,threaten_type,label,loss_state,hand_suggest,src_ip,src_port,dest_ip,dest_port,hit_intelligence,alarm_level,associa_device,create_by,create_time,update_by,update_time,file_url,device_config_id) values
        <foreach collection="list" item="item" separator=",">
            (#{item.threatenName}, #{item.threatenType}, #{item.label}, #{item.lossState}, #{item.handSuggest}, #{item.srcIp}, #{item.srcPort}, #{item.destIp}, #{item.destPort}, #{item.hitIntelligence}, #{item.alarmLevel}, #{item.associaDevice}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime},#{item.fileUrl},#{item.deviceConfigId})
        </foreach>
    </insert>

    <update id="updateTblThreatenAlarm" parameterType="TblThreatenAlarm">
        update tbl_threaten_alarm
        <trim prefix="SET" suffixOverrides=",">
            <if test="threatenName != null">threaten_name = #{threatenName},</if>
            <if test="threatenType != null">threaten_type = #{threatenType},</if>
            <if test="label != null">label = #{label},</if>
            <if test="lossState != null">loss_state = #{lossState},</if>
            <if test="handSuggest != null">hand_suggest = #{handSuggest},</if>
            <if test="srcIp != null">src_ip = #{srcIp},</if>
            <if test="srcPort != null">src_port = #{srcPort},</if>
            <if test="destIp != null">dest_ip = #{destIp},</if>
            <if test="destPort != null">dest_port = #{destPort},</if>
            <if test="hitIntelligence != null">hit_intelligence = #{hitIntelligence},</if>
            <if test="alarmLevel != null">alarm_level = #{alarmLevel},</if>
            <if test="associaDevice != null">associa_device = #{associaDevice},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="alarmNum != null">alarm_num = #{alarmNum},</if>
            <if test="assetId != null">asset_id = #{assetId},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="strategy != null">strategy = #{strategy},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="handleState != null">handle_state = #{handleState},</if>
            <if test="handleDesc != null">handle_desc = #{handleDesc},</if>
            <if test="disposer != null">disposer = #{disposer},</if>
            <if test="synchronizationStatus != null">synchronization_status = #{synchronizationStatus},</if>
            <if test="deviceConfigId != null">device_config_id = #{deviceConfigId},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdateHandleState">
        UPDATE tbl_threaten_alarm SET
        handle_state = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleState != null">#{item.handleState}</if> <if test="item.handleState == null">handle_state</if>
        </foreach>
        ELSE handle_state
        END,
        work_order_id = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.workOrderId != null">#{item.workOrderId}</if> <if test="item.workOrderId == null">work_order_id</if>
        </foreach>
        ELSE work_order_id
        END,
        disposer = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.disposer != null">#{item.disposer}</if> <if test="item.disposer == null">disposer</if>
        </foreach>
        ELSE disposer
        END,
        handle_desc = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.handleDesc != null">#{item.handleDesc}</if> <if test="item.handleDesc == null">handle_desc</if>
        </foreach>
        ELSE handle_desc
        END,
        device_config_id = CASE id
        <foreach item="item" collection="list" separator=" ">
            WHEN #{item.id} THEN <if test="item.deviceConfigId != null">#{item.deviceConfigId}</if> <if test="item.deviceConfigId == null">device_config_id</if>
        </foreach>
        ELSE device_config_id
        END
        where id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <delete id="deleteTblThreatenAlarmById" parameterType="Long">
        delete from tbl_threaten_alarm where id = #{id}
    </delete>

    <delete id="deleteTblThreatenAlarmByIds" parameterType="String">
        delete from tbl_threaten_alarm where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="count" resultType="java.lang.Integer">
        select count(1)
        from tbl_threaten_alarm
        <where>
            <if test="threatenName != null  and threatenName != ''"> and threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and threaten_type like concat('%',#{threatenType},'%')</if>
            <if test="label != null  and label != ''"> and label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and src_ip like concat('%',#{srcIp},'%')</if>
            <if test="srcPort != null "> and src_port = #{srcPort}</if>
            <if test="procotol != null "> and procotol = #{procotol}</if>
            <if test="destIp != null  and destIp != ''"> and dest_ip like concat('%',#{destIp},'%')</if>
            <if test="destPort != null "> and dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and alarm_level = #{alarmLevel}</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="deviceConfigId != null"> and ta.device_config_id=#{deviceConfigId}</if>
        </where>
    </select>
    <select id="getThreatenType" resultType="java.util.HashMap">
        select threaten_type as name,count(threaten_type) as value
        from tbl_threaten_alarm
        <where>
            <if test="startTime != null and endTime != null">
                update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        group by threaten_type
        order by count(threaten_type) desc
        <if test="top10">
            limit 10
        </if>
    </select>

    <select id="getThreatenName" resultType="java.util.HashMap">
        select threaten_name as name,count(threaten_name) as value
        from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id
        <where>
            <if test="startTime != null and endTime != null">
                t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        group by threaten_name
        order by count(threaten_name) desc
        <if test="top10">
            limit 10
        </if>
    </select>
<!--
    <select id="getThreatenGrade" resultType="java.util.HashMap">
        SELECT
            CASE alarm_level
                WHEN '2' THEN '低危'
                WHEN '3' THEN '中危'
                WHEN '4' THEN '高危'
                WHEN '5' THEN '严重'
                END AS name,
            count(alarm_level) as value
        from tbl_threaten_alarm WHERE alarm_level IN ('2', '3', '4', '5')
        group by alarm_level
        order by alarm_level desc
    </select>
    -->

    <!-- 获取威胁告警等级统计 -->
    <select id="getThreatenGrade" resultType="java.util.HashMap">
        SELECT
        t.level_name as name,
        COALESCE(COUNT(a.alarm_level), 0) as value
        FROM (
        SELECT '0' as level_code, '未知' as level_name UNION ALL
        SELECT '2', '低危' UNION ALL
        SELECT '3', '中危' UNION ALL
        SELECT '4', '高危' UNION ALL
        SELECT '5', '严重'
        ) t
        LEFT JOIN tbl_threaten_alarm a ON a.alarm_level = t.level_code
        AND a.alarm_level IN ('0','2', '3', '4', '5')
        <if test="startTime != null and endTime != null">
            AND a.update_time BETWEEN #{startTime} AND #{endTime}
        </if>
        WHERE t.level_code IN ('0','2', '3', '4', '5')
        GROUP BY t.level_code
        ORDER BY t.level_code DESC
    </select>

    <select id="getAttackIpList" resultMap="TblThreatenAlarmResult">
        select
            src_ip,
            CASE WHEN alarm_num IS NULL THEN 0 ELSE alarm_num END AS alarm_num,
            CASE WHEN update_time IS NULL THEN create_time ELSE update_time END AS update_time
        from tbl_threaten_alarm
        ORDER BY alarm_num DESC
        <if test="top10">
            limit 10
        </if>
    </select>

    <select id="getDestIpList" resultMap="TblThreatenAlarmResult">
        select
            dest_ip,
            CASE WHEN alarm_num IS NULL THEN 0 ELSE alarm_num END AS alarm_num,
            CASE WHEN update_time IS NULL THEN create_time ELSE update_time END AS update_time
        from tbl_threaten_alarm
        ORDER BY alarm_num desc
        <if test="top10">
            limit 10
        </if>
    </select>

    <select id="getAlertInfoList" resultMap="TblThreatenAlarmResult">
        SELECT
            *
        FROM
            ( SELECT threaten_name, threaten_type, alarm_level, CASE WHEN update_time IS NULL THEN create_time ELSE update_time END AS update_time FROM tbl_threaten_alarm ) t
        <where>
            <if test="startTime != null and endTime != null">
                update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="showAlarmLevels != null and showAlarmLevels.size() != 0">
                AND t.alarm_level IN
                <foreach collection="showAlarmLevels"
                         item="alarmLevel" index="index" separator="," open="(" close=")">
                    #{alarmLevel}
                </foreach>
            </if>
        </where>
        <if test="params.orderBy != null">
            ORDER BY ${params.orderBy}
        </if>
        <if test="params.sort != null">
            ${params.sort}
        </if>
    </select>

    <select id="getThreatenDayList" resultType="java.util.Map">
        SELECT DATE_FORMAT(t.update_time,'%Y-%m-%d') AS dateStr,
            dict_label AS level,
            count( distinct id) AS countNum
        FROM (
            SELECT dict_label,a.id,
            CASE  WHEN a.update_time IS NULL THEN a.create_time ELSE a.update_time END AS update_time,sd.dept_id
            FROM tbl_threaten_alarm a
            LEFT JOIN sys_dict_data d ON d.dict_type = 'threaten_type'
            AND alarm_level = d.dict_value
            left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = a.dest_ip OR nim.ipv4 = a.src_ip)
            left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
            left join sys_dept sd on sd.dept_id = tao.dept_id
            <where>
                <if test="deptId != null">
                    and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                </if>
            </where>
        ) t
        <where>
            <if test="startTime != null and endTime != null">
                t.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
            DATE_FORMAT(t.update_time,'%Y-%m-%d'),
            dict_label
    </select>

    <select id="getThreatenMonthList" resultType="java.util.Map">
        SELECT DATE_FORMAT(t.update_time,'%Y-%m') AS dateStr,
            dict_label AS level,
            count( distinct id ) AS countNum
        FROM (
            SELECT dict_label,a.id,
            CASE WHEN a.update_time IS NULL THEN a.create_time ELSE a.update_time END AS update_time,sd.dept_id
            FROM tbl_threaten_alarm a
            LEFT JOIN sys_dict_data d ON d.dict_type = 'threaten_type'
            AND alarm_level = d.dict_value
            left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = a.dest_ip OR nim.ipv4 = a.src_ip)
            left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
            left join sys_dept sd on sd.dept_id = tao.dept_id
            <where>
                <if test="deptId != null">
                    and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                </if>
            </where>
            ) t
        <where>
            <if test="startTime != null and endTime != null">
                t.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
            DATE_FORMAT(t.update_time,'%Y-%m'),
            dict_label
    </select>

    <select id="getThreatenYearList" resultType="java.util.Map">
        SELECT DATE_FORMAT(t.update_time,'%Y') AS dateStr,
            dict_label AS level,
            count( distinct id ) AS countNum
        FROM (
            SELECT dict_label,a.id,
            CASE WHEN a.update_time IS NULL THEN a.create_time ELSE a.update_time END AS update_time,a.dept_id
            FROM
            tbl_threaten_alarm a
            LEFT JOIN sys_dict_data d ON d.dict_type = 'threaten_type'
            AND alarm_level = d.dict_value
            left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = a.dest_ip OR nim.ipv4 = a.src_ip)
            left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
            left join sys_dept sd on sd.dept_id = tao.dept_id
            <where>
                <if test="deptId != null">
                    and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                </if>
            </where>
            ) t
        <where>
            <if test="startTime != null and endTime != null">
                t.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        GROUP BY
            DATE_FORMAT(t.update_time,'%Y'),
            dict_label
    </select>

    <select id="getAttackNum" resultType="java.lang.Integer">
        <!--select count(distinct id) as attack_num from (
            select t1.id,src_ip as attack_ip, dest_ip as victim_ip from tbl_threaten_alarm t1
            left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.src_ip)
            left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
            left join sys_dept sd on sd.dept_id = tao.dept_id
            <where>
                threaten_type not in('其他/网络异常/主机外联', '其他/网络异常/内网外联') and t1.handle_state=0
                <if test="startTime != null and endTime != null">
                    and t1.update_time BETWEEN #{startTime} AND #{endTime}
                </if>
                <if test="deptId != null">
                    and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                </if>
            </where>
        ) as temp-->
        <if test="deptId == null and startTime == null">
            SELECT
            COUNT(DISTINCT t1.id) AS attack_num
            FROM tbl_threaten_alarm t1
            LEFT JOIN (
            SELECT '其他/网络异常/主机外联' AS exclude_type
            UNION ALL
            SELECT '其他/网络异常/内网外联'
            ) ex ON t1.threaten_type = ex.exclude_type
            WHERE
            t1.handle_state = 0
            AND ex.exclude_type IS NULL
        </if>
        <if test="deptId != null or startTime != null">
            SELECT
            COUNT(DISTINCT t1.id) AS attack_num
            FROM tbl_threaten_alarm t1
            LEFT JOIN (
            SELECT '其他/网络异常/主机外联' AS exclude_type
            UNION ALL
            SELECT '其他/网络异常/内网外联'
            ) ex ON t1.threaten_type = ex.exclude_type
            LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) nim ON ( nim.ipv4 = t1.src_ip )
            LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
            LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
            <where>
                t1.handle_state = 0
                AND ex.exclude_type IS NULL
                <if test="startTime != null and endTime != null">
                    and t1.update_time BETWEEN #{startTime} AND #{endTime}
                </if>
                <if test="deptId != null">
                    and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                </if>
            </where>
        </if>
    </select>

    <select id="getHighRiskHostNum" resultType="java.lang.Integer">
         <!--select count(distinct temp.id) as highRiskHostNum from (
            select t1.id,src_ip as attack_ip, dest_ip as victim_ip from tbl_threaten_alarm t1
            left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
            left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
            left join sys_dept sd on sd.dept_id = tao.dept_id
            <where>
                threaten_type not in('其他/网络异常/主机外联', '其他/网络异常/内网外联') and alarm_level > 3
                AND nim.ipv4 is not null and t1.handle_state=0
                <if test="startTime != null and endTime != null">
                     and t1.update_time BETWEEN #{startTime} AND #{endTime}
                </if>
                <if test="deptId != null">
                    and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                </if>
            </where>
            GROUP BY nim.ipv4
        ) as temp-->
        SELECT
        count(1)
        FROM(
        SELECT
        COUNT( DISTINCT temp.id ) AS highRiskHostNum
        FROM
        (
        SELECT
        nim.ipv4,
        t1.id
        FROM
        tbl_threaten_alarm t1
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) nim ON nim.ipv4 = t1.src_ip
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            threaten_type not in('其他/网络异常/主机外联', '其他/网络异常/内网外联') and alarm_level > 3
            AND nim.ipv4 is not null and t1.handle_state=0
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        GROUP BY
        nim.ipv4

        UNION

        SELECT
        nim.ipv4,
        t1.id
        FROM
        tbl_threaten_alarm t1
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) nim ON nim.ipv4 = t1.dest_ip
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            threaten_type not in('其他/网络异常/主机外联', '其他/网络异常/内网外联') and alarm_level > 3
            AND nim.ipv4 is not null and t1.handle_state=0
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        GROUP BY
        nim.ipv4
        ) AS temp
        GROUP BY temp.ipv4
        )temp
    </select>

    <select id="getRiskHostNum" resultType="java.lang.Integer">
         <!--select count(distinct temp.id) as highRiskHostNum from (
            select t1.id,src_ip as attack_ip, dest_ip as victim_ip from tbl_threaten_alarm t1
            left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
            left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
            left join sys_dept sd on sd.dept_id = tao.dept_id
            <where>
                threaten_type not in('其他/网络异常/主机外联', '其他/网络异常/内网外联')
                AND nim.ipv4 is not null and t1.handle_state=0
                <if test="startTime != null and endTime != null">
                    and t1.update_time BETWEEN #{startTime} AND #{endTime}
                </if>
                <if test="deptId != null">
                    and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                </if>
            </where>
            GROUP BY nim.ipv4
        ) as temp-->
        SELECT
        COUNT(DISTINCT combined.id) AS highRiskHostNum
        FROM (
        SELECT
        t1.id
        FROM
        tbl_threaten_alarm t1
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) src ON t1.src_ip = src.ipv4
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = src.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            threaten_type not in('其他/网络异常/主机外联', '其他/网络异常/内网外联')
            AND src.ipv4 is not null and t1.handle_state=0
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        GROUP BY
        src.ipv4

        UNION ALL

        SELECT
        t1.id
        FROM
        tbl_threaten_alarm t1
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) dest ON t1.dest_ip = dest.ipv4
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = dest.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            t1.threaten_type NOT IN ( '其他/网络异常/主机外联', '其他/网络异常/内网外联' )
            AND t1.handle_state = 0
            AND dest.ipv4 IS NOT NULL
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        GROUP BY
        dest.ipv4
        ) AS combined

    </select>

    <select id="getThreatenAlarmNum" resultType="java.lang.Integer">
        <!--select count(distinct t1.id) as threatenAlarmNum from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id
        <where>
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
            <if test="dataSource != null">
                and t1.data_source=#{dataSource}
            </if>
            <if test="handleState != null">
                and t1.handle_state=#{handleState}
            </if>
        </where>-->
        SELECT
        count( DISTINCT combined.id ) AS threatenAlarmNum
        FROM
        (
        SELECT
        t1.id
        FROM
        tbl_threaten_alarm t1
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) nim ON nim.ipv4 = t1.dest_ip
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            nim.asset_id is not null
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
            <if test="dataSource != null">
                and t1.data_source=#{dataSource}
            </if>
            <if test="handleState != null">
                and t1.handle_state=#{handleState}
            </if>
        </where>

        UNION ALL

        SELECT
        t1.id
        FROM
        tbl_threaten_alarm t1
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) nim ON nim.ipv4 = t1.src_ip
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            nim.asset_id is not null
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
            <if test="dataSource != null">
                and t1.data_source=#{dataSource}
            </if>
            <if test="handleState != null">
                and t1.handle_state=#{handleState}
            </if>
        </where>
        ) AS combined
    </select>

    <select id="getThreatenLevelNum" resultType="java.util.Map">
        <!--select alarm_level, count(distinct t1.id) as threatenLevelNum from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id
        <where>
            t1.handle_state = 0
            <if test="startTime != null and endTime != null">
                 and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        group by alarm_level-->
        SELECT
        combined.alarm_level,
        count( DISTINCT combined.id ) AS threatenAlarmNum
        FROM
        (
        SELECT
        alarm_level,
        t1.id
        FROM
        tbl_threaten_alarm t1
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) nim ON nim.ipv4 = t1.dest_ip
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            t1.handle_state = 0 and nim.asset_id is not null
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>

        UNION ALL

        SELECT
        alarm_level,
        t1.id
        FROM
        tbl_threaten_alarm t1
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) nim ON  nim.ipv4 = t1.src_ip
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            t1.handle_state = 0 and nim.asset_id is not null
            <if test="startTime != null and endTime != null">
                and t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        ) AS combined
        GROUP BY
        alarm_level
    </select>

    <select id="selectAttackIpList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select ta.id,ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.attack_ip, ta.src_port, ta.victim_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time
        ,ta.data_source,ta.alarm_num,ta.file_url,ta.dept_id,ta.dept_name,tao.asset_id as query_asset_id,tao.asset_name,tao.asset_type_desc,tao.asset_class_desc,tao.asset_id, nim.ipv4 from (
        select t1.id, threaten_name, threaten_type, t1.label, loss_state, hand_suggest, src_ip as attack_ip, src_port, dest_ip as victim_ip, dest_port, hit_intelligence, alarm_level, associa_device, t1.create_by, t1.create_time, t1.update_by, t1.update_time,
        data_source,alarm_num, t1.file_url,t1.dept_id,t1.asset_id,sd.dept_name
        from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id where
                                    threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
                                    <if test="deptId != null">
                                        and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                                    </if>
        <!--union all
        select t1.id, threaten_name, threaten_type, label, loss_state, hand_suggest, dest_ip as attack_ip, src_port, src_ip as victim_ip, dest_port, hit_intelligence, alarm_level, associa_device, t1.create_by, t1.create_time, t1.update_by, t1.update_time,
        data_source,alarm_num, t1.file_url,t1.dept_id,t1.asset_id,sd.dept_name
        from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id where
                                    threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
                                    <if test="deptId != null">
                                        and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                                    </if>-->
        ) ta
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on nim.ipv4 = ta.attack_ip
        left join tbl_asset_overview tao on tao.asset_id = nim.asset_id
        <!-- left join tbl_asset_overview tao on tao.asset_id = (select asset_id from tbl_network_ip_mac where ipv4 = ta.victim_ip GROUP BY ipv4)
        left join tbl_work_order wo on FIND_IN_SET(ta.id,wo.event_ids) AND wo.work_type = '3'
        left join tbl_work_history wh on wh.work_id=wo.id and wo.flow_state = '4' and wh.id=(select twh.id from tbl_work_history twh where twh.work_id=wo.id order by twh.create_time desc limit 1)-->
        <where>
            <if test="threatenName != null  and threatenName != ''"> and threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="alarmLevel != null "> and alarm_level = #{alarmLevel}</if>
            <if test="srcIp != null  and srcIp != ''"> and src_ip like concat('%',#{srcIp},'%')</if>
            <if test="destIp != null  and destIp != ''"> and dest_ip like concat('%',#{destIp},'%')</if>
            <if test="flowState != null and flowState != ''"> and wo.flow_state = #{flowState}</if>
            <if test="handleState != null and handleState != ''"> and handle_status = #{handleState}</if>
            <if test="isAsset != null "> and ipv4 is not null</if>
            <if test="startTime != null and endTime != null">
                and ta.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        order by ta.update_time desc
    </select>

    <select id="selectVictimIpList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select ta.id,ta.threaten_name, ta.threaten_type, ta.label, ta.loss_state, ta.hand_suggest, ta.attack_ip, ta.src_port, ta.victim_ip, ta.dest_port, ta.hit_intelligence, ta.alarm_level, ta.associa_device, ta.create_by, ta.create_time, ta.update_by, ta.update_time
        ,ta.data_source,ta.alarm_num,ta.file_url,ta.dept_id,ta.dept_name,ta.handle_state,tao.asset_id as query_asset_id,tao.asset_name,tao.asset_type_desc,tao.asset_class_desc,tao.asset_id, nim.ipv4 from (
        select t1.id, threaten_name, threaten_type, t1.label, loss_state, hand_suggest, src_ip as attack_ip, src_port, dest_ip as victim_ip, dest_port, hit_intelligence, alarm_level, associa_device, t1.create_by, t1.create_time, t1.update_by, t1.update_time,
        data_source,alarm_num, t1.file_url,t1.dept_id,t1.asset_id,t1.handle_state,sd.dept_name
        from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id where
                                    threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
                                    <if test="deptId != null">
                                        and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                                    </if>
        union all
        select t1.id, threaten_name, threaten_type, t1.label, loss_state, hand_suggest, dest_ip as attack_ip, src_port, src_ip as victim_ip, dest_port, hit_intelligence, alarm_level, associa_device, t1.create_by, t1.create_time, t1.update_by, t1.update_time,
        data_source,alarm_num, t1.file_url,t1.dept_id,t1.asset_id,t1.handle_state,sd.dept_name
        from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id where
                                    threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
                                    <if test="deptId != null">
                                        and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
                                    </if>
        order by update_time desc
        ) ta
        left join (select distinct ipv4 from tbl_network_ip_mac) nim on nim.ipv4 = ta.victim_ip
        left join tbl_asset_overview tao on tao.asset_id = (select asset_id from tbl_network_ip_mac where ipv4 = ta.victim_ip GROUP BY ipv4)
        <!-- left join tbl_asset_overview tao ON tao.asset_id = tnim.asset_id
    left join tbl_work_order wo on FIND_IN_SET(ta.id,wo.event_ids) AND wo.work_type = '3'
    left join tbl_work_history wh on wh.work_id=wo.id and wo.flow_state = '4' and wh.id=(select twh.id from tbl_work_history twh where twh.work_id=wo.id order by twh.create_time desc limit 1)-->
    <where>
        <if test="threatenName != null  and threatenName != ''"> and threaten_name like concat('%', #{threatenName}, '%')</if>
        <if test="alarmLevel != null "> and alarm_level = #{alarmLevel}</if>
        <if test="srcIp != null  and srcIp != ''"> and src_ip like concat('%',#{srcIp},'%')</if>
        <if test="attackIp != null  and attackIp != ''"> and attack_ip like concat('%',#{attackIp},'%')</if>
        <if test="destIp != null  and destIp != ''"> and dest_ip like concat('%',#{destIp},'%')</if>
        <if test="victimIp != null  and victimIp != ''"> and victim_ip like concat('%',#{victimIp},'%')</if>
        <if test="flowState != null and flowState != ''"> and wo.flow_state = #{flowState}</if>
        <if test="handleState != null and handleState != ''"> and handle_status = #{handleState}</if>
        <if test="isAsset != null "> and ipv4 is not null</if>
        <if test="startTime != null and endTime != null">
            and ta.update_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </where>
</select>

<select id="getAttackSegSummary"  resultMap="TblThreatenAlarmResult">
    select ttd.attack_seg, count(distinct ta.id) as alarm_num from tbl_threaten_alarm ta  left join  threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
    <where>
        <if test="ids != null and ids.size() > 0">
            and ta.id in
            <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                #{idItem}
            </foreach>
        </if>
        <if test="disposers != null and disposers.size() > 0">
            and ta.disposer in
            <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                #{disposerItem}
            </foreach>
        </if>
        <if test="synchronizationStatus != null and synchronizationStatus != ''">
            and ta.synchronization_status = #{synchronizationStatus}
        </if>
        <if test="notIds != null and notIds.size() > 0">
            and ta.id not in
            <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                #{idItem}
            </foreach>
        </if>
        <if test="destIps != null and destIps.size() > 0">
            and ta.dest_ip in
            <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                #{ipItem}
            </foreach>
        </if>
        <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
        <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
        <if test="label != null  and label != ''"> and ta.label = #{label}</if>
        <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
        <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
        <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
        <if test="procotol != null "> and ta.procotol = #{procotol}</if>
        <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
        <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
        <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
        <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
        <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
        <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
        <if test="handleState == 99 "> and ta.handle_status is null</if>
        <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
        <if test="limitStartTime != null and limitEndTime != null "> and ta.update_time between #{limitStartTime} and #{limitEndTime}</if>
        <if test="id != null">and ta.ID = #{id}</if>
        <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
        <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
        <if test="victimIp != null and victimIp != ''"> and ta.dest_ip = #{victimIp}</if>
        <if test="attackIp != null and attackIp != ''"> and ta.src_ip = #{attackIp}</if>
        <if test="ipOr != null and ipOr != ''">
            and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
        </if>
        <if test="attackSeg != null and attackSeg != ''">
            and ttd.attack_seg = #{attackSeg}
        </if>
        <if test="ipv4List != null and ipv4List.size()>0">
            and (
            ta.src_ip in
            <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                #{ipv4Item}
            </foreach>
            OR
            ta.dest_ip in
            <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                #{ipv4Item}
            </foreach>
            )
        </if>
        <if test="workOrderIdList != null and workOrderIdList.size()>0">
            and ta.work_order_id in
            <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                #{workOrderIdItem}
            </foreach>
        </if>
        <if test="flowState != null and flowState == '99'">
            and ta.work_order_id is null
        </if>
        <if test="deviceConfigId != null">
            and ta.device_config_id = #{deviceConfigId}
        </if>
    </where>
    group by ttd.attack_seg
</select>

<select id="getAssetAttackSegSummary"  resultMap="TblThreatenAlarmResult">
    select attack_seg, count(distinct id) as alarm_num from (
          select ta.id,nim.ipv4,
                 attack_seg,
                 ta.threaten_type,
                 src_ip,
                 dest_ip,
                 ta.update_time
          from tbl_threaten_alarm ta
                   LEFT JOIN (select distinct ipv4,asset_id from tbl_network_ip_mac) nim ON nim.ipv4 = ta.src_ip OR nim.ipv4 = ta.dest_ip
                   LEFT JOIN threaten_typeseg_dict ttd on ta.threaten_type = ttd.threaten_type
                    left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
                    left join sys_dept sd on sd.dept_id = tao.dept_id
         <where>
             ipv4 is not null and ta.threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
             <if test="queryAlarmLevel != null">
                 <if test="queryAlarmLevel == 1">
                     and ta.alarm_level > 3
                 </if>
             </if>
             <if test="victimIp != null  and victimIp != ''"> and ipv4 = #{victimIp} </if>
             <if test="alarmLevel != null "> and alarm_level = #{alarmLevel}</if>
             <if test="startTime != null and endTime != null">
                 and ta.update_time BETWEEN #{startTime} AND #{endTime}
             </if>
             <if test="deptId != null">
                 and (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
             </if>
             <if test="ipv4List != null and ipv4List.size()>0">
                 and (
                 ta.src_ip in
                 <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                     #{ipv4Item}
                 </foreach>
                 OR
                 ta.dest_ip in
                 <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                     #{ipv4Item}
                 </foreach>
                 )
             </if>
             <if test="limitStartTime != null and limitEndTime != null">
                 and ta.update_time BETWEEN #{limitStartTime} AND #{limitEndTime}
             </if>
             <if test="srcIp != null and srcIp != ''">
                 and ta.src_ip = #{srcIp}
             </if>
         </where>
      ) temp
    group by attack_seg
</select>


<select id="selectIpAttackSegNum" resultMap="TblThreatenAlarmResult">
    select attack_seg, count(distinct temp.id) as alarm_num from (
        select id,src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time,alarm_level
        from tbl_threaten_alarm
        where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        union all
        select id,dest_ip as attack_ip, src_ip as victim_ip, threaten_type, update_time,alarm_level
        from tbl_threaten_alarm
        where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
    ) temp
    left join threaten_typeseg_dict ttd on temp.threaten_type = ttd.threaten_type
    <where>
        <if test="queryAlarmLevel != null">
            <if test="queryAlarmLevel == 1">
                and alarm_level > 3
            </if>
        </if>
        <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
        <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
        <if test="ipOr != null and ipOr != ''">
            and ( attack_ip = #{ipOr} or victim_ip = #{ipOr})
        </if>
        <if test="startTime != null and endTime != null">
            and update_time BETWEEN #{startTime} AND #{endTime}
        </if>
        and attack_seg is not null
    </where>
    group by ttd.attack_seg
</select>

<select id="selectAttackIpOverviewList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
    select attack_ip,attack_ip src_ip, max(temp.update_time) as update_time, sum(alarm_num) as alarm_num from (
          select src_ip as attack_ip, dest_ip as victim_ip, t1.update_time, alarm_num,t1.data_source
          from tbl_threaten_alarm t1
          left join  threaten_typeseg_dict t3 on t1.threaten_type = t3.threaten_type
          where t1.threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
            <if test="alarmLevel != null and alarmLevel != ''"> and alarm_level = #{alarmLevel} </if>
            <if test="attackSeg != null and attackSeg != ''">
                and t3.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                t1.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                t1.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
    ) temp
    <where>
        <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
        <if test="startTime != null and endTime != null">
            and temp.update_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </where>
    group by temp.attack_ip
    order by temp.update_time desc
</select>

<select id="selectVictimIpOverviewList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
    select victim_ip, max(temp.update_time) as update_time, sum(alarm_num) as alarm_num from (
          select src_ip as attack_ip, dest_ip as victim_ip, t1.update_time, alarm_num
          from tbl_threaten_alarm t1
          left join  threaten_typeseg_dict t3 on t1.threaten_type = t3.threaten_type
          where t1.threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
            <if test="alarmLevel != null and alarmLevel != ''"> and alarm_level = #{alarmLevel} </if>
            <if test="attackSeg != null and attackSeg != ''">
                and t3.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                t1.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                t1.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
          <!--union all
          select dest_ip as attack_ip, src_ip as victim_ip, t1.update_time, alarm_num,t2.dept_name
          from tbl_threaten_alarm t1
          left join sys_dept t2 on t2.dept_id = t1.dept_id
          left join  threaten_typeseg_dict t3 on t1.threaten_type = t3.threaten_type
          where t1.threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
            <if test="alarmLevel != null and alarmLevel != ''"> and alarm_level = #{alarmLevel} </if>
            <if test="deptId != null">
                and (t2.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, t2.ancestors))
            </if>
            <if test="attackSeg != null and attackSeg != ''">
                and t3.attack_seg = #{attackSeg}
            </if>-->
    ) temp
    <where>
        <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
        <if test="startTime != null and endTime != null">
            and update_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </where>
    group by victim_ip
    order by update_time desc
</select>

<select id="selectAssetIpOverviewList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
    SELECT t.ipv4 as victim_ip,t.ipv4 as src_ip, t.update_time update_time
    FROM (
        select nim.dept_id, nim.ipv4, src_ip, nim.asset_id,sd.dept_name , MAX(ta.update_time) update_time  from tbl_network_ip_mac nim
        LEFT JOIN tbl_threaten_alarm ta ON nim.ipv4 = ta.src_ip OR nim.ipv4 = ta.dest_ip
        LEFT JOIN tbl_asset_overview s ON s.asset_id = nim.asset_id
        left join sys_dept sd on sd.dept_id = s.dept_id

        WHERE ( src_ip IS NOT NULL OR dest_ip IS NOT NULL )
        <if test="queryAlarmLevel != null">
            <if test="queryAlarmLevel == 1">
                and ta.alarm_level > 3
            </if>
        </if>
        <if test="alarmLevel != null "> and alarm_level = #{alarmLevel}</if>
        <if test="threatenTypeList != null and threatenTypeList.size()>0">
            and ta.threaten_type in
            <foreach collection="threatenTypeList" item="threatenTypeItem" open="(" separator="," close=")">
                #{threatenTypeItem}
            </foreach>
        </if>
        <if test="victimIp != null  and victimIp != ''"> and nim.ipv4 = #{victimIp}</if>
        <if test="startTime != null and endTime != null">
            and ta.update_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="ipv4List != null and ipv4List.size()>0">
            and (
            nim.ipv4 in
            <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                #{ipv4Item}
            </foreach>
            )
        </if>
        GROUP BY nim.ipv4
    ) t
    order by t.update_time DESC
</select>

<select id="selectAttackIpReportList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
    select group_concat(distinct threaten_name) as threaten_name, ta.attack_ip, ta.victim_ip, ta.victim_port, nim.ipv4 from (
        select id, threaten_name, threaten_type, src_ip as attack_ip, src_port as attack_port, dest_ip as victim_ip, dest_port as victim_port, update_time
        from tbl_threaten_alarm where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        union all
        select id, threaten_name, threaten_type, dest_ip as attack_ip, dest_port as attack_port, src_ip as victim_ip, src_port as victim_port, update_time
        from tbl_threaten_alarm where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        ) ta
        left join (select distinct ipv4 from tbl_network_ip_mac) nim on nim.ipv4 = ta.victim_ip
    <where>
        <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
        <if test="startTime != null and endTime != null "> and update_time between #{startTime} and #{endTime}</if>
        <if test="limitStartTime != null and limitEndTime != null "> and update_time between #{limitStartTime} and #{limitEndTime}</if>
    </where>
    group by ta.attack_ip, ta.victim_ip, ta.victim_port, nim.ipv4
    order by update_time desc
</select>

<select id="selectVictimIpReportList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
    select  group_concat(distinct threaten_name) as threaten_name, ta.attack_ip, ta.victim_ip, ta.victim_port, nim.ipv4 from (
        select id, threaten_name, threaten_type, src_ip as attack_ip, dest_ip as victim_ip, dest_port as victim_port, update_time
        from tbl_threaten_alarm where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        union all
        select id, threaten_name, threaten_type, dest_ip as attack_ip,  src_ip as victim_ip, src_port as victim_port, update_time
        from tbl_threaten_alarm where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        ) ta
        left join (select distinct ipv4 from tbl_network_ip_mac) nim on nim.ipv4 = ta.attack_ip
    <where>
        <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
        <if test="startTime != null and endTime != null "> and update_time between #{startTime} and #{endTime}</if>
        <if test="limitStartTime != null and limitEndTime != null "> and update_time between #{limitStartTime} and #{limitEndTime}</if>
    </where>
    group by ta.attack_ip, ta.victim_ip, ta.victim_port, nim.ipv4
    order by update_time desc
</select>

<select id="selectOutConnectReportList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
    select dest_ip as attack_ip, dest_port as attack_port,  src_ip as victim_ip
    from tbl_threaten_alarm
    <where>
        threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        <if test="victimIp != null  and victimIp != ''"> and src_ip = #{victimIp}</if>
        <if test="startTime != null and endTime != null "> and update_time between #{startTime} and #{endTime}</if>
        <if test="limitStartTime != null and limitEndTime != null "> and update_time between #{limitStartTime} and #{limitEndTime}</if>
    </where>
    group by attack_ip, attack_port, victim_ip
    order by update_time desc
</select>

<select id="selectAttackIpVictimStatReport" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
     select attack_ip,  COUNT(distinct victim_ip) alarm_num  from (
        select threaten_name, alarm_level, src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time
        from tbl_threaten_alarm
        where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        union all
        select threaten_name, alarm_level, dest_ip as attack_ip, src_ip as victim_ip,  threaten_type, update_time
        from tbl_threaten_alarm
        where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
     ) temp
     <where>
          <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
          <if test="startTime != null and endTime != null "> and update_time between #{startTime} and #{endTime}</if>
          <if test="limitStartTime != null and limitEndTime != null "> and update_time between #{limitStartTime} and #{limitEndTime}</if>
          and victim_ip is not null
     </where>
     order by update_time desc
</select>

<select id="selectVictimIpAttackStatReport" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
    select victim_ip,  COUNT(distinct attack_ip) alarm_num  from (
        select threaten_name, alarm_level, src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time
        from tbl_threaten_alarm
        where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        union all
        select threaten_name, alarm_level, dest_ip as attack_ip, src_ip as victim_ip, threaten_type, update_time
        from tbl_threaten_alarm
        where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
    ) temp
    <where>
        <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
        <if test="startTime != null and endTime != null "> and update_time between #{startTime} and #{endTime}</if>
        <if test="limitStartTime != null and limitEndTime != null "> and update_time between #{limitStartTime} and #{limitEndTime}</if>
        and attack_ip is not null
    </where>
    order by update_time desc
</select>


<select id="selectTimeAxisList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
    select ta.id,ta.threaten_name, ta.threaten_type, ta.attack_ip, ta.src_port, ta.victim_ip, ta.dest_port, ta.alarm_level, ta.update_time, nim.ipv4, nim2.ipv4 isIpv4 from (
        select t1.id, t1.threaten_name, t1.threaten_type, t1.src_ip as attack_ip, t1.src_port, t1.dest_ip as victim_ip, t1.dest_port,  t1.alarm_level, t1.update_time
        from tbl_threaten_alarm t1 left join threaten_typeseg_dict t2 on t1.threaten_type = t2.threaten_type
        where t1.threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        <if test="attackSeg != null and attackSeg != ''">
            and t2.attack_seg = #{attackSeg}
        </if>
        union all
        select t1.id, t1.threaten_name, t1.threaten_type, t1.dest_ip as attack_ip, t1.src_port, t1.src_ip as victim_ip, t1.dest_port,  t1.alarm_level,  t1.update_time
        from tbl_threaten_alarm t1 left join threaten_typeseg_dict t2 on t1.threaten_type = t2.threaten_type
        where t1.threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        <if test="attackSeg != null and attackSeg != ''">
            and t2.attack_seg = #{attackSeg}
        </if>
        ) ta
        left join (select distinct ipv4 from tbl_network_ip_mac) nim on nim.ipv4 = ta.victim_ip
        left join (select distinct ipv4 from tbl_network_ip_mac) nim2 on nim2.ipv4 = ta.attack_ip
    <where>
        <if test="attackIp != null  and attackIp != ''"> and (attack_ip = #{attackIp} or victim_ip = #{attackIp})</if>
        <if test="startTime != null and endTime != null "> and update_time between #{startTime} and #{endTime}</if>
        <if test="limitStartTime != null and limitEndTime != null "> and update_time between #{limitStartTime} and #{limitEndTime}</if>
    </where>
    order by update_time Asc
</select>

<!-- 攻击阶段  列表查询-->
    <select id="getEventSegTypeList" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select attack_seg, count(1) as alarm_num from (
        select distinct t1.id,t1.src_ip as attack_ip, t1.dest_ip as victim_ip, t1.threaten_type, t1.update_time
        from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id
        where t1.threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        <if test="deptId != null">
            and (sd.dept_id=#{deptId} OR find_in_set(#{deptId}, sd.ancestors))
        </if>
        <!--union all
        select distinct t1.id,t1.dest_ip as attack_ip, t1.src_ip as victim_ip,  t1.threaten_type, t1.update_time
        from tbl_threaten_alarm t1
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id
        where t1.threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        <if test="deptId != null">
            and (sd.dept_id=#{deptId} OR find_in_set(#{deptId}, sd.ancestors))
        </if>-->
        ) temp
        left join threaten_typeseg_dict ttd on temp.threaten_type = ttd.threaten_type
        <where>
            <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
            <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
            <if test="startTime != null and endTime != null">
                and update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            and attack_seg is not null
        </where>
        group by ttd.attack_seg
    </select>

    <select id="getGroupByName" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select threaten_name, COUNT(*) alarm_num from (
              select threaten_name, alarm_level, src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time
              from tbl_threaten_alarm
              where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
              union all
              select threaten_name, alarm_level, dest_ip as attack_ip, src_ip as victim_ip,  threaten_type, update_time
              from tbl_threaten_alarm
              where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
          ) temp
        <where>
            <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
            <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
            <if test="ipOr != null and ipOr != ''">
                and ( attack_ip = #{ipOr} or victim_ip = #{ipOr})
            </if>
            <if test="srcIp != null and srcIp != ''">
                and ( attack_ip = #{srcIp} or victim_ip = #{srcIp})
            </if>
            <if test="startTime != null and endTime != null">
                and update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="limitStartTime != null and limitEndTime != null">
                and update_time BETWEEN #{limitStartTime} AND #{limitEndTime}
            </if>
        </where>
        GROUP BY threaten_name
    </select>

    <select id="getGroupByLevel" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select alarm_level, COUNT(*) alarm_num  from (
             select threaten_name, alarm_level, src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time
             from tbl_threaten_alarm
             where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
             union all
             select threaten_name, alarm_level, dest_ip as attack_ip, src_ip as victim_ip,  threaten_type, update_time
             from tbl_threaten_alarm
             where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
         ) temp
        <where>
            <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
            <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
            <if test="ipOr != null and ipOr != ''">
                and ( attack_ip = #{ipOr} or victim_ip = #{ipOr})
            </if>
            <if test="srcIp != null and srcIp != ''">
                and ( attack_ip = #{srcIp} or victim_ip = #{srcIp})
            </if>
            <if test="startTime != null and endTime != null">
                and update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="limitStartTime != null and limitEndTime != null">
                and update_time BETWEEN #{limitStartTime} AND #{limitEndTime}
            </if>
        </where>
        GROUP BY alarm_level
    </select>

    <select id="getGroupByAttack" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select attack_ip, COUNT(*) alarm_num,nim.asset_id  from (
           select threaten_name, alarm_level, src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time
           from tbl_threaten_alarm
           where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
           union all
           select threaten_name, alarm_level, dest_ip as attack_ip, src_ip as victim_ip,  threaten_type, update_time
           from tbl_threaten_alarm
           where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
       ) temp
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on nim.ipv4 = temp.attack_ip
        <where>
            <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
            <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
            <if test="ipOr != null and ipOr != ''">
                and ( attack_ip = #{ipOr} or victim_ip = #{ipOr})
            </if>
            <if test="srcIp != null and srcIp != ''">
                and ( attack_ip = #{srcIp} or victim_ip = #{srcIp})
            </if>
            <if test="startTime != null and endTime != null">
                and update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="limitStartTime != null and limitEndTime != null">
                and update_time BETWEEN #{limitStartTime} AND #{limitEndTime}
            </if>
        </where>
        GROUP BY attack_ip
    </select>

    <select id="getGroupByVictim" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select victim_ip, COUNT(*) alarm_num,nim.asset_id  from (
           select threaten_name, alarm_level, src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time
           from tbl_threaten_alarm
           where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
           union all
           select threaten_name, alarm_level, dest_ip as attack_ip, src_ip as victim_ip,  threaten_type, update_time
           from tbl_threaten_alarm
           where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
       ) temp
        left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on nim.ipv4 = temp.victim_ip
        <where>
            <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
            <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
            <if test="ipOr != null and ipOr != ''">
                and ( attack_ip = #{ipOr} or victim_ip = #{ipOr})
            </if>
            <if test="srcIp != null and srcIp != ''">
                and ( attack_ip = #{srcIp} or victim_ip = #{srcIp})
            </if>
            <if test="startTime != null and endTime != null">
                and update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="limitStartTime != null and limitEndTime != null">
                and update_time BETWEEN #{limitStartTime} AND #{limitEndTime}
            </if>
        </where>
        GROUP BY victim_ip
    </select>

    <select id="exportAttackStage" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select attack_ip, attack_seg, count(1) as alarm_num from (
             select src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time
             from tbl_threaten_alarm
             where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
             union all
             select dest_ip as attack_ip, src_ip as victim_ip,  threaten_type, update_time
             from tbl_threaten_alarm
             where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
     ) temp
         left join threaten_typeseg_dict ttd on temp.threaten_type = ttd.threaten_type
        <where>
            attack_seg is not null
            <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
            <if test="startTime != null and endTime != null">
                and update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        group by ttd.attack_seg, attack_ip
    </select>

    <select id="exportSufferStage" parameterType="TblThreatenAlarm" resultMap="TblThreatenAlarmResult">
        select victim_ip, attack_seg, count(1) as alarm_num from (
             select src_ip as attack_ip, dest_ip as victim_ip, threaten_type, update_time
             from tbl_threaten_alarm
             where threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
             union all
             select dest_ip as attack_ip, src_ip as victim_ip,  threaten_type, update_time
             from tbl_threaten_alarm
             where threaten_type in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
         ) temp
             left join threaten_typeseg_dict ttd on temp.threaten_type = ttd.threaten_type
        <where>
            attack_seg is not null
            <if test="victimIp != null  and victimIp != ''"> and victim_ip = #{victimIp}</if>
            <if test="startTime != null and endTime != null">
                and update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        group by ttd.attack_seg, victim_ip
    </select>

    <select id="selectThreatenByIpList" resultType="com.ruoyi.threaten.domain.TblThreatenAlarm">
        <include refid="selectTblThreatenAlarmStateVo"/>
        where (src_ip in
        <foreach collection="ipList" item="ip" open="(" separator="," close=")">
            #{ip}
        </foreach>
        or dest_ip in
        <foreach collection="ipList" item="ip" open="(" separator="," close=")">
            #{ip}
        </foreach>) and ta.handle_state=0
    </select>

    <select id="selectFfsafeHoneypotAttackdetailList"
            resultType="com.ruoyi.threaten.domain.FfsafeHoneypotAttackdetail" resultMap="FfsafeHoneypotAttackdetailResult">
        SELECT
            t1.*,nim.asset_id query_asset_id
        FROM
            `ffsafe_honeypot_attackdetail` t1
            left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.src_ip OR nim.ipv4 = t1.dest_ip)
        <where>
            <if test="eventName != null and eventName != ''">
                and event_name = #{eventName}
            </if>
            <if test="srcIp != null and srcIp != ''">
                and src_ip = #{srcIp}
            </if>
            <if test="destIp != null and destIp != ''">
                and dest_ip = #{destIp}
            </if>
            <if test="destPort != null">
                and dest_port = #{destPort}
            </if>
        </where>
        ORDER BY t1.timeline desc
    </select>

    <select id="getThreatTrend" resultType="com.alibaba.fastjson2.JSONObject">
        <!--SELECT
            <if test="type == 1">
            CONCAT(year(t1.update_time),WEEK(t1.update_time,1)) AS `dimension`,
            </if>
            COUNT(DISTINCT t1.id) `count`
        FROM
            tbl_threaten_alarm t1
            left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on (nim.ipv4 = t1.dest_ip OR nim.ipv4 = t1.src_ip)
            left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
            left join sys_dept sd on sd.dept_id = tao.dept_id
        <where>
            <if test="startDate != null and endDate != null">
                AND t1.update_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="deptId != null">
                AND (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
            </if>
        </where>
        <if test="type == 1">
            GROUP BY
            CONCAT(year(t1.update_time),WEEK(t1.update_time,1))
        </if>-->
        <if test="deptId == null">
            SELECT
            dimension,
            COUNT(DISTINCT id) AS count
            FROM (
            SELECT
            <if test="type == 1">
                CONCAT(year(t1.update_time),WEEK(t1.update_time,1)) AS `dimension`,
            </if>
            t1.id
            FROM tbl_threaten_alarm t1
            <where>
                <if test="startDate != null and endDate != null">
                    AND t1.update_time BETWEEN #{startDate} AND #{endDate}
                </if>
            </where>
            UNION ALL
            SELECT
            <if test="type == 1">
                CONCAT(year(t1.update_time),WEEK(t1.update_time,1)) AS `dimension`,
            </if>
            t1.id
            FROM tbl_threaten_alarm t1
            <where>
                <if test="startDate != null and endDate != null">
                    AND t1.update_time BETWEEN #{startDate} AND #{endDate}
                </if>
            </where>
            ) AS combined_data
            GROUP BY dimension;
        </if>
        <if test="deptId != null">
            SELECT
            dimension,
            COUNT(DISTINCT id) AS count
            FROM (
            SELECT
            <if test="type == 1">
                CONCAT(year(t1.update_time),WEEK(t1.update_time,1)) AS `dimension`,
            </if>
            t1.id
            FROM tbl_threaten_alarm t1
            LEFT JOIN (SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac) nim
            ON nim.ipv4 = t1.dest_ip
            LEFT JOIN tbl_asset_overview tao
            ON tao.asset_id = nim.asset_id
            LEFT JOIN sys_dept sd
            ON sd.dept_id = tao.dept_id
            <where>
                <if test="startDate != null and endDate != null">
                    AND t1.update_time BETWEEN #{startDate} AND #{endDate}
                </if>
                <if test="deptId != null">
                    AND (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
                </if>
            </where>
            UNION ALL
            SELECT
            <if test="type == 1">
                CONCAT(year(t1.update_time),WEEK(t1.update_time,1)) AS `dimension`,
            </if>
            t1.id
            FROM tbl_threaten_alarm t1
            LEFT JOIN (SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac) nim
            ON nim.ipv4 = t1.src_ip
            LEFT JOIN tbl_asset_overview tao
            ON tao.asset_id = nim.asset_id
            LEFT JOIN sys_dept sd
            ON sd.dept_id = tao.dept_id
            <where>
                <if test="startDate != null and endDate != null">
                    AND t1.update_time BETWEEN #{startDate} AND #{endDate}
                </if>
                <if test="deptId != null">
                    AND (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
                </if>
            </where>
            ) AS combined_data
            GROUP BY dimension;
        </if>
    </select>
    <select id="getVulnTrend" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT temp.*,SUM(temp.count) `count` FROM
            (SELECT
                <if test="type == 1">CONCAT(year(t1.update_time),WEEK(t1.update_time,1)) AS `dimension`,</if>COUNT(DISTINCT t1.id) `count`
             FROM
                 monitor_bss_vuln_deal t1
                 left join (select distinct ipv4,asset_id from tbl_network_ip_mac) nim on nim.ipv4 = t1.host_ip
                 left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
                 left join sys_dept sd on sd.dept_id = tao.dept_id
             <where>
                 <if test="startDate != null and endDate != null">
                     AND t1.update_time BETWEEN #{startDate} AND #{endDate}
                 </if>
                 <if test="deptId != null">
                     AND (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
                 </if>
             </where>
             <if test="type == 1">
                 GROUP BY
                 CONCAT(year(t1.update_time),WEEK(t1.update_time,1))
             </if>
             UNION
             SELECT
                <if test="type == 1">CONCAT(year(t1.update_time),WEEK(t1.update_time,1)) AS `dimension`,</if>COUNT(DISTINCT t1.id) `count`
             FROM
                 monitor_bss_webvuln_deal t1
                 left join tbl_business_application tba on t1.web_url = tba.url
                 left join sys_dept sd on sd.dept_id = tba.dept_id
             <where>
                 <if test="startDate != null and endDate != null">
                     AND t1.update_time BETWEEN #{startDate} AND #{endDate}
                 </if>
                 <if test="deptId != null">
                     AND (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
                 </if>
             </where>
             <if test="type == 1">
                 GROUP BY
                 CONCAT(year(t1.update_time),WEEK(t1.update_time,1))
             </if>
             ) temp
        GROUP BY
            temp.`dimension`
    </select>
    <select id="getAttackIpListTop5" resultType="com.ruoyi.threaten.domain.TblThreatenAlarm">
        <if test="deptId == null">
            SELECT
            pre.alarmNum,
            pre.srcIp
            FROM (
            SELECT
            src_ip AS srcIp,
            SUM(DISTINCT alarm_num) AS alarmNum
            FROM tbl_threaten_alarm t1
            GROUP BY src_ip
            ORDER BY alarmNum DESC
            LIMIT 5
            ) pre
            LEFT JOIN tbl_network_ip_mac nim ON nim.ipv4 = pre.srcIp
            LEFT JOIN tbl_server t2 ON t2.asset_id=nim.asset_id
            LEFT JOIN sys_dept sd ON sd.dept_id=t2.dept_id
        </if>
        <if test="deptId != null">
            SELECT
            SUM(DISTINCT t1.alarm_num ) as alarmNum,
            t1.src_ip as srcIp
            FROM
            `tbl_threaten_alarm` t1
            LEFT JOIN tbl_network_ip_mac nim ON nim.ipv4=t1.src_ip
            LEFT JOIN tbl_server t2 ON t2.asset_id=nim.asset_id
            LEFT JOIN sys_dept sd ON sd.dept_id=t2.dept_id
            <where>
                <if test="deptId != null">
                    AND (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
                </if>
            </where>
            GROUP BY
            t1.src_ip
            ORDER BY
            SUM(DISTINCT t1.alarm_num ) DESC
            LIMIT 5
        </if>
    </select>
    <select id="getDestIpListTop5" resultType="com.ruoyi.threaten.domain.TblThreatenAlarm">
        <if test="deptId == null">
            SELECT
            SUM( DISTINCT t1.alarm_num ) AS alarmNum,
            t1.dest_ip AS destIp
            FROM
            `tbl_threaten_alarm` t1
            GROUP BY
            t1.dest_ip
            ORDER BY
            SUM( DISTINCT t1.alarm_num ) DESC
            LIMIT 5
        </if>
        <if test="deptId != null">
            SELECT
            SUM(DISTINCT t1.alarm_num ) as alarmNum,
            t1.dest_ip as destIp
            FROM
            `tbl_threaten_alarm` t1
            LEFT JOIN tbl_network_ip_mac nim ON nim.ipv4=t1.dest_ip
            LEFT JOIN tbl_server t2 ON t2.asset_id=nim.asset_id
            LEFT JOIN sys_dept sd ON sd.dept_id=t2.dept_id
            <where>
                <if test="deptId != null">
                    AND (sd.dept_id=#{deptId} OR find_in_set(#{deptId},sd.ancestors))
                </if>
            </where>
            GROUP BY
            t1.dest_ip
            ORDER BY
            SUM(DISTINCT t1.alarm_num ) DESC
            LIMIT 5
        </if>
    </select>

    <select id="getAlarmCountsByIps" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        combined_ips.query_ip as ip,
        SUM(CASE WHEN combined_ips.alarm_level = 0 THEN 1 ELSE 0 END) AS unknown,
        SUM(CASE WHEN combined_ips.alarm_level = 1 THEN 1 ELSE 0 END) AS noThreat,
        SUM(CASE WHEN combined_ips.alarm_level = 2 THEN 1 ELSE 0 END) AS low,
        SUM(CASE WHEN combined_ips.alarm_level = 3 THEN 1 ELSE 0 END) AS medium,
        SUM(CASE WHEN combined_ips.alarm_level = 4 THEN 1 ELSE 0 END) AS high,
        SUM(CASE WHEN combined_ips.alarm_level = 5 THEN 1 ELSE 0 END) AS critical
        FROM (
        SELECT src_ip AS query_ip, alarm_level
        FROM tbl_threaten_alarm
        WHERE src_ip IN
        <foreach collection="ipList" item="ip" separator="," open="(" close=")">
            #{ip}
        </foreach>
        UNION ALL
        SELECT dest_ip AS query_ip, alarm_level
        FROM tbl_threaten_alarm
        WHERE dest_ip IN
        <foreach collection="ipList" item="ip" separator="," open="(" close=")">
            #{ip}
        </foreach>
        ) AS combined_ips
        GROUP BY combined_ips.query_ip
    </select>


    <select id="getAlarmCountsByAssetIds" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            tba.asset_id as assetId,
            SUM(CASE WHEN vd.alarm_level = 0 THEN 1 ELSE 0 END) AS unknown,
            SUM(CASE WHEN vd.alarm_level = 1 THEN 1 ELSE 0 END) AS noThreat,
            SUM(CASE WHEN vd.alarm_level = 2 THEN 1 ELSE 0 END) AS low,
            SUM(CASE WHEN vd.alarm_level = 3 THEN 1 ELSE 0 END) AS medium,
            SUM(CASE WHEN vd.alarm_level = 4 THEN 1 ELSE 0 END) AS high,
            SUM(CASE WHEN vd.alarm_level = 5 THEN 1 ELSE 0 END) AS critical
        FROM
            tbl_threaten_alarm vd
                LEFT JOIN tbl_network_ip_mac im ON (vd.src_ip = im.ipv4 OR vd.dest_ip = im.ipv4)
                LEFT JOIN tbl_application_server t3 ON t3.server_id = im.asset_id
                LEFT JOIN tbl_business_application tba ON tba.asset_id = t3.asset_id
        WHERE
            tba.asset_id in
            <foreach collection="assetIds" item="assetId" open="(" close=")" separator=",">
                #{assetId}
            </foreach>
        AND im.main_ip = 1
        group by assetId
    </select>
    <select id="selectThreatenTypeByAttackSeg" resultType="java.lang.String">
        select threaten_type from threaten_typeseg_dict where attack_seg = #{attackSeg}
    </select>
    <select id="selectNotSyncList" resultType="com.ruoyi.threaten.domain.TblThreatenAlarm" resultMap="TblThreatenAlarmBaseResult">
        select * from tbl_threaten_alarm where (synchronization_status = 0 or synchronization_status is null)
        and alarm_level > 2
        <if test="startDate != null">
            and update_time &gt;= #{startDate}
        </if>
    </select>

    <select id="countThreatenAlarmNum" resultType="java.lang.Integer">
        SELECT
        COUNT( t1.id )
        FROM
        tbl_threaten_alarm t1
        <if test="deptId != 100">
            RIGHT JOIN(
            SELECT
            im.asset_id,
            im.ipv4,
            GROUP_CONCAT( sd.dept_name ) dept_name,
            sd.dept_id,
            t2.asset_name,
            t2.asset_type_desc,
            t2.asset_class_desc
            FROM
            tbl_network_ip_mac im
            LEFT JOIN tbl_asset_overview t2 ON t2.asset_id = im.asset_id
            LEFT JOIN sys_dept sd ON sd.dept_id = t2.dept_id
            WHERE
            (sd.dept_id=#{deptId} OR FIND_IN_SET(#{deptId},sd.ancestors))
            GROUP BY
            im.asset_id
            ) t2 ON (t1.src_ip = t2.ipv4 OR t1.dest_ip = t2.ipv4)
        </if>
        <where>
            <if test="startTime != null and endTime != null">
                AND t1.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>

    <select id="countThreatenNotificationNum" resultType="java.lang.Integer">

    </select>
    <select id="groupAlarmLevelStatistics" resultType="com.alibaba.fastjson2.JSONObject">
        select count(1) as alarmNum, alarm_level from tbl_threaten_alarm ta
        <where>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="startTime != null and endTime != null">
                and ta.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                src_ip in
                <foreach collection="ipv4List" item="ipv4" separator="," open="(" close=")">
                    #{ipv4}
                </foreach>
                OR
                dest_ip in
                <foreach collection="ipv4List" item="ipv4" separator="," open="(" close=")">
                    #{ipv4}
                </foreach>
                )
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="deviceConfigId != null">
                and ta.device_config_id = #{deviceConfigId}
            </if>
        </where>
        group by alarm_level
    </select>

    <select id="selectTblThreatenAlarmListDeputy" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        tbl_threaten_alarm ta
        <where>
            <if test="ids != null and ids.size() > 0">
                and ta.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and ta.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and ta.dest_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="attackSeg != null and attackSeg != ''">
                and tyd.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                ta.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                ta.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
        </where>
        ORDER BY
        ta.update_time DESC
    </select>

    <select id="countAttackIpOverview" resultType="java.lang.Integer">
        select count(distinct temp.attack_ip) from (
        select src_ip as attack_ip, dest_ip as victim_ip, t1.update_time, alarm_num,t1.data_source
        from tbl_threaten_alarm t1
        left join  threaten_typeseg_dict t3 on t1.threaten_type = t3.threaten_type
        where t1.threaten_type not in ('其他/网络异常/主机外联', '其他/网络异常/内网外联')
        <if test="alarmLevel != null and alarmLevel != ''"> and alarm_level = #{alarmLevel} </if>
        <if test="attackSeg != null and attackSeg != ''">
            and t3.attack_seg = #{attackSeg}
        </if>
        ) temp
        LEFT JOIN (select distinct ipv4,asset_id from tbl_network_ip_mac) nim ON nim.ipv4 = temp.attack_ip
        left join tbl_asset_overview tao on tao.asset_id=nim.asset_id
        left join sys_dept sd on sd.dept_id = tao.dept_id
        <where>
            <if test="attackIp != null  and attackIp != ''"> and attack_ip = #{attackIp}</if>
            <if test="startTime != null and endTime != null">
                and temp.update_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="deptId != null and deptId != 100">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        order by temp.update_time desc
    </select>

    <select id="selectTop10Alarm" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        ta.threaten_name AS threatenName,
        COUNT(ta.threaten_name) AS num
        FROM
        tbl_threaten_alarm ta
        <where>
            <if test="ids != null and ids.size() > 0">
                and ta.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and ta.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and ta.dest_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="attackSeg != null and attackSeg != ''">
                and tyd.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                ta.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                ta.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
        </where>
        GROUP BY
        ta.threaten_name
        ORDER BY
        COUNT(ta.threaten_name) DESC
        LIMIT 10
    </select>

    <select id="selectAlarmTypeNum" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        ta.threaten_type AS threatenType,
        COUNT(ta.threaten_type) AS num
        FROM
        tbl_threaten_alarm ta
        <where>
            <if test="ids != null and ids.size() > 0">
                and ta.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and ta.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and ta.dest_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="attackSeg != null and attackSeg != ''">
                and tyd.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                ta.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                ta.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
        </where>
        GROUP BY
        ta.threaten_type
        ORDER BY
        COUNT(ta.threaten_type) DESC
        LIMIT 5
    </select>

    <select id="selectAlarmLevelStatistics" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        sdd.dict_label as alarmLevel,
        COUNT(ta.alarm_level) as num
        FROM
        tbl_threaten_alarm ta
        LEFT JOIN (
        SELECT * FROM sys_dict_data WHERE dict_type = 'threaten_type' AND dict_value NOT IN(0,1)
        ) sdd ON ta.alarm_level = sdd.dict_value
        <where>
            <if test="ids != null and ids.size() > 0">
                and ta.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and ta.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and ta.dest_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="attackSeg != null and attackSeg != ''">
                and tyd.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                ta.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                ta.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
        </where>
        GROUP BY
        ta.alarm_level
    </select>

    <select id="selectRecentlyBlockedList" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.ip,
            COUNT(t1.ip) AS num
        FROM(
                SELECT
                    a.*
                FROM
                    ffsafe_ipfilter_log a
                WHERE
                    a.action = '阻断'
                GROUP BY
                    a.create_time) t1
        <where>
            <if test="startTime != null and endTime != null ">
                and t1.create_time between #{startTime} and #{endTime}
            </if>
        </where>
        GROUP BY
            t1.ip
        order by
        COUNT(t1.ip) desc
        limit 5
    </select>

    <select id="selectAttackIpOverviewTop10" resultType="com.alibaba.fastjson2.JSONObject">
        <!--SELECT
            t1.ip,
            COUNT(t1.ip) AS num
        FROM(
                SELECT
                    a.*
                FROM
                    ffsafe_ipfilter_log a
                WHERE
                    a.action = '阻断'
                GROUP BY
                    a.create_time) t1
        <where>
            <if test="startTime != null and endTime != null ">
                and t1.create_time between #{startTime} and #{endTime}
            </if>
        </where>
        GROUP BY
            t1.ip
        ORDER BY
            COUNT(t1.ip) DESC
            LIMIT 10-->
        SELECT
        ta.src_ip ip,
        COUNT(ta.src_ip) num
        FROM
        tbl_threaten_alarm ta
        LEFT JOIN ( SELECT DISTINCT ipv4, asset_id FROM tbl_network_ip_mac ) nim ON nim.ipv4 = ta.src_ip
        LEFT JOIN tbl_asset_overview tao ON tao.asset_id = nim.asset_id
        LEFT JOIN sys_dept sd ON sd.dept_id = tao.dept_id
        <where>
            <if test="ids != null and ids.size() > 0">
                and ta.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and ta.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and ta.dest_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                ta.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                ta.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
            <if test="deptId != null and deptId != 100">
                and (sd.dept_id = #{deptId} OR FIND_IN_SET(#{deptId}, sd.ancestors))
            </if>
        </where>
        GROUP BY
        ta.src_ip
        ORDER BY
        COUNT(ta.src_ip) DESC
        LIMIT 10;
    </select>

    <select id="getRankingOfAlarmSystems" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
            t1.asset_name assetName,
        IF(t2.num IS NULL,0,t2.num) num,
        IF(t2.ipv4Num IS NULL,0,t2.ipv4Num) ipv4Num
        FROM(
                SELECT
                    a.asset_id,
                    a.asset_name,
                    a.url
                FROM
                    tbl_business_application a
                        LEFT JOIN sys_user m ON a.manager = m.user_id
                        LEFT JOIN sys_user b ON a.user_id = b.user_id
                        LEFT JOIN sys_dept c ON a.dept_id = c.dept_id
                        LEFT JOIN tbl_vendor d ON a.vendor = d.id
                        LEFT JOIN tbl_asset_overview e ON a.asset_id = e.asset_id
                <where>
                    <if test="deptId != null and deptId != 100">
                        and (c.dept_id=#{deptId} OR find_in_set(#{deptId},c.ancestors))
                    </if>
                </where>
                GROUP BY
                    a.asset_id
            ) t1
                LEFT JOIN(
            SELECT
                tas.asset_id,
                COUNT(tta.id) num,
                tas.ipv4Num
            FROM(
                    SELECT
                        tas.asset_id,
                        tas.server_id,
                        GROUP_CONCAT( nim.ipv4 SEPARATOR ',' ) AS ipv4_list,
                        COUNT(nim.ipv4) ipv4Num
                    FROM
                        tbl_application_server tas
                            LEFT JOIN tbl_network_ip_mac nim ON nim.asset_id = tas.server_id
                    WHERE
                        tas.asset_id IS NOT NULL
                    GROUP BY
                        tas.asset_id
                ) tas LEFT JOIN tbl_threaten_alarm tta ON FIND_IN_SET( tta.dest_ip, tas.ipv4_list )
            <where>
                tta.handle_state != 1
                <if test="startTime != null and endTime != null ">
                    and tta.update_time between #{startTime} and #{endTime}
                </if>
            </where>
            GROUP BY
                tas.asset_id
        ) t2 ON t1.asset_id = t2.asset_id
        ORDER BY
            t2.num DESC
        limit 5
    </select>

    <select id="getHostOverview" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        t1.highRiskNum,
        t2.riskHostsNum
        FROM(
        SELECT
        COUNT(DISTINCT ta.dest_ip) highRiskNum
        FROM
        tbl_threaten_alarm ta
        <where>
            ta.alarm_level = 5
            <if test="ids != null and ids.size() > 0">
                and ta.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and ta.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and ta.dest_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="attackSeg != null and attackSeg != ''">
                and tyd.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                ta.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                ta.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
        </where>
        ) t1 LEFT JOIN (
        SELECT
        COUNT(DISTINCT dest_ip) riskHostsNum
        FROM
        tbl_server ts
        LEFT JOIN tbl_network_ip_mac tnim ON tnim.asset_id = ts.asset_id
        LEFT JOIN tbl_threaten_alarm ta ON tnim.ipv4 = ta.dest_ip
        <where>
            ta.alarm_level NOT IN (5)
            <if test="ids != null and ids.size() > 0">
                and ta.id in
                <foreach collection="ids" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="disposers != null and disposers.size() > 0">
                and ta.disposer in
                <foreach collection="disposers" item="disposerItem" open="(" separator="," close=")">
                    #{disposerItem}
                </foreach>
            </if>
            <if test="synchronizationStatus != null and synchronizationStatus != ''">
                and ta.synchronization_status = #{synchronizationStatus}
            </if>
            <if test="notIds != null and notIds.size() > 0">
                and ta.id not in
                <foreach collection="notIds" item="idItem" open="(" separator="," close=")">
                    #{idItem}
                </foreach>
            </if>
            <if test="destIps != null and destIps.size() > 0">
                and ta.dest_ip in
                <foreach collection="destIps" item="ipItem" open="(" separator="," close=")">
                    #{ipItem}
                </foreach>
            </if>
            <if test="threatenName != null  and threatenName != ''"> and ta.threaten_name like concat('%', #{threatenName}, '%')</if>
            <if test="threatenType != null  and threatenType != ''"> and ta.threaten_type = #{threatenType}</if>
            <if test="label != null  and label != ''"> and ta.label = #{label}</if>
            <if test="lossState != null  and lossState != ''"> and ta.loss_state = #{lossState}</if>
            <if test="handSuggest != null  and handSuggest != ''"> and ta.hand_suggest = #{handSuggest}</if>
            <if test="srcIp != null  and srcIp != ''"> and ta.src_ip = #{srcIp}</if>
            <if test="procotol != null "> and ta.procotol = #{procotol}</if>
            <if test="srcPort != null "> and ta.src_port = #{srcPort}</if>
            <if test="destIp != null  and destIp != ''"> and ta.dest_ip = #{destIp}</if>
            <if test="destPort != null "> and ta.dest_port = #{destPort}</if>
            <if test="hitIntelligence != null "> and ta.hit_intelligence = #{hitIntelligence}</if>
            <if test="alarmLevel != null "> and ta.alarm_level = #{alarmLevel}</if>
            <if test="handleState != null and handleState != 99 and handleState != ''"> and ta.handle_state = #{handleState}</if>
            <if test="handleState == 99 "> and ta.handle_status is null</if>
            <if test="startTime != null and endTime != null "> and ta.update_time between #{startTime} and #{endTime}</if>
            <if test="id != null">and ta.ID = #{id}</if>
            <if test="dataSource != null">and ta.data_source = #{dataSource}</if>
            <if test="createTime != null">and DATE_FORMAT(ta.create_time,'%Y-%m-%d')=DATE_FORMAT(#{createTime},'%Y-%m-%d')</if>
            <if test="ipOr != null and ipOr != ''">
                and ( ta.src_ip = #{ipOr} or ta.dest_ip = #{ipOr})
            </if>
            <if test="attackSeg != null and attackSeg != ''">
                and tyd.attack_seg = #{attackSeg}
            </if>
            <if test="ipv4List != null and ipv4List.size()>0">
                and (
                ta.src_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                OR
                ta.dest_ip in
                <foreach collection="ipv4List" item="ipv4Item" separator="," open="(" close=")">
                    #{ipv4Item}
                </foreach>
                )
            </if>
            <if test="workOrderIdList != null and workOrderIdList.size()>0">
                and ta.work_order_id in
                <foreach collection="workOrderIdList" item="workOrderIdItem" open="(" close=")" separator=",">
                    #{workOrderIdItem}
                </foreach>
            </if>
            <if test="flowState != null and flowState == '99'">
                and ta.work_order_id is null
            </if>
        </where>
        ) t2 ON 1=1
    </select>

    <select id="selectThreatenTypeByThreatenType" resultType="java.lang.String">
        select attack_seg from threaten_typeseg_dict where threaten_type = #{threatenType}
    </select>
    <select id="selectListByGreaterThanId" resultType="com.ruoyi.threaten.domain.TblThreatenAlarm" resultMap="TblThreatenAlarmBaseResult">
        SELECT
            t1.id,t1.threaten_name,t1.alarm_level,t1.threaten_type,t1.create_time,t2.attack_seg type_seg_str
        FROM
            tbl_threaten_alarm t1
                LEFT JOIN threaten_typeseg_dict t2 ON t2.threaten_type=t1.threaten_type
        WHERE
            t1.handle_state != 1 and t1.alarm_level != 0 and t1.id > #{id}
    </select>

</mapper>
