package com.ruoyi.monitor2.service.impl;

import com.ruoyi.datainterface.domain.DataSrclog;
import com.ruoyi.datainterface.mapper.DataSrclogMapper;
import com.ruoyi.monitor2.domain.*;
import com.ruoyi.monitor2.mapper.*;
import com.ruoyi.monitor2.service.IMonitorBssVulnDealService;
import com.ruoyi.monitor2.service.IMonitorBssVulnInfoService;
import com.ruoyi.monitor2.service.IMonitorBssWebvulnDealService;
import com.ruoyi.monitor2.service.IXprocessResultService;
import com.ruoyi.safe.domain.TblNetworkIpMac;
import com.ruoyi.safe.mapper.TblNetworkIpMacMapper;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.domain.TblThreatenInfo;
import com.ruoyi.threaten.mapper.TblThreatenAlarmMapper;
import com.ruoyi.threaten.mapper.TblThreatenInfoMapper;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class XprocessResultServiceImpl implements IXprocessResultService {
    private Logger logger = LoggerFactory.getLogger(XprocessResultServiceImpl.class);

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    private IMonitorBssVulnDealService monitorBssVulnDealService;

    @Autowired
    private IMonitorBssVulnInfoService monitorBssVulnInfoService;

    @Autowired
    private IMonitorBssWebvulnDealService monitorBssWebvulnDealService;

    @Override
    public void dealVulnResult(List<MonitorBssVulnResult> bssVulnResultList) {
        for (int i = 0; i < bssVulnResultList.size(); i++) {
            MonitorBssVulnResult bssVulnResult = bssVulnResultList.get(i);
            MonitorBssVulnDeal bssVulnDeal = new MonitorBssVulnDeal();
            bssVulnDeal.setTitle(bssVulnResult.getTitle());
            bssVulnDeal.setHostIp(bssVulnResult.getHostIp());
            bssVulnDeal.setHostPort(bssVulnResult.getHostPort());
            bssVulnDeal.setProtocol(bssVulnResult.getProtocol());                 //  title, ip, port, procotol 为查询条件
            List<MonitorBssVulnDeal> monitorBssVulnDealList = monitorBssVulnDealService.selectMonitorBssVulnDealList(bssVulnDeal);
            if ((monitorBssVulnDealList == null)||(monitorBssVulnDealList.size() == 0)) {
                bssVulnDeal.setCategory(bssVulnResult.getCategory());
                bssVulnDeal.setDealStatus(MonitorBssVulnDeal.WAIT_DEAL);
                bssVulnDeal.setScanNum(1);
                bssVulnDeal.setCreateTime(bssVulnResult.getUpdateTime());
                bssVulnDeal.setUpdateTime(bssVulnResult.getUpdateTime());
                monitorBssVulnDealService.insertMonitorBssVulnDeal(bssVulnDeal);    // 没有记录插入
            } else {
                MonitorBssVulnDeal bssVulnDealUpdate = monitorBssVulnDealList.get(0);  // 应该只有一条。
                int scan_num = bssVulnDealUpdate.getScanNum();
                bssVulnDealUpdate.setScanNum(scan_num + 1);
                bssVulnDealUpdate.setUpdateTime(bssVulnResult.getUpdateTime());     // 存在记录更新扫描次数以及更新时间
                monitorBssVulnDealService.updateMonitorBssVulnDeal(bssVulnDealUpdate);
            }
        }
    }

    /**
     * 处理基础服务任务实例结果
     * @param bssHostResultList      主机结果列表
     * @param bssServiceResultList   服务结果列表
     * @param bssVulnResultList      漏洞结果列表
     * @param bssWebResultList       web结果列表
     * @param monitorBssProcess      更新实例结果信息
     * @param tblNetworkIpMacList    更新资产结果信息
     * @param bssVulnDealMap         漏洞处置map
     * @param bssWebvulnDealMap      Web漏洞处置map
     * @param bssVulnInfoMap         漏洞信息map
     * @return 入库成功 true  失败 false
     */
    @Override
    public boolean dealXprocessResult(List<MonitorBssHostResult> bssHostResultList, List<MonitorBssServiceResult> bssServiceResultList, List<MonitorBssVulnResult> bssVulnResultList, List<MonitorBssWebResult> bssWebResultList,
                                      MonitorBssProcess monitorBssProcess, List<TblNetworkIpMac> tblNetworkIpMacList, Map<String, MonitorBssVulnDeal> bssVulnDealMap, Map<String, MonitorBssWebvulnDeal> bssWebvulnDealMap, Map<String, MonitorBssVulnInfo> bssVulnInfoMap) {
        boolean ret = false;
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
        try {
            AtomicInteger count = new AtomicInteger(0);
            MonitorBssHostResultMapper bssHostResultMapper = sqlSession.getMapper(MonitorBssHostResultMapper.class);
            bssHostResultList.forEach(bssHostResult -> {
                bssHostResultMapper.insertMonitorBssHostResult(bssHostResult);
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            });

            MonitorBssServiceResultMapper bssServiceResultMapper = sqlSession.getMapper(MonitorBssServiceResultMapper.class);
            bssServiceResultList.forEach(bssServiceResult -> {
                bssServiceResultMapper.insertMonitorBssServiceResult(bssServiceResult);
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            });

            MonitorBssVulnResultMapper bssVulnResultMapper = sqlSession.getMapper(MonitorBssVulnResultMapper.class);
            bssVulnResultList.forEach(bssVulnResult -> {
                bssVulnResultMapper.insertMonitorBssVulnResult(bssVulnResult);
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            });

            if (bssWebResultList != null) {
                MonitorBssWebResultMapper bssWebResultMapper = sqlSession.getMapper(MonitorBssWebResultMapper.class);
                bssWebResultList.forEach(bssWebResult -> {
                    bssWebResultMapper.insertMonitorBssWebResult(bssWebResult);
                    count.getAndIncrement();
                    if (count.get() % 100 == 0) {
                        sqlSession.flushStatements();
                        sqlSession.clearCache();
                    }
                });
            }

            MonitorBssProcessMapper bssProcessMapper = sqlSession.getMapper(MonitorBssProcessMapper.class);
            bssProcessMapper.updateMonitorBssProcess(monitorBssProcess);

            MonitorBssVulnDealMapper bssVulnDealMapper = sqlSession.getMapper(MonitorBssVulnDealMapper.class);     // ip漏洞结果处置
            for (Map.Entry<String, MonitorBssVulnDeal> vulnDealEntry : bssVulnDealMap.entrySet()) {
                String searchKey = vulnDealEntry.getKey();
                MonitorBssVulnDeal bssVulnDeal = vulnDealEntry.getValue();
                MonitorBssVulnDeal temp = new MonitorBssVulnDeal();
                temp.setTitle(bssVulnDeal.getTitle());
                temp.setHostIp(bssVulnDeal.getHostIp());
                temp.setHostPort(bssVulnDeal.getHostPort());
                temp.setProtocol(bssVulnDeal.getProtocol());    //  title, ip, port, procotol 为查询条件
                temp.setDataSource(MonitorBssVulnDeal.SCAN_DATA);                  // 数据来源
                List<MonitorBssVulnDeal> monitorBssVulnDealList = monitorBssVulnDealService.getMonitorBssVulnDealList(temp);
                if ((monitorBssVulnDealList == null)||(monitorBssVulnDealList.size() == 0)) {
                    bssVulnDealMapper.insertMonitorBssVulnDeal(bssVulnDeal);
                } else {
                    MonitorBssVulnDeal bssVulnDealUpdate = monitorBssVulnDealList.get(0);  // 应该只有一条。
                    bssVulnDealUpdate.setScanNum(bssVulnDealUpdate.getScanNum() + bssVulnDeal.getScanNum());
                    bssVulnDealUpdate.setUpdateTime(bssVulnDeal.getUpdateTime());
                    bssVulnDealMapper.updateMonitorBssVulnDeal(bssVulnDealUpdate);
                }
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            }

            if (bssWebvulnDealMap != null) {     // web漏洞结果处置
                MonitorBssWebvulnDealMapper bssWebvulnDealMapper = sqlSession.getMapper(MonitorBssWebvulnDealMapper.class);
                for (Map.Entry<String, MonitorBssWebvulnDeal> webvulnDealEntry : bssWebvulnDealMap.entrySet()) {
                    MonitorBssWebvulnDeal bssWebvulnDeal = webvulnDealEntry.getValue();
                    List<MonitorBssWebvulnDeal> monitorBssWebvulnDealList = monitorBssWebvulnDealService.selectMonitorBssWebvulnDealList(bssWebvulnDeal);
                    if ((monitorBssWebvulnDealList == null)||(monitorBssWebvulnDealList.size() == 0)) {
                        bssWebvulnDealMapper.insertMonitorBssWebvulnDeal(bssWebvulnDeal);
                    } else {
                        MonitorBssWebvulnDeal monitorBssWebvulnDealUpdate = monitorBssWebvulnDealList.get(0);   // 应该只有一条
                        monitorBssWebvulnDealUpdate.setScanNum(monitorBssWebvulnDealUpdate.getScanNum() + bssWebvulnDeal.getScanNum());
                        monitorBssWebvulnDealUpdate.setUpdateTime(bssWebvulnDeal.getUpdateTime());
                        bssWebvulnDealMapper.updateMonitorBssWebvulnDeal(monitorBssWebvulnDealUpdate);
                    }
                    count.getAndIncrement();
                    if (count.get() % 100 == 0) {
                        sqlSession.flushStatements();
                        sqlSession.clearCache();
                    }
                }
            }

            MonitorBssVulnInfoMapper bssVulnInfoMapper = sqlSession.getMapper(MonitorBssVulnInfoMapper.class);
            for (Map.Entry<String, MonitorBssVulnInfo> vulnInfoEntry : bssVulnInfoMap.entrySet()) {
                String title = vulnInfoEntry.getKey();
                MonitorBssVulnInfo bssVulnInfo = vulnInfoEntry.getValue();
                MonitorBssVulnInfo temp = new MonitorBssVulnInfo();
                temp.setTitle(title);
                List<MonitorBssVulnInfo> monitorBssVulnInfoList = monitorBssVulnInfoService.getMonitorBssVulnInfoList(temp);
                if ((monitorBssVulnInfoList == null) || (monitorBssVulnInfoList.size() == 0)) {  // 应该只有一条。
                    bssVulnInfoMapper.insertMonitorBssVulnInfo(bssVulnInfo);
                }
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            }

            TblNetworkIpMacMapper networkIpMacMapper = sqlSession.getMapper(TblNetworkIpMacMapper.class);    // 此表为资产关联表， 多线程批量更新有可能产生锁表。
            for (int i = 0; i < tblNetworkIpMacList.size(); i++) {
                TblNetworkIpMac tblNetworkIpMac = tblNetworkIpMacList.get(i);
                networkIpMacMapper.updateScheduleInfo(tblNetworkIpMac);
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            }

            // 提交数据
            sqlSession.commit();
            //sqlSession.rollback();
            ret = true;
        } catch (Exception e) {
            sqlSession.rollback();
            e.printStackTrace();
            logger.error("基础服务任务实例结果入库出错: " + e.getMessage());
            throw e;
        } finally {
            sqlSession.close();
        }

        return ret;
    }

    @Override
    public boolean dealIpVuln(MonitorBssVulnDeal monitorBssVulnDeal, String destTableName, String dataSource, String originLog) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            MonitorBssVulnDealMapper monitorBssVulnDealMapper = sqlSession.getMapper(MonitorBssVulnDealMapper.class);
            MonitorBssVulnInfoMapper monitorBssVulnInfoMapper = sqlSession.getMapper(MonitorBssVulnInfoMapper.class);
            DataSrclogMapper dataSrclogMapper = sqlSession.getMapper(DataSrclogMapper.class);

            List<MonitorBssVulnDeal> monitorBssVulnDealList = monitorBssVulnDealMapper.getMonitorBssVulnDealList(monitorBssVulnDeal);
            if ((monitorBssVulnDealList == null)||(monitorBssVulnDealList.size() == 0)) {
                monitorBssVulnDealMapper.insertMonitorBssVulnDeal(monitorBssVulnDeal);    // 没有记录插入
            } else {
                MonitorBssVulnDeal bssVulnDealUpdate = monitorBssVulnDealList.get(0);  // 应该只有一条。
                int scan_num = bssVulnDealUpdate.getScanNum();
                bssVulnDealUpdate.setScanNum(scan_num + monitorBssVulnDeal.getScanNum());
                bssVulnDealUpdate.setUpdateTime(monitorBssVulnDeal.getUpdateTime());     // 存在记录更新扫描次数以及更新时间
                monitorBssVulnDealMapper.updateMonitorBssVulnDeal(bssVulnDealUpdate);
                monitorBssVulnDeal.setId(bssVulnDealUpdate.getId());
            }

            MonitorBssVulnInfo monitorBssVulnInfo = new MonitorBssVulnInfo();
            monitorBssVulnInfo.setTitle(monitorBssVulnDeal.getTitle());
            List<MonitorBssVulnInfo> monitorBssVulnInfoList = monitorBssVulnInfoMapper.getMonitorBssVulnInfoList(monitorBssVulnInfo);
            if ((monitorBssVulnInfoList == null)||(monitorBssVulnInfoList.size() == 0)) {
                monitorBssVulnInfo.setSeverity(monitorBssVulnDeal.getSeverity());
                monitorBssVulnInfoMapper.insertMonitorBssVulnInfo(monitorBssVulnInfo);
            } else {
                MonitorBssVulnInfo bssVulnInfoUpdate = monitorBssVulnInfoList.get(0);
                bssVulnInfoUpdate.setUpdateTime(monitorBssVulnDeal.getUpdateTime());
                monitorBssVulnInfoMapper.updateMonitorBssVulnInfo(bssVulnInfoUpdate);
            }

            // List<MonitorBssVulnDeal> tempList = monitorBssVulnDealMapper.selectMonitorBssVulnDealList(monitorBssVulnDeal);   // 获取monitorBssVulnDeal id, 对于sqlSession使用事物时， 插入记录并不会立即补充接入对像的数据库id, 而需要执行一段其它sql才会补充。
                                                                                                                             // 这个查询操作并无其它用处。。
            DataSrclog dataSrclog = new DataSrclog();
            dataSrclog.setBussinessid(monitorBssVulnDeal.getId());
            dataSrclog.setBussinessTable(destTableName);
            dataSrclog.setDataSource(dataSource);
            dataSrclog.setOriginData(originLog);
            dataSrclog.setCreateTime(new Date());
            dataSrclogMapper.insertDataSrclog(dataSrclog);

            // 提交数据
            sqlSession.commit();
            ret = true;
        } catch (Exception e) {
            sqlSession.rollback();
            e.printStackTrace();
            logger.error("IpVuln log 入库错误: " + e.getMessage());
            //throw e;
        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }

        return ret;
    }

    @Override
    public boolean dealWebVuln(MonitorBssWebvulnDeal monitorBssWebvulnDeal, String destTableName, String dataSource, String originLog) {
        boolean ret = false;
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
            MonitorBssWebvulnDealMapper monitorBssWebvulnDealMapper = sqlSession.getMapper(MonitorBssWebvulnDealMapper.class);
            MonitorBssVulnInfoMapper monitorBssVulnInfoMapper = sqlSession.getMapper(MonitorBssVulnInfoMapper.class);
            DataSrclogMapper dataSrclogMapper = sqlSession.getMapper(DataSrclogMapper.class);

            List<MonitorBssWebvulnDeal> monitorBssWebvulnDealList = monitorBssWebvulnDealMapper.getMonitorBssWebvulnDealList(monitorBssWebvulnDeal);
            if ((monitorBssWebvulnDealList == null)||(monitorBssWebvulnDealList.size() == 0)) {
                monitorBssWebvulnDealMapper.insertMonitorBssWebvulnDeal(monitorBssWebvulnDeal);    // 没有记录插入
            } else {
                MonitorBssWebvulnDeal bssWebvulnDealUpdate = monitorBssWebvulnDealList.get(0);  // 应该只有一条。
                int scan_num = bssWebvulnDealUpdate.getScanNum();
                bssWebvulnDealUpdate.setScanNum(scan_num + monitorBssWebvulnDeal.getScanNum());
                bssWebvulnDealUpdate.setUpdateTime(monitorBssWebvulnDeal.getUpdateTime());     // 存在记录更新扫描次数以及更新时间
                monitorBssWebvulnDealMapper.updateMonitorBssWebvulnDeal(bssWebvulnDealUpdate);
                monitorBssWebvulnDeal.setId(bssWebvulnDealUpdate.getId());
            }

            MonitorBssVulnInfo monitorBssVulnInfo = new MonitorBssVulnInfo();
            monitorBssVulnInfo.setTitle(monitorBssWebvulnDeal.getTitle());
            List<MonitorBssVulnInfo> monitorBssVulnInfoList = monitorBssVulnInfoMapper.getMonitorBssVulnInfoList(monitorBssVulnInfo);
            if ((monitorBssVulnInfoList == null)||(monitorBssVulnInfoList.size() == 0)) {
                monitorBssVulnInfo.setSeverity(monitorBssWebvulnDeal.getSeverity());
                monitorBssVulnInfoMapper.insertMonitorBssVulnInfo(monitorBssVulnInfo);
            } else {
                MonitorBssVulnInfo bssVulnInfoUpdate = monitorBssVulnInfoList.get(0);
                bssVulnInfoUpdate.setUpdateTime(monitorBssWebvulnDeal.getUpdateTime());
                monitorBssVulnInfoMapper.updateMonitorBssVulnInfo(bssVulnInfoUpdate);
            }

            //List<MonitorBssWebvulnDeal> tempList = monitorBssWebvulnDealMapper.selectMonitorBssWebvulnDealList(monitorBssWebvulnDeal);   // 获取monitorBssVulnDeal id
            DataSrclog dataSrclog = new DataSrclog();
            dataSrclog.setBussinessid(monitorBssWebvulnDeal.getId());
            dataSrclog.setBussinessTable(destTableName);
            dataSrclog.setDataSource(dataSource);
            dataSrclog.setOriginData(originLog);
            dataSrclog.setCreateTime(new Date());
            dataSrclogMapper.insertDataSrclog(dataSrclog);

            // 提交数据
            sqlSession.commit();
            ret = true;
        } catch (Exception e) {
            sqlSession.rollback();
            e.printStackTrace();
            logger.error("WebVuln log 入库错误: " + e.getMessage());
            //throw e;
        } finally {
            if (sqlSession != null) {
                sqlSession.close();
            }
        }

        return ret;
    }

}
