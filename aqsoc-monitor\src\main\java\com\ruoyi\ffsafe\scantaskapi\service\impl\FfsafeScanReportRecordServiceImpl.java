package com.ruoyi.ffsafe.scantaskapi.service.impl;

import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecord;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecordWithTaskInfo;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecordVO;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecordQueryParam;
import com.ruoyi.ffsafe.scantaskapi.service.IFfsafeScanReportRecordService;
import com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeScanReportRecordMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.exception.ServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 扫描报告记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Service
public class FfsafeScanReportRecordServiceImpl implements IFfsafeScanReportRecordService {
    
    private static final Logger log = LoggerFactory.getLogger(FfsafeScanReportRecordServiceImpl.class);

    @Autowired
    private FfsafeScanReportRecordMapper ffsafeScanReportRecordMapper;

    /**
     * 查询漏扫报告记录
     *
     * @param id 漏扫报告记录主键
     * @return 漏扫报告记录
     */
    @Override
    public FfsafeScanReportRecord selectFfsafeScanReportRecordById(Long id) {
        log.debug("查询漏扫报告记录，ID: {}", id);
        return ffsafeScanReportRecordMapper.selectFfsafeScanReportRecordById(id);
    }

    /**
     * 查询漏扫报告记录列表
     *
     * @param ffsafeScanReportRecord 漏扫报告记录
     * @return 漏扫报告记录集合
     */
    @Override
    public List<FfsafeScanReportRecord> selectFfsafeScanReportRecordList(FfsafeScanReportRecord ffsafeScanReportRecord) {
        log.debug("查询漏扫报告记录列表，查询条件: {}", ffsafeScanReportRecord);
        List<FfsafeScanReportRecord> list = ffsafeScanReportRecordMapper.selectFfsafeScanReportRecordList(ffsafeScanReportRecord);
        log.debug("查询完成，返回记录数: {}", list != null ? list.size() : 0);
        return list;
    }

    /**
     * 根据报表ID查询漏扫报告记录
     *
     * @param reportId 报表ID
     * @return 漏扫报告记录集合
     */
    @Override
    public List<FfsafeScanReportRecord> selectFfsafeScanReportRecordByReportId(Integer reportId) {
        log.debug("根据报表ID查询漏扫报告记录，reportId: {}", reportId);
        return ffsafeScanReportRecordMapper.selectFfsafeScanReportRecordByReportId(reportId);
    }

    /**
     * 根据生成入口查询漏扫报告记录列表
     *
     * @param generateSource 生成入口：1=单条生成，2=批量生成
     * @return 漏扫报告记录集合
     */
    @Override
    public List<FfsafeScanReportRecord> selectFfsafeScanReportRecordByGenerateSource(Integer generateSource) {
        log.debug("根据生成入口查询漏扫报告记录，generateSource: {}", generateSource);
        return ffsafeScanReportRecordMapper.selectFfsafeScanReportRecordByGenerateSource(generateSource);
    }

    /**
     * 查询扫描报告记录详细信息列表
     * 支持按报告类型和扫描目标进行筛选，返回包含扩展信息的详细数据
     *
     * @param queryParam 查询参数，包含报告类型、扫描目标等查询条件
     * @return 扫描报告记录详细信息集合，包含状态描述、类型描述、进度格式化等扩展字段
     */
    @Override
    public List<FfsafeScanReportRecordVO> selectFfsafeScanReportRecordDetailList(FfsafeScanReportRecordQueryParam queryParam) {
        try {
            log.debug("查询扫描报告记录详细信息，查询参数: {}", queryParam);

            // 调用Mapper查询数据
            List<FfsafeScanReportRecordVO> list = ffsafeScanReportRecordMapper.selectFfsafeScanReportRecordDetailList(queryParam);

            if (list != null && !list.isEmpty()) {
                log.debug("查询完成，返回记录数: {}", list.size());
            } else {
                log.debug("查询完成，无匹配记录");
            }

            return list;

        } catch (Exception e) {
            log.error("查询扫描报告记录详细信息时发生异常", e);
            throw new RuntimeException("查询扫描报告记录详细信息失败", e);
        }
    }

    /**
     * 新增漏扫报告记录
     *
     * @param ffsafeScanReportRecord 漏扫报告记录
     * @return 结果
     */
    @Override
    public int insertFfsafeScanReportRecord(FfsafeScanReportRecord ffsafeScanReportRecord) {
        try {
            log.info("新增漏扫报告记录，生成入口: {}, 报表类型: {}", 
                    ffsafeScanReportRecord.getGenerateSource(), 
                    ffsafeScanReportRecord.getReportType());
            
            // 设置创建时间
            if (ffsafeScanReportRecord.getCreateTime() == null) {
                ffsafeScanReportRecord.setCreateTime(new Date());
            }
            
            int result = ffsafeScanReportRecordMapper.insertFfsafeScanReportRecord(ffsafeScanReportRecord);
            
            if (result > 0) {
                log.info("新增漏扫报告记录成功，记录ID: {}", ffsafeScanReportRecord.getId());
            } else {
                log.warn("新增漏扫报告记录失败");
            }
            
            return result;
        } catch (Exception e) {
            log.error("新增漏扫报告记录时发生异常", e);
            throw new RuntimeException("新增漏扫报告记录失败", e);
        }
    }

    /**
     * 修改漏扫报告记录
     *
     * @param ffsafeScanReportRecord 漏扫报告记录
     * @return 结果
     */
    @Override
    public int updateFfsafeScanReportRecord(FfsafeScanReportRecord ffsafeScanReportRecord) {
        try {
            log.info("修改漏扫报告记录，记录ID: {}, 报表状态: {}", 
                    ffsafeScanReportRecord.getId(), 
                    ffsafeScanReportRecord.getReportStatus());
            
            // 如果报表状态变为生成完毕，记录生成时间
            if (FfsafeScanReportRecord.REPORT_STATUS_COMPLETED.equals(ffsafeScanReportRecord.getReportStatus()) 
                    && ffsafeScanReportRecord.getGenerateTime() == null) {
                ffsafeScanReportRecord.setGenerateTime(new Date());
                log.info("报表生成完毕，记录生成时间: {}", ffsafeScanReportRecord.getGenerateTime());
            }
            
            int result = ffsafeScanReportRecordMapper.updateFfsafeScanReportRecord(ffsafeScanReportRecord);
            
            if (result > 0) {
                log.info("修改漏扫报告记录成功");
            } else {
                log.warn("修改漏扫报告记录失败，可能记录不存在");
            }
            
            return result;
        } catch (Exception e) {
            log.error("修改漏扫报告记录时发生异常", e);
            throw new RuntimeException("修改漏扫报告记录失败", e);
        }
    }

    /**
     * 根据报表ID更新报告状态和进度
     *
     * @param ffsafeScanReportRecord 漏扫报告记录
     * @return 结果
     */
    @Override
    public int updateFfsafeScanReportRecordByReportId(FfsafeScanReportRecord ffsafeScanReportRecord) {
        try {
            log.info("根据报表ID更新报告状态，reportId: {}, 状态: {}, 进度: {}%", 
                    ffsafeScanReportRecord.getReportId(), 
                    ffsafeScanReportRecord.getReportStatus(),
                    ffsafeScanReportRecord.getReportPercent());
            
            // 如果报表状态变为生成完毕，记录生成时间
            if (FfsafeScanReportRecord.REPORT_STATUS_COMPLETED.equals(ffsafeScanReportRecord.getReportStatus()) 
                    && ffsafeScanReportRecord.getGenerateTime() == null) {
                ffsafeScanReportRecord.setGenerateTime(new Date());
                log.info("报表生成完毕，记录生成时间: {}", ffsafeScanReportRecord.getGenerateTime());
            }
            
            int result = ffsafeScanReportRecordMapper.updateFfsafeScanReportRecordByReportId(ffsafeScanReportRecord);
            
            if (result > 0) {
                log.info("根据报表ID更新报告状态成功，影响记录数: {}", result);
            } else {
                log.warn("根据报表ID更新报告状态失败，可能报表不存在");
            }
            
            return result;
        } catch (Exception e) {
            log.error("根据报表ID更新报告状态时发生异常", e);
            throw new RuntimeException("更新报告状态失败", e);
        }
    }

    /**
     * 删除漏扫报告记录信息
     *
     * @param id 漏扫报告记录主键
     * @return 结果
     */
    @Override
    public int deleteFfsafeScanReportRecordById(Long id) {
        log.info("删除漏扫报告记录，ID: {}", id);
        int result = ffsafeScanReportRecordMapper.deleteFfsafeScanReportRecordById(id);
        if (result > 0) {
            log.info("删除漏扫报告记录成功");
        } else {
            log.warn("删除漏扫报告记录失败，可能记录不存在");
        }
        return result;
    }

    /**
     * 批量删除漏扫报告记录
     *
     * @param ids 需要删除的漏扫报告记录主键集合
     * @return 结果
     */
    @Override
    public int deleteFfsafeScanReportRecordByIds(Long[] ids) {
        log.info("批量删除漏扫报告记录，数量: {}", ids != null ? ids.length : 0);
        int result = ffsafeScanReportRecordMapper.deleteFfsafeScanReportRecordByIds(ids);
        log.info("批量删除漏扫报告记录完成，删除记录数: {}", result);
        return result;
    }

}
