package com.ruoyi.ffsafe.scantaskapi.service.mapper;

import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecord;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecordWithTaskInfo;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecordVO;
import com.ruoyi.ffsafe.scantaskapi.domain.FfsafeScanReportRecordQueryParam;

import java.util.List;

/**
 * 扫描报告记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
public interface FfsafeScanReportRecordMapper {
    
    /**
     * 查询漏扫报告记录
     *
     * @param id 漏扫报告记录主键
     * @return 漏扫报告记录
     */
    public FfsafeScanReportRecord selectFfsafeScanReportRecordById(Long id);

    /**
     * 查询漏扫报告记录列表
     *
     * @param ffsafeScanReportRecord 漏扫报告记录
     * @return 漏扫报告记录集合
     */
    public List<FfsafeScanReportRecord> selectFfsafeScanReportRecordList(FfsafeScanReportRecord ffsafeScanReportRecord);

    /**
     * 根据报表ID查询漏扫报告记录
     *
     * @param reportId 报表ID
     * @return 漏扫报告记录集合
     */
    public List<FfsafeScanReportRecord> selectFfsafeScanReportRecordByReportId(Integer reportId);

    /**
     * 根据生成入口查询漏扫报告记录列表
     *
     * @param generateSource 生成入口：1=单条生成，2=批量生成
     * @return 漏扫报告记录集合
     */
    public List<FfsafeScanReportRecord> selectFfsafeScanReportRecordByGenerateSource(Integer generateSource);

    /**
     * 查询扫描报告记录详细信息列表
     * 支持按报告类型和扫描目标进行筛选，返回包含扩展信息的详细数据
     *
     * @param queryParam 查询参数，包含报告类型、扫描目标等查询条件
     * @return 扫描报告记录详细信息集合，包含状态描述、类型描述等扩展字段
     */
    public List<FfsafeScanReportRecordVO> selectFfsafeScanReportRecordDetailList(FfsafeScanReportRecordQueryParam queryParam);

    /**
     * 新增漏扫报告记录
     *
     * @param ffsafeScanReportRecord 漏扫报告记录
     * @return 结果
     */
    public int insertFfsafeScanReportRecord(FfsafeScanReportRecord ffsafeScanReportRecord);

    /**
     * 修改漏扫报告记录
     *
     * @param ffsafeScanReportRecord 漏扫报告记录
     * @return 结果
     */
    public int updateFfsafeScanReportRecord(FfsafeScanReportRecord ffsafeScanReportRecord);

    /**
     * 根据报表ID更新报告状态和进度
     *
     * @param ffsafeScanReportRecord 漏扫报告记录
     * @return 结果
     */
    public int updateFfsafeScanReportRecordByReportId(FfsafeScanReportRecord ffsafeScanReportRecord);

    /**
     * 删除漏扫报告记录
     *
     * @param id 漏扫报告记录主键
     * @return 结果
     */
    public int deleteFfsafeScanReportRecordById(Long id);

    /**
     * 批量删除漏扫报告记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFfsafeScanReportRecordByIds(Long[] ids);

}
