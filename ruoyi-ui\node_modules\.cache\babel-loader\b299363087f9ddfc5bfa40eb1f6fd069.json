{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasks.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\monitor\\ffJobTasks.vue", "mtime": 1755743179262}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_wpresult", "require", "_ffJobTasksDetail", "_interopRequireDefault", "name", "components", "FfJobTasksDetail", "props", "jobRow", "type", "Object", "default", "jobId", "String", "Number", "required", "jobType", "undefined", "data", "exploreJobId", "jobDetail", "jobTaskList", "queryParams", "pageNum", "pageSize", "total", "detailDialog", "processStatus", "processId", "loading", "exploreDisabled", "taskRow", "timer", "reportType", "created", "getList", "_this", "setInterval", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "_this2", "taskType", "listScantaskSummary", "then", "response", "rows", "catch", "createReport", "row", "_this3", "taskCreatereport", "downReport", "taskDownReport", "code", "window", "open", "msg", "taskDetail", "push"], "sources": ["src/views/frailty/monitor/ffJobTasks.vue"], "sourcesContent": ["<template>\n    <div style=\"padding: 0 15px\">\n      <div style=\"margin-bottom: 10px;\">\n        <h3 style=\"font-weight: bold\">扫描任务名称：{{ jobRow.jobName}}</h3>\n      </div>\n      <el-table height=\"100%\" v-loading=\"loading\" :data=\"jobTaskList\">\n        <el-table-column\n          label=\"任务名称\"\n        >\n          <template slot-scope=\"scope\">\n            <span> {{ jobRow.jobName }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"扫描目标\"\n        >\n          <template slot-scope=\"scope\">\n            <el-tooltip v-if=\"jobRow.jobType !== 3\" class=\"item\" placement=\"top\">\n              <div v-html=\"jobRow.ipOver\" slot=\"content\"></div>\n              <div class=\"oneLine\">\n                {{ jobRow.ipShow }}\n              </div>\n            </el-tooltip>\n            <span v-else>-</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"执行周期\"\n          prop=\"username\"\n          width=\"120\"\n        >\n          <template slot-scope=\"scope\">\n            <span> {{ jobRow.cronTransfer }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"最近扫描状态\"\n          prop=\"taskStatus\">\n          <template slot-scope=\"scope\">\n            <el-tag type=\"danger\" v-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 3\">任务异常</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 1\">扫描中</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 2\">扫描中</el-tag>\n            <el-tag type=\"success\" v-else-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 4\">已扫描</el-tag>\n            <el-tag type=\"danger\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 3\">任务异常</el-tag>\n            <el-tag type=\"danger\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 4\">任务终止</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 0\">扫描中</el-tag>\n            <el-tag v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 1\">扫描中</el-tag>\n            <el-tag type=\"success\" v-else-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 2\">已扫描</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"扫描进度\"\n          prop=\"finishRate\"\n          width=\"120\"\n        >\n          <template slot-scope=\"scope\">\n            <el-progress :text-inside=\"true\" :stroke-width=\"18\" :percentage=\"scope.row.finishRate\"></el-progress>\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"执行时间\"\n          prop=\"endTime\"\n          width=\"160\"\n        >\n          <template slot-scope=\"scope\">\n            {{ parseTime(scope.row.endTime, \"{y}-{m}-{d} {h}:{i}:{s}\") }}\n          </template>\n        </el-table-column>\n        <el-table-column\n          label=\"操作\"\n          width=\"160\"\n          fixed=\"right\"\n          class-name=\"small-padding fixed-width\"\n          :show-overflow-tooltip=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              @click=\"taskDetail(scope.row)\"\n            >详情\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.taskType === 1 && scope.row.taskStatus === 2 && scope.row.reportStatus === null\"\n              @click=\"createReport(scope.row)\"\n            >生成报告\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.taskType === 2 && scope.row.taskStatus === 4 && scope.row.reportStatus === null\"\n              @click=\"createReport(scope.row)\"\n            >生成报告\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.reportStatus !== null && scope.row.reportStatus !== 2\"\n            >报告生成中\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              v-if=\"scope.row.reportStatus === 2\"\n              @click=\"downReport(scope.row)\"\n            >下载报告\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"total>0\"\n        :total=\"total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"\n      />\n      <el-dialog title=\"任务详情\" :visible.sync=\"detailDialog\" width=\"70%\" append-to-body>\n        <ff-job-tasks-detail v-if=\"detailDialog\" :form=\"taskRow\" :job-row=\"jobRow\" />\n        <!--<detail-info v-if=\"detailDialog\" :host-ip=\"hostIp\" :detail-type=\"detailType\" :dept-name=\"deptName\" :is-asset=\"isAsset\" :current-asset-data=\"currentAssetData\" />-->\n      </el-dialog>\n    </div>\n</template>\n\n<script>\n  import { listScantaskSummary, taskCreatereport, taskDownReport } from '../../../api/monitor2/wpresult'\n  import FfJobTasksDetail from './ffJobTasksDetail'\n\n  export default {\n    name: 'ffJobTasks',\n    components: { FfJobTasksDetail },\n    props: {\n      jobRow: {\n        type: Object,\n        default: {}\n      },\n      jobId: {\n        type: [String,Number],\n        required: true,\n      },\n      jobType:{\n        type:Number,\n        default:undefined,\n      }\n    },\n    data() {\n      return {\n        //下拉框所选中的扫描实例id\n        exploreJobId: '',\n        //任务详细信息\n        jobDetail:{},\n        //任务列表\n        jobTaskList: [],\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n        },\n        // 总条数\n        total: 0,\n        detailDialog: false,\n        processStatus: undefined,\n        processId: '',\n        loading: false,\n        exploreDisabled: false,\n        taskRow: [],\n        timer: null,\n        reportType: ''\n        // jobType: 'tanHuo'\n      }\n    },\n    created() {\n      this.getList()\n      var _this = this;\n      this.timer = setInterval(() => {\n        _this.getList()\n      }, 10000);\n    },\n    beforeDestroy() {\n      // 清除定时器\n      if (this.timer) {\n        clearInterval(this.timer);\n      }\n    },\n    methods: {\n      getList() {\n        this.loading = true;\n        this.queryParams.taskType = this.jobType\n        this.queryParams.jobId = this.jobId\n        listScantaskSummary(this.queryParams).then(response => {\n          this.jobTaskList = response.rows\n          this.total = response.total;\n          this.loading = false;\n        }).catch(() => {\n          this.loading = false;\n        });\n      },\n      createReport(row) {\n        taskCreatereport(row).then(response => {\n          this.getList()\n        })\n      },\n      downReport(row) {\n        taskDownReport(row).then(response => {\n          if (response.code === 200) {\n            window.open(response.msg, \"_blank\")\n          }\n        })\n      },\n      taskDetail(row) {\n        this.taskRow = []\n        this.taskRow.push(row)\n        this.detailDialog = true\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .el-dialog__body {\n  padding: 0 20px 30px;\n  height: 80vh;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n</style>\n"], "mappings": ";;;;;;;;AA+HA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IAAAC,gBAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,GAAAI,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,OAAA;MACAP,IAAA,EAAAK,MAAA;MACAH,OAAA,EAAAM;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,YAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA;MACAC,KAAA;MACAC,YAAA;MACAC,aAAA,EAAAV,SAAA;MACAW,SAAA;MACAC,OAAA;MACAC,eAAA;MACAC,OAAA;MACAC,KAAA;MACAC,UAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,IAAAC,KAAA;IACA,KAAAJ,KAAA,GAAAK,WAAA;MACAD,KAAA,CAAAD,OAAA;IACA;EACA;EACAG,aAAA,WAAAA,cAAA;IACA;IACA,SAAAN,KAAA;MACAO,aAAA,MAAAP,KAAA;IACA;EACA;EACAQ,OAAA;IACAL,OAAA,WAAAA,QAAA;MAAA,IAAAM,MAAA;MACA,KAAAZ,OAAA;MACA,KAAAP,WAAA,CAAAoB,QAAA,QAAA1B,OAAA;MACA,KAAAM,WAAA,CAAAV,KAAA,QAAAA,KAAA;MACA,IAAA+B,6BAAA,OAAArB,WAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAApB,WAAA,GAAAwB,QAAA,CAAAC,IAAA;QACAL,MAAA,CAAAhB,KAAA,GAAAoB,QAAA,CAAApB,KAAA;QACAgB,MAAA,CAAAZ,OAAA;MACA,GAAAkB,KAAA;QACAN,MAAA,CAAAZ,OAAA;MACA;IACA;IACAmB,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,0BAAA,EAAAF,GAAA,EAAAL,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAAf,OAAA;MACA;IACA;IACAiB,UAAA,WAAAA,WAAAH,GAAA;MACA,IAAAI,wBAAA,EAAAJ,GAAA,EAAAL,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAS,IAAA;UACAC,MAAA,CAAAC,IAAA,CAAAX,QAAA,CAAAY,GAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAT,GAAA;MACA,KAAAlB,OAAA;MACA,KAAAA,OAAA,CAAA4B,IAAA,CAAAV,GAAA;MACA,KAAAvB,YAAA;IACA;EACA;AACA", "ignoreList": []}]}