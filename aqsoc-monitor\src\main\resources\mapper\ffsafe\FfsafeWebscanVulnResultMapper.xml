<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.scantaskapi.service.mapper.FfsafeWebscanVulnResultMapper">

    <resultMap type="FfsafeWebscanVulnResult" id="FfsafeWebscanVulnResultResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="vulnName"    column="vuln_name"    />
        <result property="vulnType"    column="vuln_type"    />
        <result property="impact"    column="impact"    />
        <result property="description"    column="description"    />
        <result property="recommendation"    column="recommendation"    />
        <result property="url"    column="url"    />
        <result property="param"    column="param"    />
        <result property="evidence"    column="evidence"    />
        <result property="severity"    column="severity"    />
        <result property="summaryId"    column="summary_id"    />
    </resultMap>

    <sql id="selectFfsafeWebscanVulnResultVo">
        select id, task_id, vuln_name, vuln_type, impact, description, recommendation, url, param, evidence, severity, summary_id from ffsafe_webscan_vulnresult
    </sql>

    <select id="selectFfsafeWebscanVulnResultList" parameterType="FfsafeWebscanVulnResult" resultMap="FfsafeWebscanVulnResultResult">
        <include refid="selectFfsafeWebscanVulnResultVo"/>
        <where>
            <if test="taskId != null"> and task_id = #{taskId}</if>
            <if test="vulnName != null  and vulnName != ''"> and vuln_name like concat('%', #{vulnName}, '%')</if>
            <if test="vulnType != null  and vulnType != ''"> and vuln_type = #{vulnType}</if>
            <if test="impact != null  and impact != ''"> and impact = #{impact}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="recommendation != null  and recommendation != ''"> and recommendation = #{recommendation}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="param != null  and param != ''"> and param = #{param}</if>
            <if test="evidence != null  and evidence != ''"> and evidence = #{evidence}</if>
            <if test="severity != null "> and severity = #{severity}</if>
        </where>
    </select>

    <select id="selectFfsafeWebscanVulnResultById" parameterType="Long" resultMap="FfsafeWebscanVulnResultResult">
        <include refid="selectFfsafeWebscanVulnResultVo"/>
        where id = #{id}
    </select>

    <select id="selectFfsafeWebscanVulnResultByIds" parameterType="Long" resultMap="FfsafeWebscanVulnResultResult">
        <include refid="selectFfsafeWebscanVulnResultVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertFfsafeWebscanVulnResult" parameterType="FfsafeWebscanVulnResult" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_webscan_vulnresult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="vulnName != null">vuln_name,</if>
            <if test="vulnType != null">vuln_type,</if>
            <if test="impact != null">impact,</if>
            <if test="description != null">description,</if>
            <if test="recommendation != null">recommendation,</if>
            <if test="url != null">url,</if>
            <if test="param != null">param,</if>
            <if test="evidence != null">evidence,</if>
            <if test="severity != null">severity,</if>
            <if test="summaryId != null">summary_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="vulnName != null">#{vulnName},</if>
            <if test="vulnType != null">#{vulnType},</if>
            <if test="impact != null">#{impact},</if>
            <if test="description != null">#{description},</if>
            <if test="recommendation != null">#{recommendation},</if>
            <if test="url != null">#{url},</if>
            <if test="param != null">#{param},</if>
            <if test="evidence != null">#{evidence},</if>
            <if test="severity != null">#{severity},</if>
            <if test="summaryId != null">#{summaryId},</if>
        </trim>
    </insert>

    <update id="updateFfsafeWebscanVulnResult" parameterType="FfsafeWebscanVulnResult">
        update ffsafe_webscan_vulnresult
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="vulnName != null">vuln_name = #{vulnName},</if>
            <if test="vulnType != null">vuln_type = #{vulnType},</if>
            <if test="impact != null">impact = #{impact},</if>
            <if test="description != null">description = #{description},</if>
            <if test="recommendation != null">recommendation = #{recommendation},</if>
            <if test="url != null">url = #{url},</if>
            <if test="param != null">param = #{param},</if>
            <if test="evidence != null">evidence = #{evidence},</if>
            <if test="severity != null">severity = #{severity},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeWebscanVulnResultById" parameterType="Long">
        delete from ffsafe_webscan_vulnresult where id = #{id}
    </delete>

    <delete id="deleteFfsafeWebscanVulnResultByIds" parameterType="String">
        delete from ffsafe_webscan_vulnresult where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getWebHighMiddleLevelStat" parameterType="Long" resultType="java.util.HashMap">
        select vuln_name as 'name', count(*) as 'value' from ffsafe_webscan_vulnresult
        where task_id = #{taskid} and severity > 2
        group by vuln_name
        order by vuln_name
    </select>
</mapper>