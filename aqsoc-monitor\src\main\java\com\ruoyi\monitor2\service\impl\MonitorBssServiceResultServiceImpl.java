package com.ruoyi.monitor2.service.impl;

import com.ruoyi.common.utils.CollectionUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.monitor2.domain.MonitorBssServiceResult;
import com.ruoyi.monitor2.mapper.MonitorBssServiceResultMapper;
import com.ruoyi.monitor2.service.IMonitorBssServiceResultService;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class MonitorBssServiceResultServiceImpl implements IMonitorBssServiceResultService {
    @Resource
    private SqlSessionFactory sqlSessionFactory;
    @Autowired
    private MonitorBssServiceResultMapper monitorBssServiceResultMapper;

    /**
     * 查询基础服务扫描service结果
     *
     * @param id 基础服务扫描service结果主键
     * @return 基础服务扫描service结果
     */
    @Override
    public MonitorBssServiceResult selectMonitorBssServiceResultById(Long id)
    {
        return monitorBssServiceResultMapper.selectMonitorBssServiceResultById(id);
    }

    /**
     * 批量查询基础服务扫描service结果
     *
     * @param ids 基础服务扫描service结果主键集合
     * @return 基础服务扫描service结果集合
     */
    @Override
    public List<MonitorBssServiceResult> selectMonitorBssServiceResultByIds(Long[] ids)
    {
        return monitorBssServiceResultMapper.selectMonitorBssServiceResultByIds(ids);
    }

    /**
     * 查询基础服务扫描service结果列表
     *
     * @param monitorBssServiceResult 基础服务扫描service结果
     * @return 基础服务扫描service结果
     */
    @Override
    public List<MonitorBssServiceResult> selectMonitorBssServiceResultList(MonitorBssServiceResult monitorBssServiceResult)
    {
        return monitorBssServiceResultMapper.selectMonitorBssServiceResultList(monitorBssServiceResult);
    }

    /**
     * 新增基础服务扫描service结果
     *
     * @param monitorBssServiceResult 基础服务扫描service结果
     * @return 结果
     */
    @Override
    public int insertMonitorBssServiceResult(MonitorBssServiceResult monitorBssServiceResult)
    {
        monitorBssServiceResult.setCreateTime(DateUtils.getNowDate());
        return monitorBssServiceResultMapper.insertMonitorBssServiceResult(monitorBssServiceResult);
    }

    /**
     * 新增基础服务扫描service结果列表
     *
     * @param monitorBssServiceResultList 基础服务扫描service结果列表
     * @return 结果
     */
    @Override
    public int insertMonitorBssServiceResultList(List<MonitorBssServiceResult> monitorBssServiceResultList)
    {
        int ret = 0;
        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
        try {
            MonitorBssServiceResultMapper bssServiceResultMapper = sqlSession.getMapper(MonitorBssServiceResultMapper.class);
            AtomicInteger count  = new AtomicInteger(0);
            monitorBssServiceResultList.forEach(bssServiceResult -> {
                bssServiceResultMapper.insertMonitorBssServiceResult(bssServiceResult);
                count.getAndIncrement();
                if (count.get() % 100 == 0) {
                    sqlSession.flushStatements();
                    sqlSession.clearCache();
                }
            });
            // 提交数据
            sqlSession.commit();
            //sqlSession.rollback();
            ret = monitorBssServiceResultList.size();
        } catch (Exception e) {
            sqlSession.rollback();
            throw e;
        } finally {
            sqlSession.close();
        }

        return ret;
    }

    /**
     * 修改基础服务扫描service结果
     *
     * @param monitorBssServiceResult 基础服务扫描service结果
     * @return 结果
     */
    @Override
    public int updateMonitorBssServiceResult(MonitorBssServiceResult monitorBssServiceResult)
    {
        monitorBssServiceResult.setUpdateTime(DateUtils.getNowDate());
        return monitorBssServiceResultMapper.updateMonitorBssServiceResult(monitorBssServiceResult);
    }

    /**
     * 删除基础服务扫描service结果信息
     *
     * @param id 基础服务扫描service结果主键
     * @return 结果
     */
    @Override
    public int deleteMonitorBssServiceResultById(Long id)
    {
        return monitorBssServiceResultMapper.deleteMonitorBssServiceResultById(id);
    }

    /**
     * 批量删除基础服务扫描service结果
     *
     * @param ids 需要删除的基础服务扫描service结果主键
     * @return 结果
     */
    @Override
    public int deleteMonitorBssServiceResultByIds(Long[] ids)
    {
        return monitorBssServiceResultMapper.deleteMonitorBssServiceResultByIds(ids);
    }

    @Override
    public List<MonitorBssServiceResult> findServerPage(MonitorBssServiceResult monitorBssServiceResult) {
        return monitorBssServiceResultMapper.findServerPage(monitorBssServiceResult);
    }

    @Override
    public MonitorBssServiceResult getServiceInfo(MonitorBssServiceResult monitorBssServiceResult) {
        List<MonitorBssServiceResult> list = monitorBssServiceResultMapper.selectMonitorBssServiceResultList(monitorBssServiceResult);
        return CollectionUtils.isEmpty(list)? null: list.get(0);
    }
}
